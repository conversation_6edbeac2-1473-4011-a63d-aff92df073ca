package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户关联机构
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_user_org")
public class UserOrg implements Serializable {

    private static final long serialVersionUID = 1L;
    private String userOid;
    private String orgOid;
    private String deptOid;

    /**
     * 0否 1是
     */
    private Integer choose = 0;

    public UserOrg(String userOid, String orgOid, String deptOid, int choose) {
        this.userOid = userOid;
        this.orgOid = orgOid;
        this.deptOid = deptOid;
        this.choose = choose;
    }
}
