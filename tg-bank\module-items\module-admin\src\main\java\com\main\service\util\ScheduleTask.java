package com.main.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.Global;
import com.main.exception.BusinessException;
import com.main.myenum.ActuatorSM4Utils;
import com.main.pojo.admin.Service;
import com.main.service.admin.ServiceService;
import com.main.util.CUtil;
import com.main.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@EnableScheduling
public class ScheduleTask {
    @Resource
    private ServiceService service;
    @Resource
    private AsyncProcesser processer;

    @Resource
    private RedisSrv redisSrv;

    /**
     * 应用服务接口监控
     */
//    @Scheduled(cron = "50/50 * * * * ?") //定时任务注解+cron表达式
    public void testScheduleTask() {
        String uuid = Global.createUUID();
//        log.info("执行服务状态监听-定时任务: UUID={}, Time={}", uuid, LocalDateTime.now());
        try {
            List<Service> services = service.listMonitorService();
            StringBuilder message = new StringBuilder();
            services.forEach(service -> {
                String path = service.getPath();
                String[] phones = new String[2];
                phones[0] = service.getNoticePhone();
                phones[1] = service.getNoticePhoneBak();
                message.setLength(0);
                message.append(service.getCloudName()).append("-").append(service.getServerName()).append("服务器-").append(service.getName());
                message.append("，");
                if (CUtil.isNotEmpty(path)) {
                    path = path + "/diy-actuator/health?auth=" + ActuatorSM4Utils.getAuth();
                    String resp = null;
                    log.error("定时请求：请求接口地址：{}", path);
                    try {
                        resp = HttpUtils.getUrl(path);
                        log.info("定时请求：请求接口返参：{}", resp);
                        if (resp != null) {

                            JSONObject components = null;
                            try {
                                JSONObject respObj = JSON.parseObject(resp);
                                assert respObj != null;
                                components = respObj.getJSONObject("components");
                            } catch (Exception ignored) {
                            }
                            if (components != null) {
                                try {
                                    JSONObject db = components.getJSONObject("db");
                                    JSONObject diskSpace = components.getJSONObject("diskSpace");
                                    JSONObject redis = components.getJSONObject("redis");
                                    double percent = 0;
                                    if (diskSpace != null && diskSpace.getJSONObject("details") != null) {
                                        JSONObject details = diskSpace.getJSONObject("details");
                                        long total = details.getLong("total");
                                        long free = details.getLong("free");
                                        percent = (double) free / total * 100;
                                    }
                                    //如果之前是正常的，那么现在只要找不正常的就可以了。
                                    boolean isUpdateStatus = false;
                                    if (service.getIsStop() == 0) {
                                        if (db != null && "DOWN".equals(db.getString("status"))) {
                                            isUpdateStatus = true;
                                            message.append("db 疑似挂了;");
                                        }
                                        if (percent >= 96) {
                                            isUpdateStatus = true;
                                            message.append("磁盘已使用超过96%;");
                                        }
                                        if (redis != null && "DOWN".equals(redis.getString("status"))) {
                                            isUpdateStatus = true;
                                            message.append("REDIS 疑似挂了;");
                                        }
                                        if (isUpdateStatus) {
                                            processer.updateServiceStatus(service.getOid(), 1, message.toString(), phones);
                                        }
                                    } else if (service.getIsStop() == 1) {
                                        //如果之前是不正常的，现在看看是否都正常了。
                                        isUpdateStatus = true;
                                        if (db != null && "UP".equals(db.getString("status"))) {
                                            message.append("db 正常;");
                                        } else if (db != null && "DOWN".equals(db.getString("status"))) {
                                            isUpdateStatus = false;
                                            message.append("db 疑似挂了;");
                                        }
                                        if (percent < 96) {
                                            message.append("磁盘空间正常；");
                                        } else {
                                            isUpdateStatus = false;
                                        }
                                        if (redis != null && "UP".equals(redis.getString("status"))) {
                                            message.append("REDIS 服务正常；");
                                        } else if (redis != null && "DOWN".equals(redis.getString("status"))) {
                                            isUpdateStatus = false;
                                        }
                                        if (isUpdateStatus) {
                                            message.append("服务已经恢复。");
                                            processer.updateServiceStatus(service.getOid(), 0, message.toString(), phones);
                                        }
                                    }
                                } catch (Exception e) {
                                    //无需处理
                                    CUtil.getStackTraceString(e);
                                }
                            } else {
                                //如果之前是停止服务的，现在给他状态恢复正常
                                if (service.getIsStop() == 1) {
                                    message.append("服务已经恢复。");
                                    processer.updateServiceStatus(service.getOid(), 0, message.toString(), phones);
                                }
                            }
                        } else {
                            throw new BusinessException("接口响应失败，获取响应内容为空！");
                        }
                    } catch (Exception e) {
                        CUtil.getStackTraceString(e);
                        log.error("接口请求失败了！{}", e.getMessage());
                        if (service.getIsStop() == 0) {
                            //连续失败两次再告警
                            String failTimes = redisSrv.getParam("SRV_LSN_FAIL:" + service.getOid());
                            int failTimesNum = Integer.parseInt(CUtil.nullToZero(failTimes));
                            failTimesNum = failTimesNum + 1;
                            if (failTimesNum > 1) {
                                redisSrv.delete("SRV_LSN_FAIL:" + service.getOid());
                                message.append("服务请求失败，请检查一下服务是否正常！");
                                processer.updateServiceStatus(service.getOid(), 1, message.toString(), phones);
                            } else {
                                redisSrv.setParam("SRV_LSN_FAIL:" + service.getOid(), String.valueOf(failTimesNum), 3, TimeUnit.MINUTES);
                            }
                        }
                    }
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
//        log.info("执行服务状态监听-定时任务结束: UUID={}", uuid);
    }
}
