package com.frame;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * 2020/6/19 9:51
 */
@Configuration
public class CorsConfig {
	@Value("${sys.cros:0}")
	private int cros;

	@Bean
	public CorsFilter corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		if(cros == 1) {
			source.registerCorsConfiguration("/**", buildConfig());
		}
		return new CorsFilter(source);
	}

	private CorsConfiguration buildConfig() {
		CorsConfiguration corsConfiguration = new CorsConfiguration();

		corsConfiguration.setAllowCredentials(true);
		// 1允许任何域名使用
		corsConfiguration.addAllowedOriginPattern("*");
		// 2允许任何头
		corsConfiguration.addAllowedHeader("*");
		// 3允许任何方法（post、get等）
		corsConfiguration.addAllowedMethod("*");

		corsConfiguration.setMaxAge(1800L);
		return corsConfiguration;
	}
}
