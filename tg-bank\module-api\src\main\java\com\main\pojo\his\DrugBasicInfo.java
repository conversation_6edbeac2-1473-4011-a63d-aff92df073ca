package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 药品基本信息实体类
 * <AUTHOR>
 */
@Data
@TableName("his_drug_basic_info")
public class DrugBasicInfo {
    /**
     * 诊所ID，联合主键
     */
    private String companyId;
    
    /**
     * 库房ID，联合主键
     */
    private String warehouseId;
    
    /**
     * 药品ID，联合主键
     */
    @TableId
    private String oid;

    /**
     * 停用标志 0 否 1 是
     */
    private int enable;

    /**
     * 药品名称
     */
    private String name;
    
    /**
     * 药品编码
     */
    private String code;
    
    /**
     * 通用名
     */
    private String commonName;
    
    /**
     * 商品名
     */
    private String tradeName;
    
    /**
     * 助记码
     */
    private String zjm;
    
    /**
     * 药品类型
     */
    private String type;
    
    /**
     * 药品分类
     */
    private String category;
    
    /**
     * 药理分类
     */
    private String pharmacologyCategory;
    
    /**
     * 规格
     */
    private String specification;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 生产厂家
     */
    private String manufacturer;
    
    /**
     * 批准文号
     */
    private String approvalNumber;
    
    /**
     * 条形码
     */
    private String barcode;
    
    /**
     * 产品编码
     */
    private String productCode;
    
    /**
     * 剂型
     */
    private String dosageForm;
    
    /**
     * 剂量
     */
    private Double dosage;
    
    /**
     * 剂量单位
     */
    private String dosageUnit;
    
    /**
     * 最小包装数量
     */
    private Integer minPackageQuantity;
    
    /**
     * 最小包装单位
     */
    private String minPackageUnit;
    
    /**
     * 包装单位
     */
    private String packageUnit;
    
    /**
     * 医保类型
     */
    private String insuranceType;
    
    /**
     * 医保编号
     */
    private String insuranceNo;
    
    /**
     * 医保代码
     */
    private String insuranceCode;
    
    /**
     * 结算优先级
     */
    private String settlementPriority;
    /** 是否贵重器械 */
    private Boolean isValuableEquipment;
    /** 产地类型（进口/国产）*/
    private String originType;

    public void calcSpecification() {
        if(this.dosage == null) {
            this.specification = this.minPackageQuantity + this.minPackageUnit + "/" + this.packageUnit;
        } else {
            this.specification = this.dosage + "*" + this.dosageUnit + "*" + this.minPackageQuantity + this.minPackageUnit + "/" + this.packageUnit;
        }
    }
} 