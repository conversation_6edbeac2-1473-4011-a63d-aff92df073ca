<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="2024-01-message-01" author="claude">
        <!-- 消息主表 -->
        <createTable tableName="d_message" remarks="消息主表">
            <column name="oid" type="varchar(32)" remarks="主键ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="title" type="varchar(200)" remarks="消息标题">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="mediumtext" remarks="消息内容"/>
            <column name="type" type="varchar(20)" remarks="消息类型(SYSTEM-系统消息,PERSONAL-公告通知,CHAT-聊天消息)">
                <constraints nullable="false"/>
            </column>
            <column name="receive_scope" type="varchar(20)" remarks="接收范围(ALL-全部人员,ORG-机构,USER-指定用户)">
                <constraints nullable="false"/>
            </column>
            <column name="receive_target" type="varchar(500)" remarks="接收对象ID(机构ID/用户ID列表,多个用逗号分隔)"/>
            <column name="sender_id" type="varchar(32)" remarks="发送人ID"/>
            <column name="sender_name" type="varchar(50)" remarks="发送人姓名"/>
            <column name="org_id" type="varchar(32)" remarks="发送人所属机构ID"/>
            <column name="create_time" type="datetime" remarks="创建时间"/>
            <column name="del_flag" type="int" defaultValue="0" remarks="删除标记(0-正常,1-删除)"/>
        </createTable>

        <!-- 消息接收表(只记录已读记录) -->
        <createTable tableName="d_message_receiver" remarks="消息接收表(已读记录)">
            <column name="oid" type="varchar(32)" remarks="主键ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="message_id" type="varchar(32)" remarks="消息ID">
                <constraints nullable="false"/>
            </column>
            <column name="receiver_id" type="varchar(32)" remarks="接收人ID">
                <constraints nullable="false"/>
            </column>
            <column name="read_time" type="datetime" remarks="阅读时间"/>
            <column name="create_time" type="datetime" remarks="创建时间"/>
        </createTable>

        <!-- 添加索引 -->
        <createIndex tableName="d_message" indexName="idx_message_type">
            <column name="type"/>
        </createIndex>
        <createIndex tableName="d_message" indexName="idx_message_scope">
            <column name="receive_scope"/>
        </createIndex>
        <createIndex tableName="d_message" indexName="idx_message_sender">
            <column name="sender_id"/>
        </createIndex>
        <createIndex tableName="d_message_receiver" indexName="idx_message_receiver_message">
            <column name="message_id"/>
        </createIndex>
        <createIndex tableName="d_message_receiver" indexName="idx_message_receiver_user">
            <column name="receiver_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="system" id="d_message_upt_241213">
        <addColumn tableName="d_message">
            <column name="sender_chat_sex" type="int" defaultValue="0" remarks="发送者性别" />
            <column name="receive_target_chat_sex" type="int" defaultValue="0" remarks="接受者性别" />
            <column name="is_image" type="int" defaultValue="0" remarks="是否图片" />
            <column name="sender_chat_phone" type="varchar(11)" remarks="发送者手机号" />
            <column name="receive_target_chat_phone" type="varchar(11)" remarks="接受者手机号" />
            <column name="quote_oid" type="varchar(32)" remarks="引用消息id" />
            <column name="quote_content" type="mediumtext" remarks="引用内容" />
            <column name="quote_timestamp" type="datetime" remarks="引用内容时间" />
        </addColumn>

        <createIndex tableName="d_message" indexName="idx_message_receive">
            <column name="receive_target"/>
        </createIndex>
    </changeSet>
    <changeSet author="system" id="d_message_upt_241216">
        <addColumn tableName="d_message">
            <column name="chat_read_status" type="int" defaultValue="0" remarks="针对聊天消息的已读标志 0未读 1已读，通知公告已读未读看d_message_receive" />
        </addColumn>
    </changeSet>
</databaseChangeLog> 