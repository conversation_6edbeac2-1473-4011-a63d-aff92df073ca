package com.main.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.exception.BusinessException;
import com.main.mapper.admin.UserOrgMapper;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.Dept;
import com.main.pojo.admin.Org;
import com.main.pojo.admin.UserOrg;
import com.main.util.CUtil;
import com.main.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <p>
 * 用户关联机构 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
@Slf4j
public class UserOrgServiceImpl extends ServiceImpl<UserOrgMapper, UserOrg> implements UserOrgService {
    @Resource
    private UserOrgMapper userOrgMapper;

    @Override
    public boolean mySave(String userOid, JSONArray orgList) {
        //删除 后新增
        if (orgList != null && orgList.size() > 0) {
            this.delete(userOid);
            List<UserOrg> userOrgList = new ArrayList<>();
            for (Object org : orgList) {
                String pageId = String.valueOf(org);
                String[] pageIds = pageId.split("-");
                UserOrg userOrg = new UserOrg(userOid, pageIds[0], pageIds[1], 0);
                userOrgList.add(userOrg);
            }
            return this.saveBatch(userOrgList);
        } else {
            log.error("用户机构关联保存失败：传递的机构信息为空！orgs={}", JSON.toJSONString(orgList));
            return false;
        }
    }

    @Override
    public boolean delete(String userOid) {
        LambdaQueryWrapper<UserOrg> queryWrapper = new LambdaQueryWrapper<>();
        if (userOid.contains(",")) {
            String[] oidArray = CUtil.splitArray(userOid);
            Assert.assertNotNull("当前数据为空，请勾选对应的数据进行删除！", oidArray);
            queryWrapper.in(UserOrg::getUserOid, new ArrayList<>(Arrays.asList(oidArray)));
        } else {
            queryWrapper.eq(UserOrg::getUserOid, userOid);
        }
        return this.remove(queryWrapper);
    }

    @Override
    public List<Org> getUserOrgList(String oid) {
        List<Org> orgList = userOrgMapper.getUserOrgList(oid);
        List<Org> finalList = new ArrayList<>();
        List<Dept> deptList = userOrgMapper.getUserDeptList(oid);
        Map<String, List<Dept>> orgDeptInfoMap = new HashMap<>();
        deptList.forEach(dept -> {
            List<Dept> temp = orgDeptInfoMap.get(dept.getOrgOid());
            if(temp == null || temp.isEmpty()) {
                temp = new ArrayList<>();
            }
            temp.add(dept);
            orgDeptInfoMap.put(dept.getOrgOid(),temp);
        });

        AtomicBoolean oneOrgPayed = new AtomicBoolean(false);
        orgList.forEach(org -> {
            //判断是否过期了
            oneOrgPayed.set(oneOrgPayed.get() || this.validateOrg(org));
            org.setDepts(orgDeptInfoMap.get(org.getOid()));
            if(this.validateOrg(org)) {
                finalList.add(org);
            }
        });
        if(oneOrgPayed.get()) {
            //只要有一个机构不欠费，那么就可以继续登录。
            return finalList;
        } else {
            //机构都欠费了，就不要登录了吧。
            //throw new BusinessException(HandlerType.ORG_NO_PAY);
            return finalList;
        }
    }

    /**
     * 校验机构是不是过期了
     *
     * @param org
     * @return
     */
    @Override
    public boolean validateOrg(Org org) {
        long now = Long.parseLong(DateUtil.getDate());
        long endDate = Long.parseLong(org.getEndDate().replace("-",""));
        return endDate - now >= 0;
    }

    /** 设置默认登录机构 */
    @Transactional
    @Override
    public boolean setDefaultLoginOrg(String orgOid, String userOid) {
        // 断言机构主键不为空，如果为空则抛出异常
        Assert.assertNotNull("机构主键不能为空",orgOid);
        // 断言用户主键不为空，如果为空则抛出异常
        Assert.assertNotNull("用户主键不能为空",userOid);
        // 创建一个LambdaUpdateWrapper对象用于重置用户的所有机构的选中状态为0（未选中）
        LambdaUpdateWrapper<UserOrg> updateReset = new LambdaUpdateWrapper<>();
        // 设置选中状态为0
        updateReset.set(UserOrg::getChoose, 0);
        // 指定要更新的是用户主键为userOid的记录
        updateReset.eq(UserOrg::getUserOid, userOid);
        // 执行更新操作
        this.update(updateReset);

        // 创建一个LambdaUpdateWrapper对象用于将指定机构的选中状态设置为1（选中）
        LambdaUpdateWrapper<UserOrg> update = new LambdaUpdateWrapper<>();
        // 设置选中状态为1
        update.set(UserOrg::getChoose, 1);
        // 指定要更新的是用户主键为userOid的记录
        update.eq(UserOrg::getUserOid, userOid);
        // 指定要更新的是机构主键为orgOid的记录
        update.eq(UserOrg::getOrgOid, orgOid);
        // 执行更新操作并返回更新结果
        return this.update(update);
    }
}
