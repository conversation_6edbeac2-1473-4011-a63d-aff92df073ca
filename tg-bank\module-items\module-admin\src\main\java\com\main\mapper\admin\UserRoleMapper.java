package com.main.mapper.admin;

import com.main.pojo.admin.Role;
import com.main.pojo.admin.UserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserRoleMapper extends BaseMapper<UserRole> {

    List<Role> getUserRoleList(@Param("oid") String userOid);
}
