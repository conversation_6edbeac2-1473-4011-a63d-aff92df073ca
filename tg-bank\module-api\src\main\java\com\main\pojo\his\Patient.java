package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 患者信息表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("his_patient")
public class Patient implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String oid;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 性别 男 女 未知
     */
    private String gender;

    /**
     * 年龄
     */
    private Integer ageYears;

    /**
     * 年龄
     */
    private Integer ageMonths;

    /**
     * 年龄
     */
    private Integer ageDays;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 来源
     */
    private String source;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 体重
     */
    private BigDecimal weight;

    /**
     * 是否是vip
     */
    private int isVip;
    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 文件编号
     */
    private String fileNo;

    /**
     * 医疗历史
     */
    private String medicalHistory;

    /**
     * 过敏历史
     */
    private String allergyHistory;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 访问原因
     */
    private String visitReason;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 所属机构ID
     */
    private String orgId;
} 