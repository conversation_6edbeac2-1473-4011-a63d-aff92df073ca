package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 受监控的云资源基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_cloud")
public class Cloud implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId
    private String oid;

    /**
     * 云资源名称：鲲鹏云
     */
    private String name;

    /**
     * 是否停用
     */
    private Integer isStop;


}
