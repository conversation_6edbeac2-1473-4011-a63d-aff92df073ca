package com.main.handler.admin;

import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.User;
import com.main.service.admin.MessageService;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 消息管理接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/message")
public class MessageHandler {
    
    @Resource
    private MessageService messageService;
    
    /**
     * 发送消息
     * 请求参数示例:
     * {
     *   "title": "消息标题",
     *   "content": "消息内容",
     *   "type": "SYSTEM",
     *   "receiveScope": "ALL/ORG/USER",
     *   "receiveTarget": "机构ID/用户ID列表(逗号分隔)"
     * }
     */
    @PostMapping("/send")
    @MyAuthority(auth="/notification/index")
    public ResponseVO<String> sendMessage(@RequestBody JSONObject params, @Login User user) {
        Message message = params.toJavaObject(Message.class);
        return messageService.sendMessage(message, user);
    }
    
    /**
     * 消息列表
     */
    @PostMapping("/page/list")
    public ResponseVO<Map<String, Object>> pageList(@RequestBody JSONObject params, @Login User user) {
        int pageNum = params.getIntValue("current");
        int pageSize = params.getIntValue("pageSize");
        String type = params.getString("type"); //前端传递的类型非后端类型 all 全部通知 system 系统通知 unread 未读通知
        Integer readStatus = params.getInteger("readStatus");
        if(CUtil.isEmpty(type)) {
            type = "all";
        }
        if("unread".equals(type)) {
            type = "all";
            readStatus = new Integer(0); //标记只要未读的消息
        }
        String keyword = params.getString("keyword");
        String userOid = params.getString("userOid");//获取与谁的聊天记录，仅针对聊天信息有用

        return messageService.pageMessages(pageNum, pageSize, type.toUpperCase(), keyword, readStatus, userOid, user);
    }
    
    /**
     * 标记消息已读
     */
    @PostMapping("/read")
    public ResponseVO<String> markAsRead(@RequestBody JSONObject params, @Login User user) {
        List<String> messageIds = params.getJSONArray("messageIds").toJavaList(String.class);
        return messageService.markAsRead(messageIds, user);
    }
    
    /**
     * 一键已读
     */
    @PostMapping("/read/all")
    public ResponseVO<String> markAllAsRead(@RequestBody JSONObject params, @Login User user) {
        String type = params.getString("type");
        String userOid = params.getString("userOid");
        return messageService.markAllAsRead(type, userOid, user);
    }
    
    /**
     * 获取未读消息数量
     */
    @PostMapping("/unread/count")
    public ResponseVO<Map<String, Integer>> getUnreadCount(@Login User user) {
        return messageService.getUnreadCount(user);
    }

    /**
     * 删除聊天内容
     * @param user 登录对象
     * @return
     */
    @LogAnnotation(option = "删除聊天内容")
    @PostMapping("/del/chat")
    public ResponseVO<Map<String, Integer>> delChatMessage(@Login User user, @RequestBody JSONObject params) {
        String userOid = params.getString("userOid");
        return messageService.deleteChatMessage(user, userOid);
    }
} 