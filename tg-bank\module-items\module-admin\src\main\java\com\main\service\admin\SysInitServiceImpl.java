package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.common.Global;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.*;
import com.main.service.inf.InitInfoYamlSrv;
import com.main.util.CUtil;
import com.main.vo.InitReqVO;
import com.util.JsonUtil;
import com.util.YamlUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Transactional(rollbackFor = {RuntimeException.class, Exception.class})
public class SysInitServiceImpl implements SysInitService {

    @Resource
    private OrgService orgService;

    @Resource
    private UserService userService;

    @Resource
    private DeptService deptService;

    @Resource
    private RoleService roleService;

    @Resource
    private UserOrgService userOrgService;

    @Resource
    private InitInfoYamlSrv initInfoYamlSrv;

    @Resource
    private ResService resService;

    @Override
    public ResponseVO<String> init(InitReqVO initReqVO) {
        ResponseVO<String> response = new ResponseVO<String>();
        // 1 save org
        Org org = new Org(initReqVO.getOrgName(), initReqVO.getOrgShortName());
        // 个人注册默认机构
        Org org_gth = new Org("个体户", "个体户");
        org_gth.setOid("1");
        orgService.saveOrg(org);
        orgService.saveOrg(org_gth);

        Dept dept = new Dept();
        dept.setParentId(null);
        dept.setIsStop(0);
        dept.setDeptName("默认部门");
        dept.setOrgOid(org.getOid());
        dept.setIsLeaf(1);
        deptService.saveDept(dept);
        // 个人注册默认部门
        Dept dept_gth = new Dept();
        dept.setParentId(null);
        dept.setIsStop(0);
        dept.setDeptName("个体户");
        dept.setOrgOid("1");
        dept.setIsLeaf(1);
        deptService.saveDept(dept);

        Role role = new Role();
        role.setOid("1");
        role.setName("个体户");
        role.setIsStop(0);
        role.setCreateUserOid("system");
        role.setCreateUserName("system");
        roleService.save(role);

        //2 save user

        User user = new User(initReqVO.getUserBh(), initReqVO.getUserName(), "18650093759", initReqVO.getPword());
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getObh, user.getObh());
        User db = userService.getOne(userQueryWrapper);
        if (db != null) {
            user.setOid(db.getOid());
        }
        user.setIsAdmin(1);
        userService.saveOrUpdate(user);

        //3 save user-org relationship
        UserOrg userOrg = new UserOrg(user.getOid(), org.getOid(), dept.getOid(), 1);
        LambdaQueryWrapper<UserOrg> userOrgQueryWrapper = new LambdaQueryWrapper<>();
        userOrgQueryWrapper.eq(UserOrg::getUserOid, user.getOid());
        userOrgQueryWrapper.eq(UserOrg::getOrgOid, org.getOid());
        UserOrg db4UserOrg = userOrgService.getOne(userOrgQueryWrapper);
        if (db4UserOrg != null) {
            LambdaUpdateWrapper<UserOrg> userOrgUpdateWrapper = new LambdaUpdateWrapper<UserOrg>();
            userOrgUpdateWrapper.set(UserOrg::getChoose, userOrg.getChoose());
            userOrgUpdateWrapper.eq(UserOrg::getUserOid, user.getOid());
            userOrgUpdateWrapper.eq(UserOrg::getOrgOid, org.getOid());
            userOrgService.update(userOrgUpdateWrapper);
        } else {
            userOrgService.save(userOrg);
        }

        //4 init data
        this.initData();

        //5 close init config
        initInfoYamlSrv.setParam("initFlag", "1");
        initInfoYamlSrv.setParam("password", "");
        return response;
    }

    //TODO 待完善
    private void initData() {
        String fileOfMenuDef = JsonUtil.readJsonFile(YamlUtil.getClassResources() + "/config/init_data/menu/fileList.json");
        JSONArray files = JSONObject.parseArray(fileOfMenuDef);
        List<Res> resList = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (Object file : files) {
            String jsonRes = JsonUtil.readJsonFile(YamlUtil.getClassResources() + "/config/init_data/menu/" + file);
            if (CUtil.isNotEmpty(jsonRes)) {
                JSONArray menus = JSONObject.parseArray(jsonRes);
                AtomicInteger i = new AtomicInteger(1);
                menus.forEach(menu -> {
                    Res res = JSONObject.parseObject(JSONObject.toJSONString(menu), Res.class);
                    ids.add(res.getOid());
                    res.setSort(1000* i.get());
                    i.getAndIncrement();
                    resList.add(res);
                });
            }
        }
        resService.removeByIds(ids);
        resService.saveBatch(resList);
    }

    private void getChildren(Res resParent, List<Res> resList, List<Res> menus) {
        AtomicInteger i = new AtomicInteger();
        menus.forEach(res -> {
            res.setOid(Global.createUUID());
            res.setParentId(resParent.getOid());
            res.setSort(resParent.getSort()+ i.get());
            i.getAndIncrement();
            List<Res> children = res.getChildren();
            if (children != null && children.size() > 0) {
                getChildren(res, resList, menus);
                res.setIsLeaf(0);
            }else {
                res.setIsLeaf(1);
            }
            resList.add(res);
        });
    }
}
