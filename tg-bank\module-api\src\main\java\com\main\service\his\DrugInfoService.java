package com.main.service.his;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.Login;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.pojo.his.DrugBasicInfo;
import com.main.pojo.his.DrugExtendedInfo;
import com.main.pojo.his.DrugInventoryInfo;
import com.main.pojo.his.DrugPricingInfo;
import com.main.pojo.his.vo.DrugInfoVO;

import java.util.Map;

/**
 * 药品信息服务接口
 * <AUTHOR>
 */
public interface DrugInfoService {
    /**
     * 分页查询药品信息列表
     * @param user 当前登录用户
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 搜索关键字（支持药品名称、编码、通用名、商品名、助记码模糊搜索）
     * @param warehouseId 库房ID
     * @return 药品信息分页列表
     */
    ResponseVO<Map<String, Object>> pageList(@Login User user, Integer pageNum, Integer pageSize, String keyword, String warehouseId, JSONObject param);
    
    /**
     * 根据ID获取药品详细信息
     * @param companyId 诊所ID
     * @param warehouseId 库房ID
     * @param oid 药品ID
     * @return 药品详细信息
     */
    ResponseVO<DrugInfoVO> getById(String companyId, String warehouseId, String oid);
    
    /**
     * 保存或更新药品基本信息
     * @param user 当前登录用户
     * @param drugBasicInfo 药品基本信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    ResponseVO<Boolean> saveOrUpdateBasicInfo(@Login User user, DrugBasicInfo drugBasicInfo, String warehouseId);
    
    /**
     * 保存或更新药品扩展信息
     * @param user 当前登录用户
     * @param drugExtendedInfo 药品扩展信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    ResponseVO<Boolean> saveOrUpdateExtendedInfo(@Login User user, DrugExtendedInfo drugExtendedInfo, String warehouseId);
    
    /**
     * 保存或更新药品定价信息
     * @param user 当前登录用户
     * @param drugPricingInfo 药品定价信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    ResponseVO<Boolean> saveOrUpdatePricingInfo(@Login User user, DrugPricingInfo drugPricingInfo, String warehouseId);
    
    /**
     * 保存或更新药品库存信息
     * @param user 当前登录用户
     * @param drugInventoryInfo 药品库存信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    ResponseVO<Boolean> saveOrUpdateInventoryInfo(@Login User user, DrugInventoryInfo drugInventoryInfo, String warehouseId);
    
    /**
     * 删除药品信息
     * @param companyId 诊所ID
     * @param warehouseId 库房ID
     * @param oid 药品ID
     * @return 操作结果
     */
    ResponseVO<Boolean> delete(String companyId, String warehouseId, String oid);
    
    /**
     * 保存药品完整信息
     * @param user 当前登录用户
     * @param drugInfo 药品完整信息
     * @return 操作结果
     */
    ResponseVO<Boolean> saveDrugInfo(@Login User user, DrugInfoVO drugInfo);

    /**
     * 更改药品状态
     */
    ResponseVO<String> changeDrugStatus(String drugOid, int enable);
}