package com.domino.framework.config;

import com.domino.framework.websocket.AuthHandshakeInterceptor;
import com.domino.framework.websocket.QhWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final AuthHandshakeInterceptor authHandshakeInterceptor;
    private final QhWebSocketHandler qhWebSocketHandler;

    public WebSocketConfig(AuthHandshakeInterceptor authHandshakeInterceptor,
                           QhWebSocketHandler qhWebSocketHandler) {
        this.authHandshakeInterceptor = authHandshakeInterceptor;
        this.qhWebSocketHandler = qhWebSocketHandler;
    }

    /**
     * 注册WebSocket处理器
     */
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 这里会在创建WebSocketHandler后进行注册
        registry.addHandler(qhWebSocketHandler, "/chat")
                .setAllowedOrigins("*")  // 允许跨域
                .addInterceptors(authHandshakeInterceptor); // 添加Token拦截器
    }
}
