<!DOCTYPE html>
<html lang="zh-CN" xmlns:th=http://www.thymeleaf.org>

<head th:insert="/commonHead::ui"></head>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0">
    <title>Dev自由平台</title>
    <style>
        .beian {
            position: fixed;
            bottom: 30px;
            text-align: center;
            width: 100%
        }

        #notice {
            position: fixed;
            right: 10px;
            width: 420px;
            z-index: 9999;
        }
    </style>
</head>

<body style="height: 100%;">
<div class="main" style="text-align:center;width:100%;height: 100%;position: absolute;margin-top:50px;">
    <form id="form" action="init/do" data-submit="initDo">
        <style>#t2 .tab-panel {
            min-height: 350px
        }</style>

        <div class="block-box" style="justify-content: center">

            <div class="col-6">
                <div class="block">

                    <div id="t1">
                        <div class="tab-panel">
                            <div class="block-box" style="text-align:left;">
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>初始化密码</h6>
                                            <input name='initPwd' title="初始化密码" type="text"
                                                   placeholder="请输入你需要的内容"
                                                   data-validate="require"
                                                   data-validate-msg="请输入初始化密码">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>用户账号</h6>
                                            <input name='userBh' title="用户账号" type="text" placeholder="请输入你需要的内容"
                                                   data-validate="require"
                                                   data-validate-msg="请输入账号，建议英文数字">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>用户名称</h6>
                                            <input name="userName" type="text" placeholder="请输入你需要的内容"
                                                   data-validate="require"
                                                   data-validate-msg="请输入确认密码|两次密码输入不同">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>机构名称</h6>
                                            <input name="orgName" type="text" placeholder="请输入你需要的内容"
                                                   data-validate="require"
                                                   data-validate-msg="请输入确认密码|两次密码输入不同"
                                            >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>机构简称</h6>
                                            <input name="orgShortName" type="text" placeholder="请输入你需要的内容"
                                                   data-validate="require"
                                                   data-validate-msg="请输入确认密码|两次密码输入不同"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>设置你的新密码</h6>
                                            <input name="pword" id="pword" type="text" placeholder="请输入密码"
                                                           data-validate="require|same=repword"
                                                           data-validate-msg="请输入密码|两次密码输入不同">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="block">
                                        <div class="form-group">
                                            <h6>重新输入新密码</h6>
                                            <input name="repword" id="repword" type="text"
                                                           placeholder="请输入确认密码"
                                                           data-validate="require|same=pword"
                                                           data-validate-msg="请输入确认密码|两次密码输入不同">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <button id="submit" data-loading="提交中..." data-submit class="hl" >提交表单</button>
                    </div>
                </div>

            </div>
        </div>

    </form>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
        <path fill="#0099ff" fill-opacity="0.7"
              d="M0,64L17.1,96C34.3,128,69,192,103,229.3C137.1,267,171,277,206,256C240,235,274,181,309,154.7C342.9,128,377,128,411,128C445.7,128,480,128,514,154.7C548.6,181,583,235,617,234.7C651.4,235,686,181,720,186.7C754.3,192,789,256,823,256C857.1,256,891,192,926,154.7C960,117,994,107,1029,106.7C1062.9,107,1097,117,1131,144C1165.7,171,1200,213,1234,208C1268.6,203,1303,149,1337,106.7C1371.4,64,1406,32,1423,16L1440,0L1440,320L1422.9,320C1405.7,320,1371,320,1337,320C1302.9,320,1269,320,1234,320C1200,320,1166,320,1131,320C1097.1,320,1063,320,1029,320C994.3,320,960,320,926,320C891.4,320,857,320,823,320C788.6,320,754,320,720,320C685.7,320,651,320,617,320C582.9,320,549,320,514,320C480,320,446,320,411,320C377.1,320,343,320,309,320C274.3,320,240,320,206,320C171.4,320,137,320,103,320C68.6,320,34,320,17,320L0,320Z"></path>
    </svg>
</div>
</body>
<script>
    let module = {
        reg: true,
        conf: {},
        framework: ['jquery', 'lodash', 'axios'],
        data: [],
        plugin: [],
        ready: function () {
            Eui.tab('#t1', [
                {name: '初始化信息', icon: 'ri-user-2-line', active: true}]);

            METHOD.initDo = (response) => {
                // if (response.code !== CONF.http.code_success) return;
                // setTimeout(() => {
                //     window.location.href = getRootPath() + '/main'
                // }, 200)
            };

        }
    };
</script>
<script th:src="@{/js/pui.min.js}"></script>
</html>
