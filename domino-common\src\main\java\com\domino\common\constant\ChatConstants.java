package com.domino.common.constant;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.UUID;

/**
 * 聊天相关常量
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
public class ChatConstants {

    public static final String NULLSTRING = "";//""字符串
    public static final String TOKENHEADER = "Authorization";
    public static final DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//日期format
    public static final DateFormat year_month = new SimpleDateFormat("yyyy-MM");//日期format
    public static final DateFormat year_month_day = new SimpleDateFormat("yyyy-MM-dd");//日期format
    public static final DateFormat dfpath = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");//日期format
    public static final DateFormat year_month_day_no_ = new SimpleDateFormat("yyyyMMdd");//日期format
    public static final DateFormat year_month_day_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//日期format
    public static final DateFormat year_month_day_time_no_ = new SimpleDateFormat("yyyyMMddHHmmss");//日期format
    public static final DateFormat dtimecode = new SimpleDateFormat("mm_sss");//日期format
    public static final String TOKEN_REDIS = "DvP:Admin:token:";
    //加密次数
    public static final int HASHITERATIONS = 2;
    public static final String FORMULAJSONTOP = "JSON:";
    public static final String FORMULADATETOP = "DATE:";
    public static final int TABLE_MAX_RECORD = 9000000;
    public static final String defaultPwd = "admin123";
    public static final String SOCKET_REIDS = "dev:Admin:Socket:";

    /**
     * 创建UUID
     *
     * @return UUID
     */
    public static String createUUID() {
        String uuid = UUID.randomUUID().toString();
        uuid = uuid.toUpperCase();
        uuid = uuid.replaceAll("-", ChatConstants.NULLSTRING);
        return uuid;
    }
    
    /**
     * 消息类型REDIS_CHANNEL
     */
    public static final String MSG_TYPE_SYSTEM = "SYSTEM";
    public static final String MSG_TYPE_PERSONAL = "PERSONAL";
    public static final String MSG_TYPE_CHAT = "CHAT";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /** redis 订阅消息通道标识*/
    public final static String REDIS_CHANNEL = "onMessage";
    public final static String REDIS_CHANNEL_CLOSE="close";
    public final static String REDIS_CHANNEL_SEND="send";

    /** 消息体的key*/
    public final static String REDIS_MESSAGE_KEY = "KEY";

    /** 消息体的值*/
    public final static String REDIS_MESSAGE_VALUE = "VALUE";
    
    /**
     * 接收范围
     */
    public static final String RECEIVE_SCOPE_ALL = "ALL";
    public static final String RECEIVE_SCOPE_ORG = "ORG";
    public static final String RECEIVE_SCOPE_CURRORG = "CURRORG";
    public static final String RECEIVE_SCOPE_USER = "USER";

    //实名信息
    public static final int AUTHLEVEL_NOAUTH = 0;//未实名
    public static final int AUTHLEVEL_AUTH = 1;//实名
    public static final int ID_TYPE_IDCARD = 0;//身份类型 身份证


    //参数标识 0 临时参数 有效期不超过1天的参数  1 系统参数 2 业务参数
    public static final String PFLAG_TEMP = "0";
    public static final String PFLAG_SYS = "1";
    public static final String PFLAG_BNS = "2";

    //登录渠道
    public static final String WX_PUB = "WX_PUB";
    public static final String H5 = "H5";


    //RedisKey 管理
    public static final String RK_RETRY_TIMES = "qh:login:retry-times:";//+requestId 密码或验证码连续输入错误次数
    public static final String RK_SMS_CODE = "qh:sms:code:";//+phoneNo 短信验证码 默认10分钟有效
    public static final String RK_SMS_HADSENT = "qh:sms:sendInOneMinutes:";//+phoneNo 一分钟发送一次标识 有效期1分钟
    public static final String RK_SMS_HADSENT_REQ = "qh:sms:sendInOneMinutes:req:";//+requestId一分钟发送一次标识 有效期1分钟
    public static final String RK_SMS_SENDNUMSONEDAY = "qh:sms:sendNumsInOneDay:";//+phoneNo 一天发送了多少条短信，默认允许10条
    public static final String RK_SMS_SENDNUMSONEDAY_REQ = "qh:sms:sendNumsInOneDay:req:";//+requestId 一天发送了多少条短信，默认允许10条
    public static final String RK_QH_TOKEN = "qh:user:token:";//+token token获取用户信息
    public static final String RK_QH_ID_TOKEN = "qh:user:oid:";//+oid oid获取token

    public static final String RK_QH_TICKET = "qh:user:ticket:";//+ticket ticket 用于oauth2换取token
    public static final String RK_QH_CLIENT_ID = "qh:user:clientId:";//+clientId 获取clientId相关信息 存requestId走通旧登录
    public static final String RK_QH_TICKET_TEMP = "qh:SSO:TOKEN_TICKET_TEMP:";//+token 避免token 频繁生成ticket
    public static final String RK_QH_LOGOUT = "qh:SSO:LOGOUT:";//记录第三方退出登录成功后的回调地址
    public static final String RK_TEMP = "qh:TEMP:";//临时数据

    
    /**
     * 用户在线状态
     */
    public static final String USER_ONLINE = "1";
    public static final String USER_OFFLINE = "0";
    
    /**
     * 消息已读状态
     */
    public static final Integer READ_STATUS_UNREAD = 0;
    public static final Integer READ_STATUS_READ = 1;
    
    /**
     * 删除标记
     */
    public static final Integer DEL_FLAG_NORMAL = 0;
    public static final Integer DEL_FLAG_DELETED = 1;
    
    /**
     * 是否为图片
     */
    public static final Integer IS_IMAGE_NO = 0;
    public static final Integer IS_IMAGE_YES = 1;
    
    /**
     * WebSocket消息类型
     */
    public static final String WS_MSG_TYPE_MESSAGE = "SYSTEM";
    public static final String WS_MSG_TYPE_PERSONAL = "PERSONAL";
    public static final String WS_MSG_TYPE_CHAT = "CHAT";
    public static final String WS_MSG_TYPE_NOTIFICATION = "notification";
    public static final String WS_MSG_TYPE_SHELL = "shell";
}
