package com.domino.common.qh.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.domino.common.annotation.Excel;
import com.domino.common.constant.ChatConstants;
import com.domino.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 消息对象 sys_message
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String messageId;

    /**
     * 消息标题
     */
    @Excel(name = "消息标题")
    private String title;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    private String content;

    /**
     * 消息类型(SYSTEM-系统消息,PERSONAL-公告通知,CHAT-聊天消息)
     */
    @Excel(name = "消息类型")
    private String type;

    /**
     * 发送人ID
     */
    @Excel(name = "发送人ID")
    private String senderId;

    /**
     * 发送人姓名
     */
    @Excel(name = "发送人姓名")
    private String senderName;

    /**
     * 发送人所属机构ID
     */
    private String orgId;

    /**
     * 删除标记(0-正常,1-删除)
     */
    private Integer delFlag;

    /**
     * 接收范围(ALL-全部人员, ORG-机构, CURRORG-当前登录机构,USER-指定用户)
     */
    @Excel(name = "接收范围")
    private String receiveScope;

    /**
     * 接收对象ID(机构ID/用户ID列表)
     */
    private String receiveTarget;

    /**
     * 前端使用字段-是否已读，0 否 1 是 仅用于通知公告是否已读，聊天请使用chatReadStatus
     */
    @TableField(exist = false)
    private Integer readStatus;

    // 以下字段为聊天消息专用字段，其他消息无需考虑
    /**
     * 发送者性别
     */
    private Integer senderChatSex;

    /**
     * 发送者手机号
     */
    private String senderChatPhone;

    /**
     * 接收者性别
     */
    private Integer receiveTargetChatSex;

    /**
     * 接收者手机号
     */
    private String receiveTargetChatPhone;

    /**
     * 是否是图片(0-否,1-是)
     */
    private Integer isImage;

    /**
     * 引用消息ID
     */
    private String quoteOid;

    /**
     * 引用内容
     */
    private String quoteContent;

    /**
     * 引用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date quoteTimestamp;

    /**
     * 聊天内容是否已读(0-未读,1-已读)
     */
    private Integer chatReadStatus;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String remark;

    private void init() {
        this.type = ChatConstants.WS_MSG_TYPE_MESSAGE;
        this.messageId = ChatConstants.createUUID();
        this.createTime = new Date();
        this.senderId = "SYSTEM";
        this.senderName = "系统消息";
        this.delFlag = 0;
    }

    public SysMessage() {}

    private SysMessage(String type, String content) {
        this.init();
        this.type = type;
        this.content = content;
    }

    private SysMessage(String content) {
        this.init();
        this.content = content;
    }

    private SysMessage(String type, String title, String content) {
        this.init();
        this.type = type;
        this.title = title;
        this.content = content;
    }

    public static SysMessage getOne(String type, String content) {
        return new SysMessage(type, content);
    }

    public static SysMessage getOne(String content) {
        return new SysMessage(content);
    }
    public static SysMessage getOne(String type, String title, String content) {
        return new SysMessage(type, title, content);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("messageId", getMessageId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("type", getType())
                .append("senderId", getSenderId())
                .append("senderName", getSenderName())
                .append("orgId", getOrgId())
                .append("createTime", getCreateTime())
                .append("delFlag", getDelFlag())
                .append("receiveScope", getReceiveScope())
                .append("receiveTarget", getReceiveTarget())
                .append("senderChatSex", getSenderChatSex())
                .append("senderChatPhone", getSenderChatPhone())
                .append("receiveTargetChatSex", getReceiveTargetChatSex())
                .append("receiveTargetChatPhone", getReceiveTargetChatPhone())
                .append("isImage", getIsImage())
                .append("quoteOid", getQuoteOid())
                .append("quoteContent", getQuoteContent())
                .append("quoteTimestamp", getQuoteTimestamp())
                .append("chatReadStatus", getChatReadStatus())
                .toString();
    }
}
