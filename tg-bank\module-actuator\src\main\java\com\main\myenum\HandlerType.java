package com.main.myenum;

import com.main.bean.dto.HandlerStateDTO;

/**
 * 业务状态和信息枚举类
 *
 * <AUTHOR>
 */
public enum HandlerType {

	/**
	 * 历史之前接口成功代码为 0000
	 */
	SUCCESS(20000, "操作执行成功"),
	UNKNOWN(9999, "未知错误"),

	//系统内部 - 统一错误
	SYSTEM_ERROR(9998, "系统内部错误"),

	//系统内部 - 详细错误
	SRV_SWITCH_EMPTY(9000, "系统内部错误，未找到对应前置服务！"),
	SRV_LOGIN_INVALID(9001, "登录过期或尚未登录"),
	SRV_NO_AUTH_ACCESS(9403, "当前用户无此菜单权限，请联系管理员分配！"),
	SRV_LOGIN_LOCK(9002, "用户被锁定，无法登录，请联系管理员！"),
	SRV_LOGIN_STOP(9003, "用户被停用，无法登录，请联系管理员！"),
	SRV_LOGIN_UNKOWN_STATUS(9004, "用户当前状态未知，无法登录，请联系管理员！"),

	//外部系统 - 统一错误
	OUT_SYSTEM_ERROR(1000, "外部系统错误"),


	/**
	 * 支付状态
	 */
	PAY_PAYING(2001,"用户支付中，请稍后再查询支付结果"),
	PAY_REQ_PWD(2002,"需要用户输入支付密码"),


	//外部系统 - 请求类入参错误
	REQ_PARAMS_INVALIAD(1000,"请求参数校验失败"),
	REQ_POST_TEXT_NULL(1001, "请求报文为空！"),
	REQ_POST_TEXT_ILLEGAL(1002, "请求报文未按格式要求！"),
	REQ_APP_ID_EMPTY(1003, "请求的应用ID为空！"),
	REQ_APP_ID_ILLEGAL(1004, "请求的应用ID不可用！"),
	REQ_TIMESTAMP_EMPTY(1005, "请求的时间戳为空！"),
	REQ_DECRYPT_FAIL(1006, "解密失败！"),
	REQ_CHECK_SIGN_FAIL(1007, "验签失败！"),
	REQ_METHOD_EMPTY(1008, "请求的方法为空！"),
	REQ_PAYCONFIG_NULL(1009, "支付配置参数表[PAYCONFIG]为空！"),
	REQ_CHANNEL_TYPE_ERROR(1010, "支付渠道非法，未找到对应支付渠道！"),

	SOCKET_ERROR(2000, "SOCKET 错误！");








	private int retCode;
	private String retMsg;

	HandlerType(int retCode, String retMsg) {
		this.retCode = retCode;
		this.retMsg = retMsg;
	}

	public int getRetCode() {
		return retCode;
	}

	public String getRetMsg() {
		return retMsg;
	}

	public static <T> boolean isSuccessful(HandlerStateDTO responseVO) {
		return responseVO.getCode()==(HandlerType.SUCCESS.getRetCode());
	}
}
