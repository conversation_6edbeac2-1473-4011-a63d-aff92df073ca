package com.main.sdk.test;


import com.main.bean.vo.ResponseVO;
import com.main.sdk.DevSdkClient;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

/**
 * <AUTHOR>
 */
public class SdkClientTest {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	//全局参数
	private static final String hostIp = "***************";//***************
//	private static final String hostIp = "127.0.0.1";//***************
//	private static final String web_pay_url = "http://"+hostIp+":8700/open-platform";//华为云服务器
	private static final String web_pay_url = "https://netapp.3dsky.com.cn/open-platform";//华为云服务器
	private static final String signType = "MD5";
	private static final String encryptType = "AES";
	private static final String appId = "1A3VL0KVK0000B020A0A0000CC3F48AD";
	private static final String appSecret = "1A3VL0KVE0010B010A0A0000277BDC91";
//	private static final String appId = "1";
//	private static final String appSecret = "1";

	@Test
	public void testAdminPlatform() {
		DevSdkClient sdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {
			//查看接口文档的 局部参数
			String platform = "admin-platform"; //看接口文档platform
			String method = "admin.login.getUserByToken";//看接口文档 method
			String version = "1.0.0";//看接口文档version


			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("token", "3F2AF2AC8CAA4194BF0C70DF09958903");
			// 发起交易
			ResponseVO<Map<String, Object>> responseParams = sdkClient.unifyCall(platform, method, version, paramMap);
			Map<String, Object> bridge = responseParams.getBizObj();
			System.out.println(JSON.toJSONString(responseParams));
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 测试-获取登录页面地址
	 */
	@Test
	public void getLoginPageUrl() {
		logger.info("开始请求");
		DevSdkClient sdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {

			//查看接口文档的 局部参数
			String platform = "mid-user-center"; //看接口文档platform
			String method = "user.login.url";//看接口文档 method
			String version = "1.0.0";//看接口文档version

			Map<String,Object> paramMap = new HashMap<>();
			paramMap.put("callBackUrl","http://"+hostIp+":8701/uc-app/user/test/ucCallBack");
			paramMap.put("channel","WX_PUB");
			paramMap.put("wxAppId","wx30cadbf99011e526");
			Map<String,Object> extParamMap = new HashMap<>();
			extParamMap.put("requestUUID", "123");
			paramMap.put("extParamMap",extParamMap);

			// 发起交易
			ResponseVO<Map<String,Object>> responseParams = sdkClient.unifyCall(platform,method,version,paramMap);
			logger.info("返回的结果为："+JSON.toJSONString(responseParams));
			logger.info("其中业务返回为："+JSON.toJSONString(responseParams.getBizObj()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试
	 */
	@Test
	public void testOpenPlatform() {
		logger.info("开始请求");
		DevSdkClient openPlatformSdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {

			//查看接口文档的 局部参数
			String platform = "mid-user-center"; //看接口文档platform
			String method = "test.service.forfun";//看接口文档 method
			String version = "1.0.0";//看接口文档version

			Map<String,Object> param = new HashMap<>();
			param.put("name","testUser");
			param.put("id","A000001");
//			// 发起交易
			ResponseVO responseParams = openPlatformSdkClient.unifyCall(null,method,version,param);
			logger.info("返回的结果为："+JSON.toJSONString(responseParams));
			logger.info("其中业务返回为："+JSON.toJSONString(responseParams.getBizObj()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 测试
	 */
	@Test
	public void testMidUserCenter() {
		logger.info("开始请求");
		DevSdkClient openPlatformSdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {
			//查看接口文档的 局部参数
			String platform = "mid-user-center"; //看接口文档platform
			String method = "test.service.sayHi";//看接口文档 method
			String version = "1.0.0";//看接口文档version

			Map<String,Object> param = new HashMap<>();
			param.put("name","testUser");
			param.put("id","A000001");
//			// 发起交易
			ResponseVO responseParams = openPlatformSdkClient.unifyCall(platform,method,version,param);
			logger.info("返回的结果为："+JSON.toJSONString(responseParams));
			logger.info("其中业务返回为："+JSON.toJSONString(responseParams.getBizObj()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testJms() {
		logger.info("开始请求");
		DevSdkClient openPlatformSdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {
			//查看接口文档的 局部参数
			String platform = null; //看接口文档platform
			String method = "test.service.jms";//看接口文档 method
			String version = "1.0.0";//看接口文档version

			Map<String,Object> param = new HashMap<>();
			param.put("message","send a message to u !");
//			// 发起交易
			ResponseVO responseParams = openPlatformSdkClient.unifyCall("test.service.jms",version,param);
			logger.info("返回的结果为："+JSON.toJSONString(responseParams));
			logger.info("其中业务返回为："+JSON.toJSONString(responseParams.getBizObj()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void test3() throws InterruptedException {
		// 同一时刻最大的并发线程的个数  即并发数
		final int concurrentThreadNum = 2;

		//总共多少线程
		final int countThreadNum = 2;

		ExecutorService executorService = Executors.newCachedThreadPool();
		CountDownLatch countDownLatch = new CountDownLatch(countThreadNum);
		Semaphore semaphore = new Semaphore(concurrentThreadNum);
		for (int i = 0; i< countThreadNum; i++) {
			executorService.execute(()->{
				try {
					semaphore.acquire();
//					test();
					semaphore.release();
				} catch (InterruptedException e) {
					logger.error("exception", e);
				}
				countDownLatch.countDown();
			});
		}
		countDownLatch.await();
		executorService.shutdown();
		logger.info("请求完成");
	}

}
