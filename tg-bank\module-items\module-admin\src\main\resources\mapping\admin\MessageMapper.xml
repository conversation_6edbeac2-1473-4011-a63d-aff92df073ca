<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.MessageMapper">

    <select id="pageMessages" resultType="com.main.pojo.admin.Message">
        select a.* from (
            select a.*, if(b.oid is null, 0, 1) as readStatus from d_message a
                 left join d_message_receiver b on a.oid = b.message_id and b.receiver_id = #{user.oid}
            <where>
                <choose>
                    <when test="type != null and type != '' and type != 'ALL'">
                        a.type = #{type}
                    </when>
                    <otherwise>
                        (a.type = 'SYSTEM' or a.type = 'PERSONAL')
                    </otherwise>
                </choose>
                <if test="keyword != null and keyword != ''">
                and (a.title like concat('%', #{keyword},'%') or a.content like concat('%', #{keyword},'%'))
                </if>
                <choose>
                    <when test="type == 'CHAT'">
                        and a.receive_scope = 'USER' and (
                               a.receive_target = #{userOid} and a.sender_id = #{user.oid}
                            or a.receive_target = #{user.oid} and a.sender_id = #{userOid}
                        )
                    </when>
                    <otherwise>
                        and (a.receive_scope = 'ALL'
                            or (a.receive_scope = 'ORG' and a.receive_target like concat('%', #{user.currentLoginOrg.oid},'%'))
                            or (a.receive_scope = 'USER' and a.receive_target = #{user.oid})
                        )
                    </otherwise>
                </choose>
            ) a
            <where>
                <if test="readStatus != null and readStatus != -1">
                    readStatus = #{readStatus}
                </if>
                and del_flag = 0
            </where>
           order by create_time desc
        </where>
    </select>
    <select id="getUnreadCount" resultType="java.util.Map">
        select type, sum(readStatus) nums, sum(chat_read_status) chatNums from (
            SELECT if(a.type = 'PERSONAL', 'SYSTEM', type) AS type
            , if(b.oid IS NULL, 1, 0) AS readStatus
            , if(a.chat_read_status = 0, 1, 0) as chat_read_status from d_message a
            left join d_message_receiver b on a.oid = b.message_id and b.receiver_id = #{user.oid}
                where (
                    a.receive_scope = 'ALL'
                    or (a.receive_scope = 'ORG' and a.receive_target like concat('%', #{user.currentLoginOrg.oid},'%'))
                    or (a.receive_scope = 'USER' and a.receive_target = #{user.oid})
                )
                and del_flag = 0
            ) a
        group by type
    </select>
</mapper>
