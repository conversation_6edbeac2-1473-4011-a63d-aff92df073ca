package com.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializeConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.List;
import java.util.Map;

@Slf4j
public class JsonUtil {
	private static SerializeConfig innerConfig = new SerializeConfig();

	// 默认属性配置

	public static String toJson(Object obj) {
		return JSON.toJSONString(obj);
	}

	public static <T> T parseObject(String json, Class<T> clazz) {
		return JSON.parseObject(json, clazz);
	}


	public static Map<String, Object> parseMap(String json) {
		return JSON.parseObject(json, Map.class);
	}


	public static <T> List<T> parseList(String json, Class<T> clazz) {
		return JSONArray.parseArray(json, clazz);
	}

	public static <T> T fromJson(String json, Class<T> clazz) {
		return JSON.parseObject(json, clazz);
	}

	public static <T> T fromJson(String json, TypeReference<T> type) {
		return JSON.parseObject(json, type);
	}


	/**
	 * @param json
	 * @return
	 */
	public static Map<String, Object> parseObjectMap(String json) {
		try {
			return JSON.parseObject(json, new ObjectMapRef());
		} catch (Exception e) {
			return JsonUtil.parseMap(json);
		}
	}

	//读取json文件
	public static String readJsonFile(String fileName) {
		String jsonStr = "";
		try {
			File jsonFile = new File(fileName);
			FileReader fileReader = new FileReader(jsonFile);
			Reader reader = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
			int ch = 0;
			StringBuffer sb = new StringBuffer();
			while ((ch = reader.read()) != -1) {
				sb.append((char) ch);
			}
			fileReader.close();
			reader.close();
			jsonStr = sb.toString();
			return jsonStr;
		} catch (IOException e) {
			log.error("读取文件失败了：{}", e.getMessage());
			return null;
		}
	}

	static class ObjectMapRef extends TypeReference<Map<String, Object>> {
	}

	static class StringMapRef extends TypeReference<Map<String, String>> {
	}
}
