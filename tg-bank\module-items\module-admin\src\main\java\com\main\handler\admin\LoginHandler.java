package com.main.handler.admin;

import com.alibaba.fastjson.JSONObject;
import com.common.Global;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.service.admin.UserService;
import com.main.service.util.MyHttpUtil;
import com.main.vo.MenuVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Controller
@Slf4j
public class LoginHandler {


    @Resource
    private UserService userService;


    /**
     * 登录系统
     *
     * @return 通用返回对象
     */
    @LogAnnotation(option = "用户登录")
    @PostMapping("/api/user/login")
    @ResponseBody
    public ResponseVO<Object> doLogin(@RequestBody Map<String, String> params, HttpServletRequest request) {
        log.info("用户登录，用户名：{}，密码：{}", params.get("username"), params.get("password"));
        return userService.doLoginWithPwd(MyHttpUtil.getHeaders(request), params.get("username"), params.get("password"));
    }

    @PostMapping("/user/info")
    @ResponseBody
    public ResponseVO<User> getUserInfo(@Login User user) {
        return userService.getUserInfo(user);
    }

    @LogAnnotation(option = "退出登录")
    @PostMapping("/api/user/logout")
    @ResponseBody
    public ResponseVO<User> logout(HttpServletRequest request) {
        return userService.logout(request.getHeader(Global.TOKENHEADER));
    }

    @LogAnnotation(option = "注册用户")
    @PostMapping("/api/user/register")
    @ResponseBody
    public ResponseVO<String> register(@RequestBody JSONObject params) {
        //FIXME 校验权限
        return userService.register(params);
    }

    @PostMapping("/user/menu")
    @ResponseBody
    public ResponseVO<List<MenuVO>> getMenu(@Login User user) {
        long start = System.currentTimeMillis();
        ResponseVO<List<MenuVO>> responseVO = userService.getMenu(user);
        long end = System.currentTimeMillis();
        long callTimeConsume = end - start;
        log.info("获取菜单接口耗时：{}", callTimeConsume);
        return responseVO;
    }

    @PostMapping("/user/switch/org")
    @ResponseBody
    public ResponseVO<User> switchOrg(@Login User user, @RequestBody Map<String, String> params, HttpServletRequest request){
        return userService.switchOrg(params.get("orgOid"), request, user);
    }

    @PostMapping("/user/resetPwd")
    @ResponseBody
    public ResponseVO<Object> resetPwd(@Login User user, @RequestBody JSONObject params, HttpServletRequest request){
        return userService.resetPwd(user, params.getString("oid"));
    }

    @PostMapping("/user/uptPwd")
    @ResponseBody
    public ResponseVO<Object> uptPwd(@Login User user, @RequestBody JSONObject params, HttpServletRequest request){
        User userPage = params.toJavaObject(User.class);
        return userService.uptPwd(user.getOid(), userPage);
    }

    /**
     * 个人中心中登录记录和管理团队接口
     *
     * @param user 当前登录用户信息
     * @return {managerTeam:[users],loginRecord:[userLoginRecord]}
     */
    @PostMapping("/user/getLoginRecordAndManager")
    @ResponseBody
    public ResponseVO<Object> getLoginRecordAndManager(@Login User user) {
        return userService.getLoginRecordAndManager(user);
    }
}
