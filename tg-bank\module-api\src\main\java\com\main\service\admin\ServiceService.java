package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

import java.util.List;

/**
 * <p>
 * 服务器部署的服务信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
public interface ServiceService extends IService<Service> {

    List<Service> getList(JSONObject params, User user);

    ResponseVO<String> mySave(JSONObject server, String oid);

    ResponseVO<String> myDel(String oid);

    List<Service> listMonitorService();

    void removeByCloudOid(String oid);
    void removeByServerOid(String oid);
}
