# SysMessageMapper 增强文档

## 概述

本次修改将 `SysMessageServiceImpl` 中所有使用 MyBatis-Plus 默认方法的地方替换为使用 XML 中定义的自定义方法，提高了性能和可维护性。

## 修改内容

### 1. SysMessageMapper.xml 增强

**文件路径**: `domino-system/src/main/resources/mapper/system/SysMessageMapper.xml`

#### 新增方法:
- `cleanExpiredChatPicContent` - 清理过期的聊天图片内容

#### 修改方法:
- `deleteChatMessages` - 支持删除所有聊天消息或指定用户的聊天消息

### 2. SysMessageMapper 接口增强

**文件路径**: `domino-system/src/main/java/com/domino/system/mapper/SysMessageMapper.java`

#### 新增方法:
```java
/**
 * 清理过期的聊天图片内容
 *
 * @param senderId 发送者ID
 * @param receiveTarget 接收者ID
 * @param expireTime 过期时间
 * @return 影响行数
 */
int cleanExpiredChatPicContent(@Param("senderId") String senderId, 
                               @Param("receiveTarget") String receiveTarget, 
                               @Param("expireTime") Date expireTime);
```

### 3. SysMessageServiceImpl 重构

**文件路径**: `domino-system/src/main/java/com/domino/system/service/impl/SysMessageServiceImpl.java`

#### 修改的方法:

##### 3.1 markAllAsRead 方法
**修改前**: 使用 `LambdaQueryWrapper` 和 `this.list()`
```java
LambdaQueryWrapper<SysMessage> queryWrapper = new LambdaQueryWrapper<>();
// 复杂的条件构建
List<SysMessage> unreadMessages = this.list(queryWrapper);
```

**修改后**: 使用 XML 方法和 Java Stream 过滤
```java
// 构建查询条件对象
SysMessage queryMessage = new SysMessage();
queryMessage.setDelFlag(ChatConstants.DEL_FLAG_NORMAL);

// 根据消息类型过滤
if (ChatConstants.MSG_TYPE_SYSTEM.equals(type)) {
    queryMessage.setType(ChatConstants.MSG_TYPE_SYSTEM);
} else if (ChatConstants.MSG_TYPE_PERSONAL.equals(type)) {
    queryMessage.setType(ChatConstants.MSG_TYPE_PERSONAL);
}

// 查询所有符合条件的消息
List<SysMessage> allMessages = messageMapper.selectMessageList(queryMessage);

// 过滤出未读消息和符合接收范围的消息
List<SysMessage> unreadMessages = allMessages.stream()
    .filter(message -> !readMessageIds.contains(message.getMessageId()))
    .filter(message -> {
        // 接收范围过滤逻辑
        if (ChatConstants.RECEIVE_SCOPE_ALL.equals(message.getReceiveScope())) {
            return true;
        } else if (ChatConstants.RECEIVE_SCOPE_ORG.equals(message.getReceiveScope())) {
            return message.getReceiveTarget() != null && 
                   message.getReceiveTarget().contains(user.getDeptId().toString());
        } else if (ChatConstants.RECEIVE_SCOPE_USER.equals(message.getReceiveScope())) {
            return user.getUserId().toString().equals(message.getReceiveTarget());
        }
        return false;
    })
    .collect(Collectors.toList());
```

##### 3.2 deleteChatMessage 方法
**修改前**: 使用 `LambdaUpdateWrapper` 和 `this.remove()`
```java
LambdaUpdateWrapper<SysMessage> update = new LambdaUpdateWrapper<>();
update.set(SysMessage::getDelFlag, ChatConstants.DEL_FLAG_DELETED);
// 复杂的条件构建
this.remove(update);
```

**修改后**: 使用 XML 中的专用方法
```java
if(StringUtils.isEmpty(userOid)) {
    // 删除所有与当前用户相关的聊天消息
    messageMapper.deleteChatMessages(user.getUserId().toString(), null);
} else {
    // 删除与指定用户的聊天消息
    messageMapper.deleteChatMessages(user.getUserId().toString(), userOid);
}
```

##### 3.3 removeChatPic 方法
**修改前**: 使用 `LambdaUpdateWrapper` 和 `this.update()`
```java
LambdaUpdateWrapper<SysMessage> update = new LambdaUpdateWrapper<>();
update.set(SysMessage::getContent, "[图片已过期]");
// 复杂的条件构建
this.update(update);
```

**修改后**: 使用 XML 中的专用方法
```java
Date expireTime = DateUtils.addDays(new Date(), -3);
messageMapper.cleanExpiredChatPicContent(oid, target, expireTime);
```

### 4. XML 方法增强

#### 4.1 deleteChatMessages 方法增强
```xml
<!-- 删除聊天记录 -->
<update id="deleteChatMessages">
    update sys_message set del_flag = 1
    where type = 'CHAT'
    <choose>
        <when test="receiveTarget != null and receiveTarget != ''">
            and ((sender_id = #{senderId} and receive_target = #{receiveTarget})
                 or (sender_id = #{receiveTarget} and receive_target = #{senderId}))
        </when>
        <otherwise>
            and (sender_id = #{senderId} or receive_target = #{senderId})
        </otherwise>
    </choose>
</update>
```

#### 4.2 新增 cleanExpiredChatPicContent 方法
```xml
<!-- 清理过期的聊天图片内容 -->
<update id="cleanExpiredChatPicContent">
    update sys_message set content = '[图片已过期]'
    where type = 'CHAT'
    and sender_id = #{senderId}
    <if test="receiveTarget != null and receiveTarget != ''">
        and receive_target = #{receiveTarget}
    </if>
    and create_time &lt;= #{expireTime}
    and content != '[图片已过期]'
    and del_flag = 0
</update>
```

## 优化效果

### 1. 性能提升
- **减少 ORM 开销**: 直接使用 SQL 语句，避免了 MyBatis-Plus 的动态 SQL 生成开销
- **更精确的查询**: XML 中的 SQL 语句针对具体业务场景优化
- **减少内存使用**: 避免了复杂的 Lambda 表达式和条件构建器

### 2. 可维护性提升
- **SQL 可见性**: 所有 SQL 语句都在 XML 文件中，便于查看和优化
- **类型安全**: 使用强类型的方法签名，编译时检查参数类型
- **业务语义化**: 方法名更具业务含义，如 `deleteChatMessages`、`cleanExpiredChatPicContent`

### 3. 功能完善
- **条件查询优化**: 使用 `<choose>`、`<when>`、`<otherwise>` 标签实现复杂条件逻辑
- **参数灵活性**: 支持可选参数，如 `receiveTarget` 可为空
- **业务逻辑清晰**: 将复杂的业务逻辑封装在专用的 SQL 方法中

## 代码清理

### 移除的依赖
```java
// 移除未使用的导入
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.domino.common.utils.spring.SpringUtils;
```

### 保留的依赖
```java
// 保留必要的 MyBatis-Plus 依赖
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
```

## 测试建议

### 1. 单元测试
- 测试 `deleteChatMessages` 方法的两种场景（删除所有/删除指定用户）
- 测试 `cleanExpiredChatPicContent` 方法的过期时间逻辑
- 测试 `markAllAsRead` 方法的消息过滤逻辑

### 2. 集成测试
- 验证聊天消息删除功能的完整性
- 验证图片过期清理功能的正确性
- 验证消息已读标记功能的准确性

### 3. 性能测试
- 对比修改前后的查询性能
- 测试大数据量下的处理能力
- 验证内存使用情况

## 注意事项

1. **数据库索引**: 确保相关字段有适当的索引支持
2. **参数验证**: 在调用方法前进行必要的参数验证
3. **事务管理**: 批量操作建议在事务中执行
4. **错误处理**: 添加适当的异常处理机制

## 后续优化建议

1. **缓存策略**: 为频繁查询的数据添加缓存
2. **分页优化**: 为大数据量查询添加分页支持
3. **异步处理**: 对于耗时的批量操作考虑异步处理
4. **监控告警**: 添加性能监控和异常告警机制
