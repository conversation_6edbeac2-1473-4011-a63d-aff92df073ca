package com.domino.framework.websocket;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.domino.common.constant.ChatConstants;
import com.domino.common.core.domain.model.LoginUser;
import com.domino.common.core.redis.RedisCache;
import com.domino.common.qh.domain.SysMessage;
import com.domino.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class QhWebSocketHandler extends TextWebSocketHandler {
    private static AtomicInteger onlineCount = new AtomicInteger(0);
    
    /**
     * 用来存放每个客户端对应的 WebSocketHandler 对象
     * key: token
     * value: WebSocketHandler实例
     */
    private static final ConcurrentHashMap<String, QhWebSocketHandler> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 存放 WebSocket Session
     * key: token
     * value: WebSocketSession 
     */
    private static final ConcurrentHashMap<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();

    private String id;
    private final StringRedisTemplate template;
    private final RedisCache redisCache;
    //private final AsyncMessageProcessor asyncProcesser;
    private final ISysUserService userService;


    @Autowired
    public QhWebSocketHandler(StringRedisTemplate template, RedisCache redisCache, ISysUserService userService) {
        this.template = template;
        this.redisCache = redisCache;
        //this.asyncProcesser = asyncProcesser;
        this.userService = userService;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Map<String, Object> attributes = session.getAttributes();

        try {
            // 从AuthHandshakeInterceptor设置的属性中获取用户信息
            LoginUser loginUser = (LoginUser) attributes.get("loginUser");
            String jwtToken = (String) attributes.get("token");
            Boolean authenticated = (Boolean) attributes.get("authenticated");

            if (loginUser != null && authenticated != null && authenticated) {
                // 使用LoginUser中的UUID token作为连接标识
                this.id = loginUser.getToken();

                // 如果已存在相同token的连接，关闭旧连接
                if (webSocketMap.containsKey(this.id)) {
                    QhWebSocketHandler oldHandler = webSocketMap.get(this.id);
                    try {
                        oldHandler.close(this.id);
                        log.info("关闭用户旧连接 - token: {}", this.id);
                    } catch (Exception e) {
                        log.error("关闭旧连接失败 - token: {}, error: {}", this.id, e.getMessage());
                    }
                }

                // 注册新连接
                webSocketMap.put(this.id, this);
                sessionMap.put(this.id, session);
                addOnlineCount();

                log.info("用户连接成功 - 用户: {}, token: {}, 当前在线数: {}, 当前连接数: {}",
                    loginUser.getUsername(), this.id, getOnlineCount(), webSocketMap.size());

                // 异步更新用户在线状态,此方法后续换一个类
                //asyncProcesser.syncUserOnlineState(userService, ChatConstants.USER_ONLINE, this.id);

            } else {
                log.warn("WebSocket连接失败：用户未通过身份验证");
                session.close();
            }
        } catch (Exception e) {
            log.error("WebSocket连接建立异常: {}", e.getMessage(), e);
            session.close();
            throw e;
        }
    }

    /**
     * 接收客户端的消息请求
     *
     * @param session WebSocketSession
     * @param message TextMessage
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // 处理接收到的文本消息
        String receivedMessage = message.getPayload(); // 获取接收到的消息内容
        log.warn("接收到的消息：{}", receivedMessage); // 打印接收到的消息内容
        JSONObject jsonObject = JSON.parseObject(receivedMessage);
        String type = jsonObject.getString("type");
        String token = jsonObject.getString("token");
        if(StrUtil.isEmpty(token) || StrUtil.isEmpty(type)) {
            sendMessage(token, SysMessage.getOne(ChatConstants.WS_MSG_TYPE_SHELL, "请确保type 和 token 入参不为空！"));
        }else {
            switch (type) {
                case "shell":
//                    fileService = SpringUtils.getBean(FileService.class);
//                    fileService.receiveWebSocket(jsonObject);
                    break;
                case "ping":
                    break;
                default:
                    sendMessage(token, SysMessage.getOne("未找到type对应的实现类！"));
                    break;

            }
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String token = this.id;
        if (token != null) {
            close(token);
            log.info("连接关闭 - token: {}, 当前在线数: {}, 当前连接数: {}", 
                token, getOnlineCount(), webSocketMap.size());
        }
    }

    /**
     * 向客户端发送消息，外部服务需要发送消息的请访问此接口
     * @description: 分布式  使用redis 去发布消息
     */
    public void sendMessage(@NotNull String key, SysMessage message) {
        String newMessge = null;
        newMessge = new String(JSON.toJSONString(message).getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        Map<String, String> map = new HashMap<>();
        map.put(ChatConstants.REDIS_MESSAGE_KEY, key);
        map.put(ChatConstants.REDIS_MESSAGE_VALUE, newMessge);
        template.convertAndSend(ChatConstants.REDIS_CHANNEL, JSON.toJSONString(map));
    }

    public void close(String token) {
        if (webSocketMap.containsKey(token)) {
            try {
                webSocketMap.remove(token);
                WebSocketSession session = sessionMap.remove(token);
                if (session != null && session.isOpen()) {
                    session.close();
                }
                subOnlineCount();
                //此方法后续换一个类
                //asyncProcesser.syncUserOnlineState(userService, ChatConstants.USER_OFFLINE, token);
                
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败 - token: {}, error: {}", token, e.getMessage());
            }
        }
    }

    /**
     * 向客户端真的发消息 （本地模式）外部服务禁止直接通过此方法发送消息，否则不支持分布式部署，通过sendMessage方法发送。
     *
     * @param key
     * @param message
     */
    void sendMessageToClient(@NotNull String key, String message) {
        if(!"ALL".equals(key)) {
            QhWebSocketHandler webSocketServer = webSocketMap.get(key);
            if (!ObjectUtil.isEmpty(webSocketServer)) {
                try {
                    webSocketServer.sendMessage(key, message);
                } catch (IOException e) {
                    e.getStackTrace();
                    log.error("编号id为：{}，发送消息：{}。失败！", key, message);
                }
            }
        } else {
            //群发
            webSocketMap.keySet().forEach(keyTemp -> {
                QhWebSocketHandler webSocketServer = webSocketMap.get(keyTemp);
                if (!ObjectUtil.isEmpty(webSocketServer)) {
                    try {
                        webSocketServer.sendMessage(keyTemp, message);
                    } catch (IOException e) {
                        e.getStackTrace();
                        log.error("编号id为：{}，发送消息：{}。失败！", keyTemp, message);
                    }
                }
            });
        }
    }

    /**
     * 实现服务器主动推送
     */
    void sendMessage(String key, String message) throws IOException {
        sessionMap.get(key).sendMessage(new TextMessage(message));
    }

    public static synchronized AtomicInteger getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        QhWebSocketHandler.onlineCount.getAndIncrement();
    }

    public static synchronized void subOnlineCount() {
        QhWebSocketHandler.onlineCount.getAndDecrement();
    }

    /**
     * 获取当前连接的用户token
     * @return 当前用户的token，如果未连接返回null
     */
    public String getCurrentUserToken() {
        return this.id;
    }

    /**
     * 检查指定token的用户是否在线
     * @param token 用户token
     * @return 是否在线
     */
    public static boolean isUserOnline(String token) {
        return webSocketMap.containsKey(token);
    }

    /**
     * 获取所有在线用户的token列表
     * @return 在线用户token集合
     */
    public static java.util.Set<String> getOnlineUserTokens() {
        return webSocketMap.keySet();
    }
}