package com.main.mapper.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.pojo.admin.Role;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface RoleMapper extends BaseMapper<Role> {

    IPage<Role> getPageOfRecords(Page<Role> page, @Param("isAdmin") int isAdmin, @Param("keyWord") String keyWord, @Param("userOid") String userOid);

    String getAutoGeneralID();
}
