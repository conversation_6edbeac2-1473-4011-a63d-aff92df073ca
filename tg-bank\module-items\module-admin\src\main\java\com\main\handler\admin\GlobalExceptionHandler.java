package com.main.handler.admin;

import com.common.Global;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Errors;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.util.NestedServletException;

import java.util.Optional;

/**
 * 全局异常处理
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResponseVO<Object> handle(Exception e) {
        getStackTraceString(e);
        ResponseVO<Object> responseVO = new ResponseVO<>();
        if (e instanceof BusinessException) {
            log.error("BusinessException异常处理，捕获异常信息:{}", e.getMessage());
            BusinessException businessException = (BusinessException) e;
            responseVO.setCode(businessException.getErrorCode());
            responseVO.setMsg(businessException.getErrorMessage());
        } else if (e instanceof MethodArgumentNotValidException) {
            log.error("MethodArgumentNotValidException异常处理，捕获异常信息:{}", e.getMessage());
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) e;
            BindingResult bindingResult = methodArgumentNotValidException.getBindingResult();
            Optional<String> message = Optional.of(bindingResult).map(Errors::getAllErrors).map(error -> error.get(0).getDefaultMessage());
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            message.ifPresent(responseVO::setMsg);
        } else if (e instanceof RpcException) {
            log.error("RpcException异常处理，捕获异常信息:{}", e.getMessage());
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg("远程服务繁忙，请稍后再试");
        } else if (e instanceof NestedServletException) {
            log.error("NestedServletException异常处理，捕获异常信息:{}", e.getMessage());
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            String errorMsg = e.getMessage().replace("Handler dispatch failed; nested exception is java.lang.AssertionError", Global.NULLSTRING);
			if(errorMsg.contains("expected")) {
				errorMsg = errorMsg.substring(0, errorMsg.indexOf("expected"));
			}
            responseVO.setMsg("处理请求异常 " + errorMsg);
        } else {
            String error = e.getMessage();
            if (CUtil.isEmpty(error)) {
                error = "空指针异常！";
            }
            log.error("Exception异常处理，捕获异常信息:{}", error);
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg(error);
        }
//		responseVO.setRemind("notice");

        return responseVO;
    }

    public static void getStackTraceString(Throwable e) {
        StackTraceElement[] traceElements = e.getStackTrace();
        StringBuilder traceBuilder = new StringBuilder();
        if (traceElements != null && traceElements.length > 0) {
            for (StackTraceElement traceElement : traceElements) {
                traceBuilder.append(traceElement.toString());
                traceBuilder.append("\n");
            }
        }
        log.error("全局出错了！{}", traceBuilder.toString());
    }
}
