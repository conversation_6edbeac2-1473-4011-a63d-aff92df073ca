package com.main.vo;

import com.common.StringUtil;
import lombok.Data;

/**
 * 通过ip获取用户所在的信息
 *
 * <AUTHOR>
 */
@Data
public class IPEntity {
	//浏览器和操作系统等信息
	String ipAddress;
	String browser;
	String osName;

	//国家
	String countryName;
	//国家代码
	String countryCode;

	//省份
	String provinceName;
	String provinceCode;

	//城市名称
	String cityName;

	//邮政编码
	String postalCode;

	//经度
	Double longitude;
	//纬度
	Double latitude;

	String location;


	public String getLocation() {
		if(StringUtil.isNotEmpty(location)) {
			return location;
		}else {
			return countryName+" "+provinceName+" "+cityName;
		}
	}
}
