package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.service.admin.DicService;
import org.junit.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 通用字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@RestController
@RequestMapping("/dic")
public class DicHandler {
    @Resource
    private DicService dicService;

    @MyAuthority(auth="/admin/dic/index")
    @PostMapping("/tree")
    public ResponseVO<Map<String,Object>> getTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        return dicService.getTreeData(String.valueOf(params.get("parentId")), user);
    }

    @LogAnnotation(option = "保存字典")
    @MyAuthority(auth="/admin/dic/index")
    @PostMapping("/save")
    public ResponseVO<String> saveOrg(@RequestBody JSONObject obj) {
        return dicService.save(obj);
    }

    @LogAnnotation(option = "删除字典")
    @MyAuthority(auth="/admin/dic/index")
    @PostMapping("/del")
    public ResponseVO<String> deleteOrg(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return dicService.delete(params.getString("oid"), params.getString("parentId"));
    }
}

