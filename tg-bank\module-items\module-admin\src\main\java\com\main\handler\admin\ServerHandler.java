package com.main.handler.admin;

import com.aizuda.monitor.OshiMonitor;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.handler.ActuatorHandler;
import com.main.handler.Api;
import com.main.myenum.ActuatorSM4Utils;
import com.main.pojo.admin.Server;
import com.main.pojo.admin.User;
import com.main.service.admin.ServerService;
import com.main.util.CUtil;
import org.junit.Assert;
import org.springframework.web.bind.annotation.*;
import oshi.SystemInfo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RequestMapping("/server")
@RestController
public class ServerHandler {
    private SystemInfo si = new SystemInfo();
    @Resource
    private OshiMonitor oshiMonitor;

    @Resource
    private ActuatorHandler actuatorHandler;

    @Resource
    private ServerService serverService;

    @MyAuthority(auths = "/server/base/index")
    @RequestMapping("/actuator/monitor")
    public ResponseVO<Map<String, Object>> monitor(HttpServletRequest request) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        Map<String, Object> server = actuatorHandler.monitor(ActuatorSM4Utils.getAuth());
        response.setData(server);
        return response;
    }

    @Deprecated
    @RequestMapping("/baseInfo")
    public JSONObject getBaseInfo() {
        return actuatorHandler.getBaseInfo(ActuatorSM4Utils.getAuth());
    }
    @MyAuthority(auths = "/server/base/index")
    @RequestMapping("/actuator/processor")
    public ResponseVO<JSONObject> getProcessor() {
        ResponseVO<JSONObject> resp = new ResponseVO<>();
        resp.setData(actuatorHandler.getProcessor(ActuatorSM4Utils.getAuth()));
        return resp;
    }
    @MyAuthority(auths = "/server/base/index")
    @RequestMapping("/actuator/processes/{sort}")
    public ResponseVO<JSONArray> getProcess(@PathVariable String sort) {
        ResponseVO<JSONArray> resp = new ResponseVO<>();
        resp.setData(actuatorHandler.getProcess(ActuatorSM4Utils.getAuth(), sort));
        return resp;
    }

    @MyAuthority(auth="/server/cloud/index")
    @RequestMapping("/list")
    @ResponseBody
    public ResponseVO<Map<String, Object>> list(@RequestBody JSONObject params, @Login User user) {
        String start = String.valueOf(params.get("current"));
        String limit = String.valueOf(params.get("pageSize"));
        int pageNum = Integer.parseInt(CUtil.nullToZero(start));
        int pageSize = Integer.parseInt(CUtil.nullToZero(limit));
        Assert.assertNotNull("未传递云服务器OID！", params.getString("cloudOid"));
        IPage<Server> serverList = serverService.getPage(pageNum, pageSize, params, user);
        return Api.packageTable(serverList);
    }

    @LogAnnotation(option = "保存服务器信息")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@RequestBody JSONObject server, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行此操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return serverService.saveServer(server, user.getOid());
    }

    @LogAnnotation(option = "删除服务器信息")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return serverService.deleteServer(params.getString("oid"));
    }

}
