package com.main.mapper.admin;

import com.main.pojo.admin.Res;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface ResMapper extends BaseMapper<Res> {

    String getAutoGeneralID(@Param("parentId") String parentId);

    List<Res> getResources();

    List<Res> getResourcesByUserid(String oid);
}
