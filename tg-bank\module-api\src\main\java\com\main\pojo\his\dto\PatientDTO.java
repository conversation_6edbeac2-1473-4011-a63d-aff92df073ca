package com.main.pojo.his.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 患者信息传输对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PatientDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String oid;

    /**
     * 患者姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 性别 M-男 F-女 U-未知
     */
    @NotBlank(message = "性别不能为空")
    @Pattern(regexp = "^[MFU]$", message = "性别只能是M(男)、F(女)、U(未知)")
    private String gender;

    /**
     * 年龄-年
     */
    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer ageYears;

    /**
     * 年龄-月
     */
    @Min(value = 0, message = "月龄不能小于0")
    @Max(value = 11, message = "月龄不能大于11")
    private Integer ageMonths;

    /**
     * 年龄-天
     */
    @Min(value = 0, message = "天数不能小于0")
    @Max(value = 31, message = "天数不能大于31")
    private Integer ageDays;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 出生日期 格式:yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 患者来源
     */
    private String source;

    /**
     * 身份证号
     */
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证号格式不正确")
    private String idCard;

    /**
     * 婚姻状况 1-未婚 2-已婚 3-离异 4-丧偶
     */
    @Pattern(regexp = "^[1-4]$", message = "婚姻状况值不正确")
    private String maritalStatus;
    /**
     * 是否是vip
     */
    private int isVip=0;
    /**
     * 体重(kg)
     */
    @DecimalMin(value = "0.00", message = "体重不能小于0")
    @DecimalMax(value = "500.00", message = "体重不能大于500")
    private BigDecimal weight;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 病历号
     */
    private String fileNo;

    /**
     * 既往病史
     */
    @Size(max = 500, message = "既往病史长度不能超过500字")
    private String medicalHistory;

    /**
     * 过敏史
     */
    @Size(max = 500, message = "过敏史长度不能超过500字")
    private String allergyHistory;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500字")
    private String remarks;

    /**
     * 就诊原因
     */
    @Size(max = 500, message = "就诊原因长度不能超过500字")
    private String visitReason;

    private String orgId;
} 