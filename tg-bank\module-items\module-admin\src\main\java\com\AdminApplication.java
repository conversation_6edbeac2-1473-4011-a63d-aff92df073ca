package com;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@ServletComponentScan
@DubboComponentScan(basePackages = "com.main.service")
@MapperScan(basePackages = "com.main.mapper")
@EnableTransactionManagement
@EnableAsync
@Slf4j
public class AdminApplication {

	public static void main(String[] args) {
		try {
			SpringApplication.run(AdminApplication.class, args);
		}catch(Exception e) {
			e.printStackTrace();
			log.error("后台管理应用服务启动失败！失败原因：{}",e.getMessage());
		}
	}
}
