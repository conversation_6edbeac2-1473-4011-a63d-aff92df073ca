package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.mapper.admin.ServiceMapper;
import com.main.pojo.admin.User;
import com.main.pojo.admin.Service;
import com.main.util.CUtil;
import com.main.util.Global;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务器部署的服务信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@org.springframework.stereotype.Service
public class ServiceServiceImpl extends ServiceImpl<ServiceMapper, Service> implements ServiceService {

    @Resource
    private ServiceMapper mapper;

    @Override
    public List<Service> getList(JSONObject params, User user) {
        LambdaQueryWrapper<Service> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Service::getServerOid, params.getString("serverOid"));
        return this.list(queryWrapper);
    }

    @Override
    public ResponseVO<String> mySave(JSONObject server, String oid) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Service db = server.toJavaObject(Service.class);
        if (CUtil.isEmpty(db.getOid())) {
            //ADD
            db.setOid(Global.createUUID());
            this.save(db);
        } else {
            //UPDATE
            this.updateById(db);
        }
        return responseVO;
    }

    @Override
    public ResponseVO<String> myDel(String oid) {
        ResponseVO<String> response = new ResponseVO<>();
        if(CUtil.isNotEmpty(oid)) {
            this.removeById(oid);
        }
        return response;
    }

    @Override
    public List<Service> listMonitorService() {
        return mapper.listMonitorService();
    }

    @Override
    public void removeByCloudOid(String oid) {
        LambdaQueryWrapper<Service> query = new LambdaQueryWrapper<>();
        query.eq(Service::getCloudOid, oid);
        this.remove(query);
    }

    @Override
    public void removeByServerOid(String oid) {
        LambdaQueryWrapper<Service> query = new LambdaQueryWrapper<>();
        query.eq(Service::getServerOid, oid);
        this.remove(query);
    }
}
