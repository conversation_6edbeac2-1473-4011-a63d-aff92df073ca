package com.main.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.Global;
import com.main.pojo.admin.User;
import com.main.service.util.AsyncProcesser;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

@Aspect
@Component
@Order(-5)
@Slf4j
public class ControllerInterceptor {
    @Value("${server.port}")
    private String port;
    // 这里不加lazy 会导致循环依赖问题，无法启动
    @Resource
    @Lazy
    private AsyncProcesser asyncProcesser;

    @Resource
    private RedisSrv redisSrv;

    /**
     * 定义一个切入点.
     * 解释下：
     * ~ 第一个 * 代表任意修饰符及任意返回值.
     * ~ 第二个 * 任意包名
     * ~ 第三个 * 代表任意方法.
     * ~ 第四个 * 定义在web包或者子包
     * ~ 第五个 * 任意方法
     * ~ .. 匹配任意数量的参数.
     */

    @Pointcut("execution(public * com.main.handler..*.*(..))")
    public void webLog() {
    }


    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {

        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        if(!request.getRequestURL().toString().contains("/server/actuator/")) {
            String tokenParam = request.getHeader(Global.TOKENHEADER);
            if (CUtil.isEmpty(tokenParam)) {
                tokenParam = request.getParameter(Global.TOKENHEADER);
            }
            User user = null;
            if (StringUtils.isNotBlank(tokenParam)) {
                user = redisSrv.getUserByToken(tokenParam);
                try {
                    if (user != null) {
                        //add TraceID
                        MDC.put("traceId", user.getOid() + "|" + user.getName() + "|" + Global.createUUID().substring(25));
                        RpcContext.getContext().setAttachment("traceId", user.getOid() + "|" + user.getName() + "|" + Global.createUUID().substring(25));
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
            // 记录下请求内容
            log.info("==========================CONTROLLER=============================");
            if (user != null) {
                log.info("用户信息 : [" + user.getObh() + "/" + user.getName() + "] ");
            }
            log.info("访问地址  : " + request.getRequestURL().toString());
            log.info("调用类型  : " + request.getMethod());
            log.info("客户地址  : " + CUtil.getIpAdrress(request));
            log.info("服务方法  : " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
            Object[] argObjs = joinPoint.getArgs();
            List<Object> newParams = new ArrayList<>();
            if (argObjs != null && argObjs.length > 0) {
                for (Object obj : argObjs) {
                    if (!(obj instanceof User) && obj instanceof Map) {
                        newParams.add(obj);
                    }
                }
            }
            //获取所有参数方法一：
            Enumeration<String> enu = request.getParameterNames();
            StringBuilder params = new StringBuilder();
            params.append("[");
            while (enu.hasMoreElements()) {
                String paraName = (String) enu.nextElement();
                params.append(paraName + ":" + JSON.toJSONString(request.getParameter(paraName))).append(",");
            }
            params.append("]");

            String paramsJson = null;
            if ((CUtil.isEmpty(params.toString()) || "[]".equals(params.toString())) && newParams.size() > 0) {
                paramsJson = JSONObject.toJSONString(newParams);
            } else {
                paramsJson = params.toString();
            }
            log.info("参数列表  :" + paramsJson);
        }
    }



    @Around(value = "webLog()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Object result = null;
        try {
            // 处理完请求，返回内容
            result = pjp.proceed();

            // 接收到请求，记录请求内容
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            if(!request.getRequestURL().toString().contains("/server/actuator/")) {
                String tokenParam = request.getHeader(Global.TOKENHEADER);
                if (CUtil.isEmpty(tokenParam)) {
                    tokenParam = request.getParameter(Global.TOKENHEADER);
                }
                // 记录下请求内容
                log.info("==========================CONTROLLER-RESP=============================");
                User user = redisSrv.getUserByToken(tokenParam);
                try {
                    if (user != null) {
                        //add TraceID
                        MDC.put("traceId", user.getOid() + "|" + user.getName() + "|" + Global.createUUID());
                        RpcContext.getContext().setAttachment("traceId", user.getOid() + "|" + user.getName() + "|" + Global.createUUID());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                log.info("服务方法  : " + pjp.getSignature().getDeclaringTypeName() + "." + pjp.getSignature().getName());
                Object[] argObjs = pjp.getArgs();
                List<Object> newParams = new ArrayList<>();
                if (argObjs != null && argObjs.length > 0) {
                    for (Object obj : argObjs) {
                        if (!(obj instanceof User) && obj instanceof Map) {
                            newParams.add(obj);
                        }
                    }
                }
                //获取所有参数方法一：
                Enumeration<String> enu = request.getParameterNames();
                StringBuilder params = new StringBuilder();
                params.append("[");
                while (enu.hasMoreElements()) {
                    String paraName = (String) enu.nextElement();
                    params.append(paraName + ":" + JSON.toJSONString(request.getParameter(paraName))).append(",");
                }
                params.append("]");
                log.info("响应报文  : " + JSON.toJSONString(result));
                String targetName = pjp.getTarget().getClass().getName();
                String methodName = pjp.getSignature().getName();

                String paramsJson = null;
                if ((CUtil.isEmpty(params.toString()) || "[]".equals(params.toString())) && newParams.size() > 0) {
                    paramsJson = JSONObject.toJSONString(newParams);
                } else {
                    paramsJson = params.toString();
                }
                Class targetClass = null;
                try {
                    targetClass = Class.forName(targetName);
                    Object[] arguments = pjp.getArgs();   //获得参数列表
                    Method[] method = targetClass.getMethods();
//                    String methode = "";
                    String option = "";
                    for (Method m : method) {
                        if (m.getName().equals(methodName)) {
                            Class[] tmpCs = m.getParameterTypes();
                            if (tmpCs.length == arguments.length) {
                                LogAnnotation methodCache = m.getAnnotation(LogAnnotation.class);
                                if (methodCache != null) {
//                                    methode = methodCache.moduleName();
                                    option = methodCache.option();
                                    String className = pjp.getSignature().getDeclaringTypeName().replace("com.main.handler","");
                                    String classMethod = pjp.getSignature().getName();
                                    //异步存储
                                    asyncProcesser.asyncSaveLogInfo(user, paramsJson, CUtil.getIpAdrress(request), className, classMethod, option, JSON.toJSONString(result));
                                }
                                break;
                            }
                        }
                    }
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
            }
            return result;
        } catch (Throwable e) {
            log.error("AOP异常：{}", e.getMessage());
            throw e;
        }

    }


}
