package com.domino.qh.service;

import com.domino.common.qh.document.QuestionDocument;
import com.domino.common.qh.dto.QhProcessPaperDTO;
import com.domino.common.qh.dto.QhProcessPaperDetailDTO;
import com.domino.common.qh.form.QuestionSearchForm;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 题目Elasticsearch服务接口
 */
public interface QuestionElasticSearchService {

    /**
     * 创建题目索引
     * @return 是否创建成功
     */
    boolean createQuestionIndex() throws IOException;

    /**
     * 批量保存题目到ES
     * @param paper 试卷信息（包含试卷基本信息和题目详情）
     * @return 保存成功的数量
     */
    int batchSaveQuestions(QhProcessPaperDTO paper) throws IOException;

    /**
     * 保存单个题目到ES
     * @param questionDocument 题目文档
     * @return ES文档ID
     */
    String saveQuestion(QuestionDocument questionDocument) throws IOException;

    /**
     * 根据ID删除题目
     * @param questionId 题目ID
     */
    void deleteQuestionById(String questionId) throws IOException;

    /**
     * 根据试卷名称删除所有题目
     * @param paperName 试卷名称
     */
    void deleteQuestionsByPaperName(String paperName) throws IOException;

    /**
     * 根据ID逻辑删除题目（更新isDeleted为1）
     * @param questionId 题目ID
     */
    void logicalDeleteQuestionById(String questionId) throws IOException;

    /**
     * 根据试卷名称逻辑删除所有题目（更新isDeleted为1）
     * @param paperName 试卷名称
     */
    void logicalDeleteQuestionsByPaperName(String paperName) throws IOException;

    /**
     * 根据题目ID列表批量逻辑删除题目（更新isDeleted为1）
     * @param questionIds 题目ID列表
     */
    void logicalDeleteQuestionsByIds(List<String> questionIds) throws IOException;

    /**
     * 更新题目信息
     * @param questionDocument 题目文档
     */
    void updateQuestion(QuestionDocument questionDocument) throws IOException;

    /**
     * 根据ID查询题目
     * @param questionId 题目ID
     * @return 题目文档
     */
    QuestionDocument getQuestionById(String questionId) throws IOException;

    /**
     * 检查题目索引是否存在
     * @return 是否存在
     */
    boolean isQuestionIndexExist() throws IOException;

    /**
     * 删除题目索引
     * @return 是否删除成功
     */
    boolean deleteQuestionIndex() throws IOException;

    /**
     * 搜索题目
     * @param searchForm 搜索条件
     * @return 搜索结果
     */
    Map<String, Object> searchQuestions(QuestionSearchForm searchForm) throws IOException;
}
