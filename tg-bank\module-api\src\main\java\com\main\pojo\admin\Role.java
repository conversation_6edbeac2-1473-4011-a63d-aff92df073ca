package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_role")
public class Role implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String oid;

    private String name;

    private Integer isStop;

    private String createUserOid;

    @TableField(exist = false)
    private String createUserName;

    @TableField(exist = false)
    private String[] res;
}
