package com.facade;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动生成初始代码
 * <AUTHOR>
 * 2021/12/06 9:44
 */

public class MpHelper {
	static String packageHeader = "his"; //platform, his

	public static void main(String[] args) {
		String projectPath = System.getProperty("user.dir");
		//FIXME 修改为自己的项目名和包名
		String outputDirOfApi = projectPath + "/module-api/src/main/java"; //生成文件输出目录
		String outputDirOfApp = projectPath + "/module-items/module-admin/src/main/java"; //生成文件输出目录
		String outputDirOfXML = projectPath + "/module-items/module-admin/src/main/resources"; //生成xml文件输出目录
		String author = "Generator"; //注释作者
		//FIXME 修改为自己项目表前缀
		String entityPrefix = "d_"; //前缀

		//FIXME 根据实际情况修改要生成的表
		String[] tableName = {"his_user"}; //表名多个以 , 分割

		//FIXME 是否要覆盖已有的文件，可能导致修改的内容丢失
		boolean override = false;
		//FIXME 数据库自己修改
		String dbUrl = "**************************************************************************";
		DataSourceConfig dataSourceConfig = new DataSourceConfig();
		dataSourceConfig.setDbType(DbType.MYSQL)
				.setUrl(dbUrl)
				.setUsername("test")
				.setPassword("whatup@250.")
				.setDriverName("com.mysql.cj.jdbc.Driver");
		StrategyConfig strategyConfig = new StrategyConfig();
		strategyConfig
				.setCapitalMode(true)
				.setEntityLombokModel(true) //设置是否使用 lombook
				.setNaming(NamingStrategy.underline_to_camel)
				.setColumnNaming(NamingStrategy.underline_to_camel)
				.setRestControllerStyle(true)
				.setControllerMappingHyphenStyle(true)
				.setInclude(tableName)//修改替换成你需要的表名，多个表名传数组
				.setTablePrefix(entityPrefix)
				.setSkipView(true)
		;

		MpHelper.generatorApi(override,author,outputDirOfApi,dataSourceConfig,strategyConfig);
		MpHelper.generatorApp(override,author,outputDirOfApp,dataSourceConfig,strategyConfig);
		MpHelper.generatorXml(override,author,outputDirOfXML,dataSourceConfig,strategyConfig);
	}

	private static boolean generatorApi(boolean override,String author,String outputDir,DataSourceConfig dataSourceConfig,StrategyConfig strategyConfig) {
		GlobalConfig config = new GlobalConfig();
		config.setAuthor(author);
		config.setOutputDir(outputDir);
		//FIXME 如果生成的文件已经存在，是否覆盖，文件覆盖可能导致已修改的代码被覆盖。
		config.setFileOverride(override);
		config.setOpen(false); //文件生成完是否打开目录
		config.setEnableCache(false);
		config.setServiceName("%sService");


		PackageConfig packageConfig = new PackageConfig();
		packageConfig.setParent("com");
		packageConfig.setService("main.service." + packageHeader);
		packageConfig.setEntity("main.pojo." + packageHeader);

//		packageConfig.setController(null);
//		packageConfig.setServiceImpl(null);
//		packageConfig.setXml(null);
//		packageConfig.setMapper(null);

		TemplateConfig templateConfig  = new TemplateConfig();
		templateConfig.setController(null);
		templateConfig.setServiceImpl(null);
		templateConfig.setXml(null);
		templateConfig.setMapper(null);

		AutoGenerator mpg = new AutoGenerator();

		mpg.setGlobalConfig(config);
		mpg.setDataSource(dataSourceConfig);
		mpg.setStrategy(strategyConfig);
//		mpg.setCfg(cfg);
		mpg.setPackageInfo(packageConfig);
		mpg.setTemplate(templateConfig);
		mpg.execute();
		return true;
	}

	private static boolean generatorApp(boolean override,String author,String outputDir,DataSourceConfig dataSourceConfig,StrategyConfig strategyConfig) {
		GlobalConfig config = new GlobalConfig();
		config.setAuthor(author);
		config.setOutputDir(outputDir);
		//FIXME 如果生成的文件已经存在，是否覆盖，文件覆盖可能导致已修改的代码被覆盖。
		config.setFileOverride(override);
		config.setOpen(false); //文件生成完是否打开目录
		config.setEnableCache(false);
		config.setServiceName("%sService");
		config.setControllerName("%sHandler");
		config.setServiceImplName("%sServiceImpl");
		config.setMapperName("%sMapper");
		config.setXmlName("%sMapper");

		PackageConfig packageConfig = new PackageConfig();
		packageConfig.setParent("com");
		packageConfig.setController("main.handler");
		packageConfig.setServiceImpl("main.service." + packageHeader);
		packageConfig.setService("main.service." + packageHeader);
		packageConfig.setEntity("main.pojo." + packageHeader);
		packageConfig.setMapper("main.mapper");
//		packageConfig.setXml("../../resources/mapping");
        TemplateConfig templateConfig  = new TemplateConfig();
        templateConfig.setEntity(null);
        templateConfig.setService(null);
        templateConfig.setXml(null);

		AutoGenerator mpg = new AutoGenerator();
        mpg.setTemplate(templateConfig);
		mpg.setGlobalConfig(config);
		mpg.setDataSource(dataSourceConfig);
		mpg.setStrategy(strategyConfig);
//		mpg.setCfg(cfg);
		mpg.setPackageInfo(packageConfig);
		mpg.execute();
		return true;
	}

	private static boolean generatorXml(boolean override,String author,String outputDir,DataSourceConfig dataSourceConfig,StrategyConfig strategyConfig) {
		GlobalConfig config = new GlobalConfig();
		config.setAuthor(author);
		config.setOutputDir(outputDir);
		//FIXME 如果生成的文件已经存在，是否覆盖，文件覆盖可能导致已修改的代码被覆盖。
		config.setFileOverride(override);
		config.setOpen(false); //文件生成完是否打开目录
		config.setEnableCache(false);
		config.setServiceName("%sService");
		config.setControllerName("%sHandler");
		config.setServiceImplName("%sServiceImpl");
		config.setMapperName("%sMapper");
		config.setXmlName("%sMapper");

		PackageConfig packageConfig = new PackageConfig();
		packageConfig.setParent("");
		packageConfig.setController("com.main.handler");
		packageConfig.setServiceImpl("com.main.service." + packageHeader);
		packageConfig.setService("com.main.service." + packageHeader);
		packageConfig.setEntity("com.main.pojo." + packageHeader);
		packageConfig.setMapper("com.main.mapper");
		packageConfig.setXml("mapping");

		// 自定义需要填充的字段
		List<TableFill> tableFillList = new ArrayList<>();
		//如 每张表都有一个创建时间、修改时间
		//而且这基本上就是通用的了，新增时，创建时间和修改时间同时修改
		//修改时，修改时间会修改，
		//虽然像Mysql数据库有自动更新几只，但像ORACLE的数据库就没有了，
		//使用公共字段填充功能，就可以实现，自动按场景更新了。
		//如下是配置
		//TableFill createField = new TableFill("gmt_create", FieldFill.INSERT);
		//TableFill modifiedField = new TableFill("gmt_modified", FieldFill.INSERT_UPDATE);
		//tableFillList.add(createField);
		//tableFillList.add(modifiedField);

		// 自定义配置
//		InjectionConfig cfg = new InjectionConfig() {
//			@Override
//			public void initMap() {
//				// to do nothing
//			}
//		};
//		List<FileOutConfig> focList = new ArrayList<>();
//		focList.add(new FileOutConfig("/templates/mapper.xml.ftl") {
//			@Override
//			public String outputFile(TableInfo tableInfo) {
//				// 自定义输入文件名称
//				return projectPath + "/src/main/resources/mapping/"
//						+ "/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
//			}
//		});
//		cfg.setFileOutConfigList(focList);

		TemplateConfig templateConfig  = new TemplateConfig();
		templateConfig.setEntity(null);
		templateConfig.setService(null);
		templateConfig.setServiceImpl(null);
		templateConfig.setController(null);
		templateConfig.setMapper(null);


		AutoGenerator mpg = new AutoGenerator();
		mpg.setTemplate(templateConfig);
		mpg.setGlobalConfig(config);
		mpg.setDataSource(dataSourceConfig);
		mpg.setStrategy(strategyConfig);
		mpg.setPackageInfo(packageConfig);
		mpg.execute();
		return true;
	}
}
