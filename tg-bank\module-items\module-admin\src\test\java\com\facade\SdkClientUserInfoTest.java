package com.facade;


import com.main.bean.vo.ResponseVO;
import com.common.Global;
import com.alibaba.fastjson.JSON;
import com.main.sdk.DevSdkClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试用例样例
 *
 * <AUTHOR>
 * 2019/5/23 13:47
 */
@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = UCApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class SdkClientUserInfoTest {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

//	private static final String web_pay_url = "http://114.115.173.131:8700/open-platform";
	private static final String web_pay_url = "http://127.0.0.1:8201/item-app";//本地模拟测试服务接口
	private static final String signType = "PLAIN";
	private static final String encryptType = "PLAIN";
	private static final String appId = "1A3VL0KVK0000B020A0A0000CC3F48AD";
	private static final String appSecret = "1A3VL0KVE0010B010A0A0000277BDC91";


	/**
	 * 测试-获取登录页面地址
	 */
	@Test
	public void getLoginPageUrl() {
		logger.info("开始请求");
		DevSdkClient sdkClient = new DevSdkClient(web_pay_url, appId, appSecret, signType, encryptType);
		try {

			//查看接口文档的 局部参数
			String platform = "mid-user-center"; //看接口文档platform
			String method = "user.login.url";//看接口文档 method
			String version = "1.0.0";//看接口文档version

			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("callBackUrl", "http://1146-220-250-52-237.ngrok.io/uc-app/user/test/ucCallBack");
			paramMap.put("channel", "WX_PUB");
			paramMap.put("wxAppId", "wx30cadbf99011e526");
			Map<String, Object> extParamMap = new HashMap<>();
			extParamMap.put("requestUUID", Global.createUUID());
			paramMap.put("extParamMap", extParamMap);

			// 发起交易
			ResponseVO responseParams = sdkClient.unifyCall(platform, method, version, paramMap);
			logger.info("返回的结果为：" + JSON.toJSONString(responseParams));
			logger.info("其中业务返回为：" + JSON.toJSONString(responseParams.getBizObj()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


}
