package com.config;

/**
 * <AUTHOR>
 * 2020/5/12 13:28
 */


import com.alibaba.fastjson.JSON;
import com.common.Global;
import com.common.MyAuthority;
import com.main.bean.vo.ResponseVO;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.Res;
import com.main.pojo.admin.User;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.Objects;

@Component
@Slf4j
public class AuthorizationInterceptor implements HandlerInterceptor {
    @Value("${web.login.valid:1}")
    private int enableFilter;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedisSrv redisSrv;

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        if (enableFilter == 0) {
            return true;
        } else {
            long a = System.currentTimeMillis();
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            MyAuthority authority = method.getAnnotation(MyAuthority.class);
            if (authority != null) {
                String authorityStr = authority.auth();
                String[] authorityArray = authority.auths();
                if(authorityArray == null || authorityArray.length == 0) {
                    if(CUtil.isNotEmpty(authorityStr)) {
                        authorityArray = new String[1];
                        authorityArray[0] = authorityStr;
                    }
                }
                //验证授权信息
                String tokenParam = request.getHeader(Global.TOKENHEADER);
                if (CUtil.isEmpty(tokenParam)) {
                    tokenParam = request.getParameter(Global.TOKENHEADER);
                }
                if (StringUtils.isNotBlank(tokenParam)) {
                    User user = redisSrv.getUserByToken(tokenParam);
                    if (user == null ) {
                        return this.responseFalse(response, "登录信息为空，请重新登录！");
                    }
                    //超级管理员不受限制
                    if (authorityArray != null
                            && authorityArray.length != 0
                            && !authority.ignore()
                            && Objects.requireNonNull(user).getIsAdmin() != 1) {
                        log.info("鉴权拦截器：在请求参数列表查找mToken:{}", tokenParam);
                        if (StringUtils.isNotBlank(tokenParam)) {
                            if (user.getResList() != null) {
                                for (Res resourcesInfo : user.getResList()) {
                                    for(String authStr : authorityArray) {
                                        if (CUtil.isNotEmpty(resourcesInfo.getExtra()) && resourcesInfo.getExtra().contains(authStr)) {
                                            long b = System.currentTimeMillis();
//                                            log.info("鉴权拦截器：鉴权成功！鉴权拦截器耗时：{}",b-a);
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                        return responseFalse(response, "您没有此菜单【" + JSON.toJSONString(authorityArray) + "】的权限");
                    } else {
                        long b = System.currentTimeMillis();
//                        log.info("鉴权拦截器：鉴权成功！鉴权拦截器耗时：{}",b-a);
                        return true;
                    }
                }else {
                    return responseFalse(response, "鉴权参数"+Global.TOKENHEADER+"为传递！");
                }
            } else {
                return true;
            }
        }
    }

    private boolean responseFalse(HttpServletResponse response, String error) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        ResponseVO<String> responseVO = new ResponseVO<>();
        responseVO.setRetInfo(HandlerType.SRV_NO_AUTH_ACCESS);
        responseVO.setMsg(error);
        //上传文件用的是这个
        responseVO.setError(error);
        log.info("鉴权拦截器：鉴权失败！信息:{}", JSON.toJSONString(responseVO));
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(responseVO));
        out.flush();
        out.close();
        return false;
    }

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}
