package com.main.exception;

import com.main.myenum.HandlerType;

/**
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private int errorCode;
	private String errorMessage;

	@Override
	public String toString() {
		return "错误代码："+getErrorCode()+"，错误信息："+getErrorMessage();
	}

	public int getErrorCode() {
		return errorCode;
	}



	public String getErrorMessage() {
		return errorMessage;
	}



	public BusinessException(int errorCode, String errorMessage) {
		super(errorMessage);
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
	}
	public BusinessException(HandlerType handlerType) {
		super(handlerType.getRetMsg());
		this.errorCode = handlerType.getRetCode();
		this.errorMessage = handlerType.getRetMsg();
	}



	public BusinessException(String errorMessage) {
		super(errorMessage);
		this.errorCode = HandlerType.SYSTEM_ERROR.getRetCode();
		this.errorMessage = errorMessage;
	}

	public BusinessException(String errorMessage, Exception e) {
		super(errorMessage,e);
		this.errorMessage = errorMessage;
	}



	public BusinessException(Throwable e) {
		super(e);
	}



}
