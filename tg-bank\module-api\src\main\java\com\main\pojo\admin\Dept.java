package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_dept")
public class Dept implements Serializable {

    private static final long serialVersionUID=1L;
    @TableId
    private String oid;

    private String deptName;

    @TableId
    private String orgOid;

    // 0启用 1停用
    private Integer isStop;

    private String parentId;

    @TableField(exist = false)
    private String parentName;

    private Integer isLeaf;

}
