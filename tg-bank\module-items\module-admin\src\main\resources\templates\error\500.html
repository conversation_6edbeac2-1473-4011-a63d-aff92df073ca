﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:include="/commonHead::common">
    <title>404</title><!-- Canonical SEO -->
</head>
<body>
<div id="app">

    <el-result title="系统错误" sub-title="看来我们遇到了一些问题">
        <template #icon>
            <el-image
                    th:src="@{/style/img/500.svg}"
            ></el-image>
        </template>
        <template #extra>
            <h1 style="font-size:50px; color:#409Eff; text-align: center">ERROR/500</h1>
            <!--            <el-button type="primary" size="medium">Back</el-button>-->
        </template>
    </el-result>
</div>
<script lang="ts">
    const App = {
        data() {
            return {};
        }, methods: {}
    };
    const app = Vue.createApp(App);
    app.use(ElementPlus);
    app.mount("#app");
</script>

</body>
</html>
