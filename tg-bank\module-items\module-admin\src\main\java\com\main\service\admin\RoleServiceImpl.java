package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.RoleMapper;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.RoleRes;
import com.main.pojo.admin.User;
import com.main.pojo.admin.UserRole;
import com.main.service.util.RedissLockUtil;
import com.main.util.CUtil;
import org.junit.Assert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    @Resource
    private RoleMapper mapper;

    @Resource
    private UserRoleService userRoleService;
    @Resource
    private RoleResService roleResService;

    @Override
    public IPage<Role> getPage(int pageNum, int pageSize, String keyWord, User user) {
        //获取页面查询表单参数
        Page<Role> page = new Page<>(pageNum, pageSize);
        //普通用户不能访问角色资源
        if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限访问角色资源");
        } else {
            IPage<Role> list = mapper.getPageOfRecords(page, user.getIsAdmin(), keyWord, user.getOid());
            return list;
        }
    }

    @Override
    public ResponseVO<Map<String, Object>> getSelectList(String oid, User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        JSONArray dVal = new JSONArray();
        List<String> authRoles = new ArrayList<>();
        if (CUtil.isNotEmpty(oid)) {
            //1 获取用户关联的角色
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getUserOid, oid);
            List<UserRole> userRoleList = userRoleService.list(queryWrapper);

            Map<String, String> userRoleMap = new HashMap<>();
            userRoleList.forEach(userRole -> {
                authRoles.add(userRole.getRoleOid());
                userRoleMap.put(userRole.getRoleOid(), userRole.getUserOid());
                dVal.add(userRole.getRoleOid());
            });
        }

        //2 获取所有的角色列表
        LambdaQueryWrapper<Role> queryRoleWrapper = new LambdaQueryWrapper<>();
        queryRoleWrapper.eq(Role::getIsStop, 0);
        //不是超级管理员，那就是只能选择自己有的或者自己新增的角色
        if (user.getIsAdmin() != 1) {
            queryRoleWrapper.and(w -> w.in(Role::getOid, user.getRoleList()).or().eq(Role::getCreateUserOid, user.getOid()));
        }
        queryRoleWrapper.orderByAsc(Role::getName);
        List<Role> roles = this.list(queryRoleWrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        //3处理数据
        roles.forEach(role -> {
            Map<String, Object> map = new HashMap<>();
            map.put("label", role.getName());
            map.put("value", role.getOid());
            result.add(map);
        });
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("data", result);
        retMap.put("dVal", dVal);
        response.setData(retMap);
        return response;
    }

    @Override
    @Transactional
    public ResponseVO<String> saveRole(JSONObject role, User user) {
        ResponseVO<String> response = new ResponseVO<>();
        if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限访问角色资源");
        } else {
            Role roleInfo = JSONObject.toJavaObject(role, Role.class);
            if (CUtil.isEmpty(roleInfo.getOid())) {
                //ADD
                String oid = this.getId();
                roleInfo.setOid(oid);
                roleInfo.setCreateUserOid(user.getOid());
                this.save(roleInfo);
            } else {
                //UPDATE
                roleInfo.setCreateUserOid(null);
                this.updateById(roleInfo);
            }
            //TODO 调整权限
            String[] res = roleInfo.getRes();
            List<RoleRes> relations = new ArrayList<RoleRes>();
            for (String resOne : res) {
                RoleRes roleRes = new RoleRes();
                roleRes.setRoleOid(roleInfo.getOid());
                roleRes.setResOid(resOne);
                relations.add(roleRes);
            }

            //先删除旧的关联菜单
            LambdaUpdateWrapper<RoleRes> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(RoleRes::getRoleOid, roleInfo.getOid());
            roleResService.remove(deleteWrapper);
            //保存新的角色关联菜单
            boolean flag = roleResService.saveBatch(relations);
            if (!flag) {
                response.setRetInfo(HandlerType.SYSTEM_ERROR);
                response.setMsg("数据库数据操作失败！");
            }
            return response;
        }
    }

    private String getId() {
        if(RedissLockUtil.tryLock("ROLE_ADD_KEY", 5)) {
            try {
                String oid = mapper.getAutoGeneralID();
                return CUtil.handlerId(oid, null);
            } catch (Exception e) {
                CUtil.getStackTraceString(e);
                throw new BusinessException(e.getMessage());
            } finally {
                RedissLockUtil.unlock("ROLE_ADD_KEY");
            }
        } else {
            throw new BusinessException("系统繁忙，无法获取唯一锁");
        }
    }

    @Override
    public ResponseVO<String> deleteRole(String oid, User user) {
        ResponseVO<String> response = new ResponseVO<>();
        LambdaUpdateWrapper<Role> delWrapper = new LambdaUpdateWrapper<>();
        delWrapper.eq(Role::getOid, oid);
        if (user.getIsAdmin() != 1) {
            delWrapper.eq(Role::getCreateUserOid, user.getOid());
        }
        boolean flag = this.remove(delWrapper);
        if (!flag) {
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("数据库数据操作失败！");
        }
        return response;
    }

    @Override
    public ResponseVO<String> deleteRoles(List<String> oids) {
        ResponseVO<String> response = new ResponseVO<>();
        boolean flag = this.removeByIds(oids);
        if (!flag) {
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("数据库数据操作失败！");
        }
        return response;
    }

    @Override
    public ResponseVO<String> changeStatus(String oid, String isStop) {
        Assert.assertNotNull("入参oid为空，无法修改！", oid);
        Assert.assertNotNull("入参isStop为空，无法修改！", isStop);
        ResponseVO<String> responseVO = new ResponseVO<>();
        LambdaUpdateWrapper<Role> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Role::getIsStop, isStop);
        updateWrapper.eq(Role::getOid, oid);
        boolean flag = this.update(updateWrapper);
        if (!flag) {
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg("数据库数据操作失败！");
        }
        return responseVO;
    }

    @Override
    public ResponseVO<Role> getOne(String oid) {
        Assert.assertNotNull("入参oid为空，无法修改！", oid);
        ResponseVO<Role> responseVO = new ResponseVO<>();
        Role role = this.getById(oid);
        LambdaQueryWrapper<RoleRes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RoleRes::getRoleOid, oid);
        List<RoleRes> roles = roleResService.list(queryWrapper);
        List<String> resids = new ArrayList<>();
        roles.forEach(one -> {
            resids.add(one.getResOid());
        });
        role.setRes(resids.toArray(new String[0]));
        responseVO.setData(role);
        return responseVO;
    }
}
