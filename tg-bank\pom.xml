<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.katasea</groupId>
    <artifactId>tg-bank</artifactId>
    <packaging>pom</packaging>
    <version>v1.0.0</version>
    <name>tg-bank</name>
    <description>parent pom</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.14</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <tomcat.version>9.0.98</tomcat.version>
    </properties>
    <modules>
        <module>module-sdk</module>
        <module>module-api</module>
        <module>module-actuator</module>
        <module>module-items/module-admin</module>
    </modules>
</project>
