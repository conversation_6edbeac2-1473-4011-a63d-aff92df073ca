package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.ServerMapper;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.Server;
import com.main.pojo.admin.User;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.util.CUtil;
import com.main.util.DateUtil;
import com.main.util.Global;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 云资源的服务器信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Service
public class ServerServiceImpl extends ServiceImpl<ServerMapper, Server> implements ServerService {
    @Resource
    private ServerMapper mapper;

    @Resource
    private ServiceService serviceService;

    @Override
    public IPage<Server> getPage(int pageNum, int pageSize, JSONObject params, User user) {
        //获取页面查询表单参数
        Page<Role> page = new Page<>(pageNum, pageSize);
        String cloudOid = params.getString("cloudOid");
        //普通用户不能访问角色资源
        if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限访问角色资源");
        } else {
            IPage<Server> list = mapper.getPageOfRecords(page, cloudOid, params);
            return list;
        }
    }

    @Override
    public ResponseVO<String> saveServer(JSONObject server, String userOid) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Server db = server.toJavaObject(Server.class);
        if (CUtil.isEmpty(db.getOid())) {
            //ADD
            db.setCreateTime(DateUtil.getMyTime());
            db.setCreateUser(userOid);
            db.setUpdateTime(DateUtil.getMyTime());
            db.setUpdateUser(userOid);
            db.setOid(Global.createUUID());
            this.save(db);
        } else {
            //UPDATE
            db.setUpdateTime(DateUtil.getMyTime());
            db.setUpdateUser(userOid);
            this.updateById(db);
        }

        return responseVO;
    }

    @Override
    public ResponseVO<String> deleteServer(String oid) {
        ResponseVO<String> response = new ResponseVO<>();
        if(CUtil.isNotEmpty(oid)) {
            this.removeById(oid);
            //删除服务
            serviceService.removeByServerOid(oid);
        }
        return response;
    }

    @Override
    public void removeByCloudOid(String oid) {
        LambdaQueryWrapper<Server> query = new LambdaQueryWrapper<>();
        query.eq(Server::getCloudOid, oid);
        this.remove(query);
    }
}
