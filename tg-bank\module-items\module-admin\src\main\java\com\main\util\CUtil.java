package com.main.util;

import com.alibaba.fastjson.JSONObject;
import com.common.MathUtil;
import com.main.bean.dto.StateInfo;
import com.main.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 工具类，存放大部分通用方法
 *
 * <AUTHOR>
 */
@Slf4j
public class CUtil {

	private static final RSAUtils4UC rsaUtils = new RSAUtils4UC();
	public static String capitalizeFirstLetter(String str) {
		if (str == null || str.isEmpty()) {
			return str;
		}
		return Character.toUpperCase(str.charAt(0)) + str.substring(1);
	}
	public static JSONObject rsaDecode(String params) {
		if (CUtil.isEmpty(params)) {
			throw new BusinessException("加密密文不能为空");
		}
		//使用固定key
		String encPost = null;
		try {
			rsaUtils.getKeys();
			log.info("加密串：{}", params);
			encPost = rsaUtils.decodeByPrivateKey(params);
			log.info("解密后：{}", encPost);

		} catch (Exception e) {
			log.error("设置rsa加密密钥失败了！{}", e.getMessage());
			throw new RuntimeException(e);
		}
		return JSONObject.parseObject(encPost);
	}
	public static String getCellValue(Cell cell) {
		String cellvalue;
		if (cell != null) {
			// 判断当前Cell的Type
			switch (cell.getCellType()) {
				// 如果当前Cell的Type为NUMERIC
				case HSSFCell.CELL_TYPE_NUMERIC: {
					short format = cell.getCellStyle().getDataFormat();
					if (format == 14 || format == 31 || format == 57 || format == 58) {    //excel中的时间格式
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
						double value = cell.getNumericCellValue();
						Date date = DateUtil.getJavaDate(value);
						cellvalue = sdf.format(date);
					}
					// 判断当前的cell是否为Date
					else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。
						// 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式
						Date date = cell.getDateCellValue();
						DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
						cellvalue = formater.format(date);
					} else { // 如果是纯数字
						// 取得当前Cell的数值
						cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());

					}
					break;
				}
				// 如果当前Cell的Type为STRIN
				case HSSFCell.CELL_TYPE_STRING:
					// 取得当前的Cell字符串
					cellvalue = cell.getStringCellValue().replaceAll("'", "''");
					break;
				case HSSFCell.CELL_TYPE_BLANK:
					cellvalue = null;
					break;
				// 默认的Cell值
				default: {
					cellvalue = Global.NULLSTRING;
				}
			}
		} else {
			cellvalue = "";
		}
		return cellvalue;
	}
	/**
	 * 处理临时的上传文件，例如通过excel上传数据
	 * @param file
	 * @return
	 */
	public static File uploadFileDeal(MultipartFile file) {
		String path = System.getProperty("user.dir") + Global.FILE_UPTEMPPATH;
		if (!file.isEmpty()) {
			File tempFile = new File(path);
			if (!tempFile.exists()) {tempFile.mkdirs();}
			String tou = "TempUploadDataExcel" + new Random().nextLong(); //
			String wei = file.getOriginalFilename().substring(file.getOriginalFilename().indexOf("."));
			String fileName = tou + wei;
			File targetFile = new File(path, fileName);
			try {
				file.transferTo(targetFile);
			} catch (IOException e) {
			}
			return targetFile;
		}else {
			return null;
		}
	}
	/**
	 * 把状态信息转换为表单需要的map数据
	 * @param stateInfo 状态对象
	 * @return 符合结果的map
	 */
	public static Map<String,Object> changeFormResult(StateInfo stateInfo) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("success", stateInfo.getFlag());
		map.put("message", stateInfo.getMsg());
		return map;
	}
	/**
	 * 判断是否为空字符串
	 *
	 * @param str 字符串
	 * @return true 空 false 非空
	 */
	public static boolean isEmpty(String str) {
		return str == null || "".equals(str)
				|| str.length() == 0 || "".equals(str.trim())
				|| "null".equals(str.toLowerCase())
				|| "NaN".equals(str) || "undefined".equals(str);
	}

	/**
	 * 判断是否为空对象
	 *
	 * @param obj 空对象
	 * @return true 空 false 非空
	 */
	public static boolean isEmpty(Object obj) {
		return obj == null || "".equals(String.valueOf(obj))
				|| String.valueOf(obj).length() == 0 || "".equals(String.valueOf(obj).trim())
				|| "null".equals(String.valueOf(obj).toLowerCase())
				|| "NaN".equals(String.valueOf(obj)) || "undefined".equals(String.valueOf(obj));
	}

	/**
	 * 判断是否不为空字符串
	 *
	 * @param str 字符串
	 * @return true 非空 false 空
	 */
	public static boolean isNotEmpty(String str) {
		return !CUtil.isEmpty(str);
	}

	/**
	 * 空字符串转为0
	 *
	 * @param str 字符串
	 * @return 字符串
	 */
	public static String nullToZero(String str) {
		if (CUtil.isEmpty(str)) {
			return "0";
		} else {
			return str;
		}
	}
	public static Integer nullToZero(Integer str) {
		if (CUtil.isEmpty(str)) {
			return 0;
		} else {
			return str;
		}
	}

	/**
	 * 空字符串转换为""
	 *
	 * @param str 字符串
	 * @return 字符串
	 */
	public static String nullToStr(String str) {
		if (CUtil.isEmpty(str)) {
			return Global.NULLSTRING;
		} else {
			return str;
		}
	}

	/**
	 * 判断list 是否是空的
	 *
	 * @param list 集合
	 * @return true 空 false 非空
	 */
	public static boolean isEmptyList(List<?> list) {
		return list == null || list.isEmpty();
	}

	/**
	 * 强转字符串
	 *
	 * @param object 对象
	 * @return 字符串
	 */
	public static String vo(Object object) {
		return String.valueOf(object);
	}

	/**
	 * 获取map的值，可以预防空指针问题。
	 *
	 * @param map 集合
	 * @param key 键
	 * @param <T> 泛型
	 * @return 值
	 */
	public static <T> T getMV(Map<String, T> map, String key) {
		if (map == null || map.size() == 0) {
			return null;
		} else {
			return map.get(key);
		}
	}

	public static <T> T getMV(Map<Integer, T> map, int key) {
		if (map == null || map.size() == 0) {
			return null;
		} else {
			return map.get(key);
		}
	}

	/**
	 * 把List<Map> 结果集转换为 Map<Key,Value>形式返回
	 * 例如 [{key:1,value:2}] => {1,2}
	 *
	 * @param list  集合
	 * @param key   键
	 * @param value 值
	 * @return MAP集合
	 */
	public static Map<String, String> changetListToMap(List<Map<String, Object>> list, String key, String value) {
		Map<String, String> resultMap = new HashMap<>();
		if (list != null) {
			for (Map<String, Object> map : list) {
				if (map != null)
					resultMap.put(String.valueOf(map.get(key)), String.valueOf(map.get(value)));
			}
		}
		return resultMap;
	}

	/**
	 * 把获取的数据库list 变为 map
	 * 例如获取的数据为 [{id:1,name:ff},{id:2,name:dd}] => {1:true,2:true} or {1:<T> value,2...}
	 *
	 * @param list  需要转换的集合对象
	 * @param key   list<Map<String,Object> 数据里要把哪个key的值当做新map的key。例如上面例子中的 id
	 * @param value Map的值
	 * @param <T>   泛型
	 * @return Map
	 */
	public static <T> Map<String, T> changetListToMap(List<Map<String, Object>> list, String key, T value) {
		Map<String, T> resultMap = new HashMap<>();
		if (list != null) {
			for (Map<String, Object> map : list) {
				resultMap.put(String.valueOf(map.get(key)), value);
			}
		}
		return resultMap;
	}

	/**
	 * 把List<Map> 结果集转换为 Map<Key,Object>形式返回
	 * 例如 [{key:1,value:2}] => {1,{key:1,value:2}}
	 *
	 * @param list 集合
	 * @param key  键
	 * @return MAP集合
	 */
	public static Map<String, Map<String, Object>> changeListToMap(List<Map<String, Object>> list, String key) {
		Map<String, Map<String, Object>> resultMap = new HashMap<>();
		for (Map<String, Object> map : list) {
			resultMap.put(String.valueOf(map.get(key)), map);
		}
		return resultMap;
	}


	/**
	 * 处理主键规则
	 *
	 * @param mapOfID  主键集合
	 * @param parentId 父节点
	 * @return 字符串主键
	 */
	public static String dealPKRule(Map<String, Object> mapOfID, String parentId) {
		String result;
		if (mapOfID == null) {
			if (CUtil.isEmpty(parentId)) {
				result = "1";
			} else {
				result = parentId + "01";
			}
		} else {
			result = String.valueOf(Integer.parseInt(String.valueOf(mapOfID.get("id"))) + 1);
		}
		return result;
	}


	//date = 20180910
	public static List<String> getYearMonthDayList(String date, String split) {
		if (CUtil.isEmpty(date)) {
			date = Global.year_month_day_time_no_.format(new Date());
		} else {
			date = date.replace("-", "");
		}
		int year = Integer.parseInt(date.substring(0, 4));
		String month = date.substring(4, 6);
		int day = 0;
		try {
			day = CUtil.getDays(Global.year_month_day_no_.parse(date));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		List<String> result = new ArrayList<>();
		for (int i = 1; i <= day; i++) {
			if (i < 10) {
				result.add(String.valueOf(year) + split + month + split + "0" + i);
			} else {
				result.add(String.valueOf(year) + split + month + split + i);
			}
		}
		return result;
	}

	public static List<String> getYearMonthList(String date, String split) {
		if (CUtil.isEmpty(date)) {
			date = Global.year_month_day_time_no_.format(new Date());
		} else {
			date = date.replace("-", "");
		}
		int year = Integer.parseInt(date.substring(0, 4));
		List<String> list = new ArrayList<>();
		for (int i = 1; i < 13; i++) {
			if (i < 10) {
				list.add(String.valueOf(year) + split + "0" + i);
			} else {
				list.add(String.valueOf(year) + split + i);
			}
		}
		return list;
	}

	public static int getDays(Date date) {
		Calendar cal = Calendar.getInstance(); //调用Calendar 中的方法；
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, 1); // 把时间调整为当月的第一天；
		cal.add(Calendar.MONTH, 1); // 月份调至下个月；
		cal.add(Calendar.DAY_OF_MONTH, -1); // 时间减去一天（就等于上个月的最后一天）
		int month = cal.get(Calendar.MONTH) + 1; //调取月份（月份在表示中会少 1，如：1月份得出数字是 0；
		int days = cal.get(Calendar.DAY_OF_MONTH);//调取当月的天数。
		return days;
	}

	/**
	 * DATE:[{key:"yyyyMMdd",value:"/"}]
	 * 获取日期的键值对
	 *
	 * @param date 日期
	 * @param info 信息
	 * @return
	 */
	public static Map<String, String> getDateKVList(String date, Map<String, Object> info) {
		String key = String.valueOf(info.get("key"));
		if (key.contains("dd")) {
			List<String> keyList = CUtil.getYearMonthDayList(date, Global.NULLSTRING);
			List<String> valueList = CUtil.getYearMonthDayList(date, String.valueOf(info.get("value")));
			Map<String, String> result = new HashMap<>();
			for (int i = 0; i < keyList.size(); i++) {
				result.put(keyList.get(i), valueList.get(i));
			}
			return result;
		} else if (key.contains("MM")) {
			List<String> keyList = CUtil.getYearMonthList(date, Global.NULLSTRING);
			List<String> valueList = CUtil.getYearMonthList(date, String.valueOf(info.get("value")));
			Map<String, String> result = new HashMap<>();
			for (int i = 0; i < keyList.size(); i++) {
				result.put(keyList.get(i), valueList.get(i));
			}
			return result;
		} else {
			return null;
		}
	}

	/**
	 * 判断是否是本地客户端地址
	 *
	 * @param remoteAddr
	 * @return
	 */
	public static boolean isLocalhost(String remoteAddr) {
		if ("0:0:0:0:0:0:0:1".equals(remoteAddr) || "127.0.0.1".equals(remoteAddr)) {
			return true;
		} else {
			return false;
		}
	}

	public static String formateJSON(String jsonStr) {
		if (null == jsonStr || "".equals(jsonStr)) return "";
		StringBuilder sb = new StringBuilder();
		char last = '\0';
		char current = '\0';
		int indent = 0;
		for (int i = 0; i < jsonStr.length(); i++) {
			last = current;
			current = jsonStr.charAt(i);

			//遇到{ [换行，且下一行缩进
			switch (current) {
				case '{':
				case '[':
					sb.append(current);
					sb.append('\n');
					indent++;
					addIndentBlank(sb, indent);
					break;

				//遇到} ]换行，当前行缩进
				case '}':
				case ']':
					sb.append('\n');
					indent--;
					addIndentBlank(sb, indent);
					sb.append(current);
					break;

				//遇到,换行
				case ',':
					sb.append(current);
					if (last != '\\') {
						sb.append('\n');
						addIndentBlank(sb, indent);
					}
					break;
				default:
					sb.append(current);
			}
		}
		return sb.toString();
	}

	/**
	 *      * 添加space
	 *      * @param sb
	 *      * @param indent
	 *     
	 */
	private static void addIndentBlank(StringBuilder sb, int indent) {
		for (int i = 0; i < indent; i++) {
			sb.append('\t');
		}
	}

	/**
	 * 处理带有中文的关键字。
	 *
	 * @param T       报错提示类 e.g : this.getClass();
	 * @param keyword 关键字
	 * @return 处理结果
	 */
	public static String decodeKeyWord(Class T, String keyword) {
		if (CUtil.isEmpty(keyword)) {
			keyword = Global.NULLSTRING;
		} else {
			try {
				keyword = URLDecoder.decode(keyword, "UTF-8");
			} catch (UnsupportedEncodingException e) {
				keyword = Global.NULLSTRING;
			}
			keyword = keyword.replace("\'", "%");
		}
		return keyword;
	}

	/**
	 * 按,分隔字符串并返回数组对象
	 *
	 * @param str 需要处理的字符串
	 * @return 数组对象
	 */
	public static String[] splitArray(String str) {
		if(CUtil.isEmpty(str)) {
			return null;
		}else {
			String[] array = str.split(",");
			if(array.length == 0) {
				array = new String[1];
				array[0] = str;
			}
			return array;
		}
	}

    public static String dealAuth(String header) {
		if(CUtil.isNotEmpty(header)) {
			return header.replace("Bearer ","");
		}else {
			return header;
		}
    }

	public static String getIpAdrress(HttpServletRequest request) {
		String Xip = request.getHeader("X-Real-IP");
		String XFor = request.getHeader("X-Forwarded-For");

		if (CUtil.isEmpty(XFor)) {
			XFor = request.getHeader("x-forwarded-for");
		}

		if (CUtil.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
			//多次反向代理后会有多个ip值，第一个ip才是真实ip
			int index = XFor.indexOf(",");
			if (index != -1) {
				return XFor.substring(0, index);
			} else {
				return XFor;
			}
		}
		XFor = Xip;
		if (StringUtils.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
			return XFor;
		}
		if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
			XFor = request.getHeader("Proxy-Client-IP");
		}
		if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
			XFor = request.getHeader("WL-Proxy-Client-IP");
		}
		if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
			XFor = request.getHeader("HTTP_CLIENT_IP");
		}
		if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
			XFor = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
			XFor = request.getRemoteAddr();
		}
		return XFor;
	}
	/**
	 * 打印异常信息
	 * @param e
	 * @return
	 */
	public static String getStackTraceString(Throwable e) {
		StackTraceElement[] traceElements = e.getStackTrace();
		StringBuilder traceBuilder = new StringBuilder();
		if(traceElements != null && traceElements.length > 0) {
			for(StackTraceElement traceElement : traceElements) {
				traceBuilder.append(traceElement.toString());
				traceBuilder.append("\n");
			}
		}
		log.error(traceBuilder.toString());
		return traceBuilder.toString();
	}

    public static String handlerId(String oid, String parentId) {
		try {
			if(CUtil.isEmpty(oid)) {
				if(CUtil.isEmpty(parentId) || "0".equals(parentId)) {
					//最原始第一条
					return "100";
				}else {
					//该父节点下第一条
					return parentId + "001";
				}
			}else {
				Long.parseLong(oid);
				//正常加1
				return MathUtil.cacComplex(oid + "+1", 0);
			}
		}catch (Exception e) {
			log.error("自动生成oid时候，获取菜单oid有非法字母，无法转换为纯数字！");
			throw new BusinessException("自动生成oid时候，获取菜单oid有非法字母，无法转换为纯数字！");
		}

    }

	/**
	 * SQL注入检查
	 * @param str 需要验证的字符串
	 * @return 处理后的安全字符串
	 * @throws BusinessException 如果包含SQL注入风险则抛出异常
	 */
	public static String sqlInject(String str) {
		if (isEmpty(str)) {
			return str;
		}
		// 去掉'|"|;|\字符
		str = str.replace("'", "")
				.replace("\"", "")
				.replace(";", "")
				.replace("\\", "");

		// 转换成小写
		String lowStr = str.toLowerCase();

		// SQL注入关键字
		String[] keywords = {
				"master", "truncate", "insert", "select", "delete", "update", "declare",
				"alter", "drop", "sleep", "exec", "count", "chr", "mid", "master", "or ",
				"and ", "delete", "like'", "create", "modify", "rename", "table",
				"iframe", "script", "bind", "unionall", "into", "load_file", "outfile"
		};

		// 判断是否包含非法字符
		for (String keyword : keywords) {
			if (lowStr.contains(keyword)) {
				log.error("疑似SQL注入攻击，包含非法字符: {}", keyword);
				throw new BusinessException("包含非法字符");
			}
		}

		return str;
	}

	/**
	 * 处理SQL的in查询参数,同时进行SQL注入检查
	 * @param params 要处理的参数字符串,多个值用逗号分隔
	 * @return 处理后的安全字符串,格式如: 'value1','value2'
	 * @throws BusinessException 如果包含SQL注入风险则抛出异常
	 */
	public static String handleSqlInParams(String params) {
		if (isEmpty(params)) {
			return "''";
		}
		
		// 分割参数
		String[] values = params.split(",");
		StringBuilder result = new StringBuilder();
		
		for (String value : values) {
			// 去除空格
			value = value.trim();
			
			// SQL注入检查
			value = sqlInject(value);
			
			// 拼接
			if (result.length() > 0) {
				result.append(",");
			}
			result.append("'").append(value).append("'");
		}
		
		return result.toString();
	}
}

