package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.mapper.admin.DeptMapper;
import com.main.pojo.admin.Dept;
import com.main.pojo.admin.UserOrg;
import com.main.util.BeanUtils;
import com.main.util.CUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {
    @Resource
    private UserOrgService userOrgService;

    @Resource
    private DeptMapper deptMapper;

    @Override
    public ResponseVO<Map<String, Object>> getDeptTreeData(String oid, String orgOid) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dept::getOrgOid, orgOid);
        List<Dept> deptList = this.list(queryWrapper);

        Map<String, List<Dept>> parentChildrenMap = new HashMap<>();
        Map<String, String> idNameMap = new HashMap<>();
        //先获取部门id与名称映射关系
        deptList.forEach(dept -> {
            idNameMap.put(dept.getOid(), dept.getDeptName());
        });

        deptList.forEach(dept -> {
            String parentId = dept.getParentId();
            if (CUtil.isEmpty(parentId)) {
                parentId = "0";
            }
            dept.setParentName(idNameMap.get(dept.getParentId()));
            if(CUtil.isEmpty(dept.getParentName())) {
                dept.setParentName("一级部门");
            }
            List<Dept> children = parentChildrenMap.get(parentId);
            if (children == null) children = new ArrayList<>();
            children.add(dept);
            parentChildrenMap.put(parentId, children);
        });
        List<Dept> topDeptList = parentChildrenMap.get("0");
        List<Map<String, Object>> treeData = new ArrayList<>();
        if (topDeptList != null && topDeptList.size() > 0) {
            handlerChildren(parentChildrenMap, treeData, topDeptList);
        }

        //获取当前用户已经选择的
        JSONArray dVal = new JSONArray();
        LambdaQueryWrapper<UserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userOrgLambdaQueryWrapper.eq(UserOrg::getUserOid, oid);
        userOrgLambdaQueryWrapper.eq(UserOrg::getOrgOid, orgOid);
        List<UserOrg> userOrgList = userOrgService.list(userOrgLambdaQueryWrapper);
        if (userOrgList != null) {
            userOrgList.forEach(userOrg -> dVal.add(userOrg.getDeptOid()));
        }
        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        retDataMap.put("dVal", dVal);

        response.setData(retDataMap);
        return response;
    }

    @Override
    public ResponseVO<String> saveDept(JSONObject dept) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Dept deptInfo = dept.toJavaObject(Dept.class);
        if (CUtil.isEmpty(deptInfo.getOid())) {
            //ADD
            String oid = this.getDeptOId(deptInfo.getOrgOid(), deptInfo.getParentId());
            deptInfo.setOid(oid);
            deptInfo.setIsLeaf(1);
            this.save(deptInfo);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(deptInfo.getParentId())) {
                this.updateLeaf(deptInfo.getOrgOid(), deptInfo.getParentId(), 0);
            }
        } else {
            //UPDATE
            LambdaUpdateWrapper<Dept> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Dept::getOid, deptInfo.getOid());
            updateWrapper.eq(Dept::getOrgOid, deptInfo.getOrgOid());
            this.update(deptInfo, updateWrapper);
        }

        return responseVO;
    }

    @Override
    public ResponseVO<String> saveDept(Dept deptInfo) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        if (CUtil.isEmpty(deptInfo.getOid())) {
            //ADD
            String oid = this.getDeptOId(deptInfo.getOrgOid(), deptInfo.getParentId());
            deptInfo.setOid(oid);
            deptInfo.setIsLeaf(1);
            this.save(deptInfo);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(deptInfo.getParentId())) {
                this.updateLeaf(deptInfo.getOrgOid(), deptInfo.getParentId(), 0);
            }
        } else {
            //UPDATE
            LambdaUpdateWrapper<Dept> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Dept::getOid, deptInfo.getOid());
            updateWrapper.eq(Dept::getOrgOid, deptInfo.getOrgOid());
            this.update(deptInfo, updateWrapper);
        }

        return responseVO;
    }

    @Override
    public boolean updateLeaf(String orgOid, String oid, int isLeaf) {
        LambdaUpdateWrapper<Dept> update = new LambdaUpdateWrapper<>();
        update.set(Dept::getIsLeaf, isLeaf);
        update.eq(Dept::getOid, oid);
        update.eq(Dept::getOrgOid, orgOid);
        return this.update(update);
    }

    @Override
    public String getDeptOId(String orgOid, String parentId) {
        String oid = deptMapper.getAutoGeneralID(orgOid, parentId);
        return CUtil.handlerId(oid, parentId);
    }

    @Override
    public ResponseVO<String> delete(String orgOid, String oid, String parentId) {
        ResponseVO<String> response = new ResponseVO<>();
        //删除他和他的子节点
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(Dept::getOid, oid);
        queryWrapper.eq(Dept::getOrgOid, orgOid);
        this.remove(queryWrapper);
        //判断他的老父亲是不是只有他这个儿子，如果是，就给他老父亲叶子标记为1
        if (CUtil.isNotEmpty(parentId)) {
            LambdaQueryWrapper<Dept> queryParentWrapper = new LambdaQueryWrapper<>();
            queryParentWrapper.eq(Dept::getParentId, CUtil.nullToStr(parentId));
            queryParentWrapper.eq(Dept::getOrgOid, CUtil.nullToStr(orgOid));
            List<Dept> orgs = this.list(queryParentWrapper);
            if (orgs == null || orgs.size() == 0) {
                this.updateLeaf(orgOid, parentId, 1);
            }
        }
        return response;
    }

    private List<Map<String, Object>> getOrgTreeChildrenData(Map<String, List<Dept>> parentChildrenMap, String oid) {
        List<Map<String, Object>> childrenTreeNode = new ArrayList<>();
        List<Dept> children = parentChildrenMap.get(oid);
        handlerChildren(parentChildrenMap, childrenTreeNode, children);
        return childrenTreeNode;
    }

    private void handlerChildren(Map<String, List<Dept>> parentChildrenMap, List<Map<String, Object>> childrenTreeNode, List<Dept> children) {
        if (children != null && children.size() > 0) {
            children.forEach(dept -> {
                Map<String, Object> treeNode = BeanUtils.autoBeanToMap(dept);
                treeNode.put("label", dept.getDeptName());
                treeNode.put("value", dept.getOid());
                treeNode.put("disabled", dept.getIsStop() != 0);
                if (dept.getIsLeaf() == 0) {
                    List<Map<String, Object>> childrenTreeData = getOrgTreeChildrenData(parentChildrenMap, dept.getOid());
                    treeNode.put("children", childrenTreeData);
                } else {
                    treeNode.put("children", null);
                }
                childrenTreeNode.add(treeNode);
            });
        }
    }
}
