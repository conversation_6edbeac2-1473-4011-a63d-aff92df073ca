package com.main.service.admin;

import com.main.service.util.MyHttpUtil;
import com.main.service.util.YamlUtil;
import com.main.util.CUtil;
import com.main.vo.IPEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * IP地址服务
 */
@Service
@Slf4j
public class IpAddressService {

    public static final String dbPath2 = "/config/ip2region.xdb";

    private static Searcher searcher;

    @PostConstruct
    public void init() {
        try {
            // 1、从 dbPath 中预先加载 VectorIndex 缓存，并且把这个得到的数据作为全局变量，后续反复使用。
            byte[] vIndex = Searcher.loadVectorIndexFromFile(YamlUtil.getClassResources() + dbPath2);
            // 2、使用全局的 vIndex 创建带 VectorIndex 缓存的查询对象。
            searcher = Searcher.newWithVectorIndex(YamlUtil.getClassResources() + dbPath2, vIndex);

        } catch (Exception e) {
            log.error("IP地址服务初始化异常:" + e.getMessage(), e);
        }
    }

    /**
     * 通过ip地址获取所属国家-离线方式
     *
     * @param ip IP地址
     * @return 国家信息
     */
    public String getLocationByIpOffline(String ip) throws Exception {
        // 3、查询
        return searcher.search(ip);
    }


    public IPEntity getIPMsg(String ip) {

        IPEntity msg = new IPEntity();

        try {
            if(CUtil.isNotEmpty(ip)) {

                String location = this.getLocationByIpOffline(ip);
                String[] arr = location.split("\\|");
                if(arr.length > 3) {
                    msg.setLocation(arr[arr.length -3] + arr[arr.length -2] + " " + arr[arr.length -1]);
                }else {
                    msg.setLocation(location);
                }
//            try {
//                // 在线方式 ip-api网站，获取地址
//                String resp = IpUtil4IpApi.getLocation(Iputil4Pconline.getIp());
//                JSONObject respObj = JSON.parseObject(resp);
////                {
////                    "status":"success", "country":"中国", "countryCode":"CN", "region":"FJ", "regionName":
////                    "福建省", "city":"福州市", "zip":"", "lat":26.0492, "lon":119.2906, "timezone":
////                    "Asia/Shanghai", "isp":"Chinanet", "org":"Chinanet FJ", "as":"AS4134 CHINANET-BACKBONE", "query":
////                    "************"
////                }
//                msg.setCountryName(respObj.getString("country"));
//                msg.setCountryCode(respObj.getString("countryCode"));
//                msg.setProvinceName(respObj.getString("regionName"));
//                msg.setProvinceCode(respObj.getString("region"));
//                msg.setCityName(respObj.getString("city"));
//                msg.setPostalCode(respObj.getString("city"));
//                //经度
//                msg.setLongitude(respObj.getDouble("lon"));
//                //纬度
//                msg.setLatitude(respObj.getDouble("lat"));
//            } catch (Exception e) {
//                try {
//                    // 在线方式 pconline网站，获取地址
//                    String location = Iputil4Pconline.getAddress(ip);
//                    if (CommonUtil.isNotEmpty(location)) {
//                        msg.setLocation(location);
//                    } else {
//                        throw new BusinessException("无法通过互联网获取IP所在地");
//                    }
//                } catch (Exception e2) {
//                    // 走离线的方式获取，离线方式不太准确
//                    //中国|0|福建省|福州市|电信
//                    String location = this.getLocationByIpOffline(ip);
//                    String[] arr = location.split("\\|");
//                    if(arr.length > 3) {
//                       msg.setLocation(arr[arr.length -3] + arr[arr.length -2] + arr[arr.length -1]);
//                    }else {
//                        msg.setLocation(location);
//                    }
//                }
//            }
            }

        } catch (Exception e) {
            e.printStackTrace();
            CUtil.getStackTraceString(e);
            log.error("获取ip地址失败了！{}", e.getMessage());
        }

        return msg;
    }

    public String getIpAdrress(HttpServletRequest request) {
        Map<String, String> hearderMap = MyHttpUtil.getHeaders(request);
        return getIpAdrress(hearderMap);
    }


    public String getIpAdrress(Map<String, String> headerMap) {
        if (headerMap == null) {
            headerMap = new HashMap<String, String>();
        }
        String Xip = headerMap.get("X-Real-IP");
        String XFor = headerMap.get("X-Forwarded-For");
        if (StringUtils.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = XFor.indexOf(",");
            if (index != -1) {
                return XFor.substring(0, index);
            } else {
                return XFor;
            }
        }
        XFor = Xip;
        if (StringUtils.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            return XFor;
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = headerMap.get("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = headerMap.get("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = headerMap.get("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = headerMap.get("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = headerMap.get("remoteAddr");
        }
        return XFor;
    }
}
