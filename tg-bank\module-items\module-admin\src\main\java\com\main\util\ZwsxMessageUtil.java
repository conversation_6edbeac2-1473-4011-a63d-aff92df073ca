package com.main.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName : ZwsxMessageUtil
 * @Description : 中网数信短信工具类
 * <AUTHOR> FuXingHong
 * @Date: 2020-11-11 18:17
 */
@Component
@Slf4j
public class ZwsxMessageUtil {

    public static final String FROM_TYPE = "fromType";//来源方编码
    public static final String CALLED_NUMBER = "calledNumber";//手机号码
    public static final String ORG_CODE = "orgCode";//机构编码
    public static final String RETURN_TYPE = "returnType";//返回数据格式类型
    public static final String TIMESTAMP = "timestamp";//时间戳
    public static final String CONTENT = "content";//短信内容
    public static final String KEY = "key";//秘钥
    public static final String SIGN = "统一用户中心";//签名

    public static final String SUCCESS_CODE = "100";//通信成功返回码
    public static final String SUCCESS_ERROR = "0";//业务成功返回码


    private static String zwsxMessageUrl;

    @Value("${sys.sms.ecan.webUrl}")
    public void inject(String messageUrl) {
        zwsxMessageUrl = messageUrl;
    }

    /**
     * @Description: 返回数据格式类型
     * <AUTHOR>
     * @date 2020年10月19日下午3:45:34
     * @version 1.0
     */
    public interface RETURN_TYPE_LIST{

        /**
         * json格式
         */
        public final static String JSON_FORTMAT = "1";
        /**
         * xml格式
         */
        public final static String XML_FORTMAT  = "2";
    }

    /**
     * 单条发送短信
     * @param map
     * fromType(来源方编码)
     * calledNumber(发送号码)
     * orgCode(机构编码)
     * returnType(返回类型)
     * content(发送内容)
     * timestamp(时间戳)
     * key(秘钥)
     * @return
     */
    public static String sendByOne(Map<String, String> map) {

        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        log.info("smsUrl:" + zwsxMessageUrl);
        Client client = dcf.createClient(zwsxMessageUrl);
        try {
            StringBuffer xml = new StringBuffer();
            xml.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xml.append("<data>");
            xml.append("<orgCode>" + map.get(ORG_CODE) + "</orgCode>");
            xml.append("<fromType>" + map.get(FROM_TYPE) + "</fromType>");
            xml.append("<timestamp>" + map.get(TIMESTAMP) + "</timestamp>");
            xml.append("<token>" + getSign(map) + "</token>");
            xml.append("<returnType>" + map.get(RETURN_TYPE) + "</returnType>");
            xml.append("<sign>" + "统一用户中心" + "</sign>");

            StringBuffer buffer = new StringBuffer();

            buffer.append("<calledNumber>" + map.get(CALLED_NUMBER) + "</calledNumber>");
            buffer.append("<content>" + map.get(CONTENT) + "</content>");
            buffer.append("<sendTime></sendTime>");

            xml.append("<params>" + buffer.toString()+ "</params>");

            xml.append("</data>");
//            log.info("短信平台-入参：{}", xml.toString());
            String paramXml = xml.toString();
            Object[] paramObjs = new Object[1];
            paramObjs[0] = paramXml;
            Object[] objs = client.invoke("sendByOne", paramObjs);
//            log.info("短信平台-入参：{}", (String) objs[0]);
            return (String) objs[0];
        } catch (Exception e) {

            e.printStackTrace();
        }

        return null;
    }


    /**
     * 批量发送短信
     * @param map
     * fromType(来源方编码)
     * calledNumber(发送号码，用逗号隔开，一次最多提交2000个)
     * orgCode(机构编码)
     * returnType(返回类型)
     * content(发送内容)
     * timestamp(时间戳)
     * key(秘钥)
     * @return
     */
    public static String sendByMany(Map<String, String> map) {

        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        Client client = dcf.createClient(zwsxMessageUrl);
        try {
            StringBuffer xml = new StringBuffer();
            xml.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xml.append("<data>");

            xml.append("<orgCode>" + map.get(ORG_CODE) + "</orgCode>");
            xml.append("<fromType>" + map.get(FROM_TYPE) + "</fromType>");
            xml.append("<timestamp>" + map.get(TIMESTAMP) + "</timestamp>");
            xml.append("<token>" + getSign(map) + "</token>");
            xml.append("<returnType>" + map.get(RETURN_TYPE) + "</returnType>");

            StringBuffer buffer = new StringBuffer();

            buffer.append("<calledNumber>" + map.get(CALLED_NUMBER) + "</calledNumber>");
            buffer.append("<content>" + map.get(CONTENT) + "</content>");
            buffer.append("<sendTime></sendTime>");

            xml.append("<params>" + buffer.toString()+ "</params>");

            xml.append("</data>");

            System.out.println(xml.toString());

            String paramXml = xml.toString();
            Object[] paramObjs = new Object[1];
            paramObjs[0] = paramXml;
            Object[] objs = client.invoke("sendByMany", paramObjs);
            System.out.println(objs[0]);
            return (String) objs[0];
        } catch (Exception e) {

            e.printStackTrace();
        }

        return null;
    }

    /**
     * 多内容多被叫发送短信
     * @param map
     * fromType(来源方编码)
     * orgCode(机构编码)
     * returnType(返回类型)
     * timestamp(时间戳)
     * key(秘钥)
     * content(发送内容)msisdn:手机号码集合，smsText：短信内容，格式如下：
     * {
    "content": [{
    "msisdn": ["17095960661", "18559951105"],
    "smsText": "尊敬的客户，你好，祝你新年快乐！"
    }, {
    "msisdn": ["17095960660", "18559951106"],
    "smsText": "尊敬的客户，你好，未来3天天气有雨，请你注意！"
    }]
    }
     * @return
     */
    public static String sendByManyToMany(Map<String, String> map) {

        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        Client client = dcf.createClient(zwsxMessageUrl);
        try {
            StringBuffer xml = new StringBuffer();
            xml.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xml.append("<data>");

            xml.append("<orgCode>" + map.get(ORG_CODE) + "</orgCode>");
            xml.append("<fromType>" + map.get(FROM_TYPE) + "</fromType>");
            xml.append("<timestamp>" + map.get(TIMESTAMP) + "</timestamp>");
            xml.append("<token>" + getSign(map) + "</token>");
            xml.append("<returnType>" + map.get(RETURN_TYPE) + "</returnType>");
            xml.append("<sign>" + "尤溪县总医院" + "</sign>");

            StringBuffer buffer = new StringBuffer();
            buffer.append("<content>");
            buffer.append(map.get(CONTENT));
            buffer.append("</content>");
            buffer.append("<sendTime></sendTime>");

            xml.append("<params>" + buffer.toString()+ "</params>");

            xml.append("</data>");

            System.out.println(xml.toString());

            String paramXml = xml.toString();
            Object[] paramObjs = new Object[1];
            paramObjs[0] = paramXml;
            Object[] objs = client.invoke("sendByManyToMany", paramObjs);
            System.out.println(objs[0]);
            return (String) objs[0];
        } catch (Exception e) {

            e.printStackTrace();
        }

        return null;
    }

    /**
     * 生成签名
     * @param map
     * @return
     */
    private static String getSign(Map<String,String> map){

        String orgCode = (String) map.get(ORG_CODE);
        String timestamp = (String) map.get(TIMESTAMP);
        String fromType = (String) map.get(FROM_TYPE);
        String returnType = (String) map.get(RETURN_TYPE);

        StringBuffer buffer = new StringBuffer();

        buffer.append("fromType="+fromType+"&orgCode="+orgCode+"&returnType="+returnType+"&timestamp="+timestamp);
        buffer.append("&key=" + map.get(KEY));

        try {

            String signature = MD5Encode(buffer.toString(), "UTF-8").toUpperCase();
            return signature;
        } catch (Exception e) {
            log.info("接口签名验证异常："+e.getMessage());
        }

        return null;

    }

    /**
     * md5加密
     * @param origin
     * @param charsetname
     * @return
     */
    public static String MD5Encode(String origin, String charsetname) {
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (charsetname == null || "".equals(charsetname))
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes()));
            else {
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes(charsetname)));
            }

        } catch (Exception exception) {
        }
        return resultString;
    }

    private static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n += 256;
        }

        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

    public static void main(String[] args) {
        //下发短信
        Map<String,String> map = new HashMap<>();
        map.put(ZwsxMessageUtil.RETURN_TYPE, RETURN_TYPE_LIST.JSON_FORTMAT);
        map.put(ZwsxMessageUtil.TIMESTAMP, String.valueOf(System.currentTimeMillis()));
        map.put(ZwsxMessageUtil.ORG_CODE, "fjecan");
        map.put(ZwsxMessageUtil.KEY, "47ecc26d284e437391b1ace026262e1a");
        map.put(ZwsxMessageUtil.FROM_TYPE, "URP");
        map.put(ZwsxMessageUtil.CONTENT, "12121212");
        map.put(ZwsxMessageUtil.CALLED_NUMBER, "18250321061");

        String result = ZwsxMessageUtil.sendByOne(map);
        System.out.println(result);
    }
}
