<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.RoleMapper">

    <select id="getPageOfRecords" resultType="com.main.pojo.admin.Role">
        select a.*, b.name as create_user_name from d_role a left join d_user b on a.create_user_oid = b.oid
        <where>
            <if test="isAdmin == 0">
                and create_user_oid = #{userOid}
            </if>
            <if test="keyWord != null">
                and (a.oid like concat('%', #{keyWord}, '%') or a.name like concat('%', #{keyWord}, '%') or b.name like concat('%', #{keyWord}, '%'))
            </if>
        </where>
    </select>
    <select id="getAutoGeneralID" resultType="java.lang.String">
        select max(oid) as id from d_role
    </select>
</mapper>
