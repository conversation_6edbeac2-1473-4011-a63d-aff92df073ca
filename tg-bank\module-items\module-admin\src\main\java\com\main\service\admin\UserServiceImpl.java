package com.main.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.Global;
import com.common.ZjmUtil;
import com.constant.DPConstant;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.ResMapper;
import com.main.mapper.admin.UserMapper;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.*;
import com.main.service.util.AsyncProcesser;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import com.main.util.MD5Util;
import com.main.vo.IPEntity;
import com.main.vo.MenuMetaVO;
import com.main.vo.MenuVO;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private UserOrgService userOrgService;
    @Resource
    private UserLoginRecordService userLoginRecordService;
    @Resource
    private IpAddressService ipAddressService;
    @Autowired
    private AsyncProcesser asyncProcesser;
    @Resource
    private ResMapper resMapper;
    @Resource
    private RoleResService roleResService;

    @Resource
    private ResService resService;

    @Resource
    private RedisSrv redisSrv;
    @Value("${sys.login.retry-times:3}")
    private int retryTimes;

    @Value("${sys.login.token-efftime:2}")
    private long tokenEffTime;
    @Resource
    private OrgService orgService;
    @Resource
    private DeptService deptService;

    //===============================用户功能服务接口==========================//
    @Override
    public IPage<User> getChatPage(int pageNum, int pageSize, JSONObject params, User user) {
        //获取页面查询表单参数
        Page<User> page = new Page<>(pageNum, pageSize);
        return userMapper.getPageOfChatRecords(page, params, user.getOid());
    }

    @Override
    public IPage<User> getPage(int pageNum, int pageSize, JSONObject params, User user) {
        //获取页面查询表单参数
        Page<User> page = new Page<>(pageNum, pageSize);
        JSONArray entryDay = params.getJSONArray("entryDay");
        if (entryDay != null && entryDay.size() > 0) {
            params.put("beginDay", entryDay.getString(0));
            params.put("endDay", entryDay.getString(1));
        }
        String userOid = null;
        if (user.getIsAdmin() != 1) {
            userOid = user.getOid();
        }
        return userMapper.getPageOfRecords(page, params, userOid);
    }

    @Override
    public ResponseVO<Map<String, Object>> changeStatus(String oid, String status) {
        //TODO 判断是否是超管，否则不让修改
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(User::getStatus, status);
        updateWrapper.eq(User::getOid, oid);
        this.update(updateWrapper);
        return response;
    }

    @Transactional
    @Override
    public ResponseVO<String> register(JSONObject params) {
        User user = params.toJavaObject(User.class);
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getObh, user.getObh());
        User userDB = this.getOne(wrapper);
        LambdaQueryWrapper<User> wrapperPhone = new LambdaQueryWrapper<>();
        wrapperPhone.eq(User::getPhone, user.getPhone());
        User userDB4Phone = this.getOne(wrapperPhone);
        Assert.assertNull("当前登录账号已经重复，请重新输入！", userDB);
        Assert.assertNull("当前手机号已经重复，请重新输入！", userDB4Phone);
        user.setOid(Global.createUUID());
        user.setStatus("1");
        user.setIsAdmin(-1);
        user.setPwd(Global.defaultPwd);
        user.setZjm(ZjmUtil.generateZJM(user.getName()));
        //设置密码
        user.setPwd(MD5Util.encryptWithSalt(user.getPwd()));
        this.save(user);
        //角色
        JSONArray role = new JSONArray(Collections.singletonList("1"));
        JSONArray orgList = new JSONArray(Collections.singletonList("1-1"));
        //保存角色
        userRoleService.mySave(user.getOid(), role);
        //保存机构
        userOrgService.mySave(user.getOid(), orgList);
        return new ResponseVO<>();
    }

    @Transactional
    @Override
    public ResponseVO<String> mySave(JSONObject params, User loginUser) {
        User user = params.toJavaObject(User.class);
        ResponseVO<String> response = new ResponseVO<>();

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getObh, user.getObh());
        User userDB = this.getOne(wrapper);
        LambdaQueryWrapper<User> wrapperPhone = new LambdaQueryWrapper<>();
        wrapperPhone.eq(User::getPhone, user.getPhone());
        User userDB4Phone = this.getOne(wrapperPhone);
        //权限控制
        if (loginUser.getIsAdmin() == 0) {
            user.setIsAdmin(-1);
        } else if (loginUser.getIsAdmin() == -1) {
            throw new BusinessException("当前用户无权限新增用户，请联系管理员新增！");
        }
        //新增
        if (CUtil.isEmpty(user.getOid())) {
            user.setOid(Global.createUUID());
            Assert.assertNull("当前登录账号已经重复，请重新输入！", userDB);
            Assert.assertNull("当前手机号已经重复，请重新输入！", userDB4Phone);
            //判断登录账号是否重复
            //处理密码
            if (CUtil.isEmpty(user.getPwd())) {
                user.setPwd(Global.defaultPwd);
            }
            user.setZjm(ZjmUtil.generateZJM(user.getName()));
            log.info("初始化默认密码为：{}", user.getPwd());
            //设置密码
            user.setPwd(MD5Util.encryptWithSalt(user.getPwd()));
            this.save(user);
        } else {
            if (userDB == null || !userDB.getOid().equals(user.getOid())) {
                response.setRetInfo(HandlerType.OUT_SYSTEM_ERROR);
                response.setMsg("无法修改用户，主索引与数据库不符！");
            } else if (userDB4Phone != null && !userDB4Phone.getOid().equals(user.getOid())) {
                response.setRetInfo(HandlerType.OUT_SYSTEM_ERROR);
                response.setMsg("无法修改用户，手机号已重复，请重新输入！");
            } else {
                user.setPwd(userDB.getPwd());
                user.setZjm(ZjmUtil.generateZJM(user.getName()));
                this.updateById(user);
            }
        }
        //角色
        JSONArray role = params.getJSONArray("roles");
        JSONArray orgList = params.getJSONArray("orgs");
        //保存角色
        userRoleService.mySave(user.getOid(), role);
        //保存机构
        userOrgService.mySave(user.getOid(), orgList);
        return response;
    }

    @Override
    public ResponseVO<Object> resetPwd(User user, String oid) {
        // 判断是不是机构管理员获取超级管理员
        if(user.getIsAdmin() == -1) {
            throw new BusinessException("机构管理员和超级管理员才可以重置密码");
        }
        JSONObject params = new JSONObject();
        params.put("keyWord", oid);
        // 判断这个人是不是归这个机构管理员管辖
        IPage<User> userList = this.getPage(0,20,params,user);
        if(userList.getTotal() > 0) {
            LambdaUpdateWrapper<User> update = new LambdaUpdateWrapper<>();
            update.set(User::getPwd, MD5Util.encryptWithSalt(Global.defaultPwd));
            update.eq(User::getOid, oid);
            this.update(update);
        }else {
            throw new BusinessException("该机构下未找到该员工，请重试！");
        }
        return new ResponseVO<>();
    }


    @Transactional
    @Override
    public ResponseVO<Object> uptPwd(String oid, User userPage) {
        if(!oid.equals(userPage.getOid())) {
            throw new BusinessException("修改的用户信息与登录凭证不匹配！");
        }
        User userDB = this.getById(oid);
        if(!userDB.getPwd().equals( MD5Util.encryptWithSalt(userPage.getOriPwd()))) {
            throw new BusinessException("原始密码输入错误，请重试！");
        }
        if(!userPage.getNewPwd().equals(userPage.getRepPwd())) {
            throw new BusinessException("两次密码输入不一致，无法设置密码！");
        }
        LambdaUpdateWrapper<User> update = new LambdaUpdateWrapper<>();
        update.set(User::getPwd, MD5Util.encryptWithSalt(userPage.getNewPwd()));
        if(CUtil.isNotEmpty(userPage.getBirthday())) {
            update.set(User::getBirthday, userPage.getBirthday());
        }
        if(CUtil.isNotEmpty(userPage.getEmail())) {
            update.set(User::getEmail, userPage.getEmail());
        }
        update.eq(User::getOid, oid);
        this.update(update);
        return new ResponseVO<>();
    }

    @Transactional
    @Override
    public ResponseVO<String> delete(String userOid) {
        ResponseVO<String> response = new ResponseVO<>();
        boolean b = false;
        if (userOid.contains(",")) {
            String[] oidArray = CUtil.splitArray(userOid);
            Assert.assertNotNull("当前数据为空，请勾选对应的数据进行删除！", oidArray);
            b = this.removeByIds(new ArrayList<>(Arrays.asList(oidArray)));
        } else {
            b = this.removeById(userOid);
        }
        if (!b) {
            response.setRetInfo(HandlerType.OUT_SYSTEM_ERROR);
            response.setMsg("未找到对应记录，删除0行");
        } else {
            //删除对应的角色信息
            userRoleService.delete(userOid);
            userOrgService.delete(userOid);
        }
        return response;
    }

    //==============================登录相关接口含菜单=========================//
    @Override
    public ResponseVO<Object> doLoginWithPwd(Map<String, String> headers, String username, String password) {

        Assert.assertNotNull("输入密码不能为空！", password);
        Assert.assertNotNull("输入账号不能为空！", username);

        //密钥加盐
        password = MD5Util.encryptWithSalt(CUtil.nullToStr(password));

        //校验连续输入错误的密码和短信验证码失败的次数是否大于约定的最大值，如果是就报错
        int failTimes = redisSrv.checkRetryTimes(username, retryTimes);

        //访问数据库操作放下面，失败次数过多的，不要浪费数据库资源

        LambdaQueryWrapper<User> queryUser = new LambdaQueryWrapper<>();
        queryUser.eq(User::getObh, username);
        User userDB = this.getOne(queryUser);
        if (userDB == null || !password.equals(userDB.getPwd())) {
            redisSrv.addRetryTimes(username, ++failTimes);
            //解决0次机会提示问题。
            redisSrv.checkRetryTimes(username, retryTimes);
            throw new BusinessException(HandlerType.OUT_SYSTEM_ERROR.getRetCode(),
                    "账号或密码不匹配，请重新输入！您还剩下" + (retryTimes - failTimes) + "次机会");
        } else {
            ResponseVO<Object> responseVO = new ResponseVO<>();
            if (userDB.getIsAdmin() == 1) {
                userDB.addRole("admin");
            }
            //登录成功！
            if (userDB.getStatus().equals(DPConstant.STATUS_LOCK)) {
                responseVO.setRetInfo(HandlerType.SRV_LOGIN_LOCK);
            } else if (userDB.getStatus().equals(DPConstant.STATUS_OFF)) {
                responseVO.setRetInfo(HandlerType.SRV_LOGIN_STOP);
            } else if (userDB.getStatus().equals(DPConstant.STATUS_NORMAL)) {
                String token = Global.createUUID();
                userDB.setToken(token);
                responseVO = this.reloadLoginUserInfo(responseVO, userDB);
                //登录记录保存
                String remoteAddr = ipAddressService.getIpAdrress(headers);
                UserAgent userAgent = UserAgent.parseUserAgentString(headers.get("User-Agent"));
                Browser browser = userAgent.getBrowser();
                OperatingSystem os = userAgent.getOperatingSystem();
                IPEntity ipEntity = ipAddressService.getIPMsg(remoteAddr);
                ipEntity.setBrowser(browser.getName());
                ipEntity.setOsName(os.getName());
                ipEntity.setIpAddress(remoteAddr);
                asyncProcesser.handlerUserLoginInfo(this, ipEntity, userDB);
            } else {
                responseVO.setRetInfo(HandlerType.SRV_LOGIN_UNKOWN_STATUS);
            }
            return responseVO;
        }
    }

    private ResponseVO<Object> reloadLoginUserInfo(ResponseVO<Object> responseVO, User userDB) {
        Map<String, String> body = new HashMap<>();
        //获取菜单
        getMenu(userDB);
        //获取角色id
        List<Role> roles = userRoleService.getUserRoleList(userDB.getOid());
        List<String> roleIdList = new ArrayList<>();
        roles.forEach(role -> {
            roleIdList.add(role.getOid());
        });
        userDB.setRoleList(roleIdList);
        userDB.setRoleInfoList(roles);
        //获取单位
        userDB.setOrgList(userOrgService.getUserOrgList(userDB.getOid()));
        //判断机构列表是不是空
        if (userDB.getOrgList() == null || userDB.getOrgList().isEmpty()) {
            responseVO.setRetInfo(HandlerType.ORG_NO_RELA);
            return responseVO;
        }
        //获取当前选择的单位
        AtomicReference<Org> orgChoose = new AtomicReference<>();
        userDB.getOrgList().forEach(org -> {
            if (org.getChoose() == 1) {
                orgChoose.set(JSON.parseObject(JSON.toJSONString(org), Org.class));
            }
        });
        if (orgChoose.get() == null) {
            //都没有设置的话，随机取第一个机构。
            userDB.setCurrentLoginOrg(JSON.parseObject(JSON.toJSONString(userDB.getOrgList().get(0)), Org.class));
        } else {
            userDB.setCurrentLoginOrg(orgChoose.get());
        }

        //放redis
        redisSrv.setToken(userDB, tokenEffTime);
        body.put("token", userDB.getToken());
        responseVO.setData(body);
        return responseVO;
    }

    @Override
    public ResponseVO<User> getUserInfo(User user) {
        //FIXME 后续处理的逻辑
        return new ResponseVO<>(user);
    }

    @Override
    public ResponseVO<User> logout(String token) {
        Assert.assertNotNull("请求未传递鉴权头信息，无法获取鉴权信息", token);
        redisSrv.delete(DPConstant.RK_DP_TOKEN + token);
        return new ResponseVO<>();
    }

    @Override
    public ResponseVO<List<MenuVO>> getMenu(User user) {
        //获取登录用户菜单信息列表
        List<Res> resources = null;
        String s = "[\n" +
                "  {\n" +
                "    \"oid\": \"100\",\n" +
                "    \"name\": \"管理模块\",\n" +
                "    \"res_url\": \"/admin\",\n" +
                "    \"parent_id\": \" \",\n" +
                "    \"is_leaf\": \"0\",\n" +
                "    \"sort\": 1000,\n" +
                "    \"extra\": \"LAYOUT\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"admin\",\n" +
                "    \"icon\": \"view-list\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100001\",\n" +
                "    \"name\": \"用户管理\",\n" +
                "    \"res_url\": \"user\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 2000,\n" +
                "    \"extra\": \"/admin/user/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"AdminUser\",\n" +
                "    \"icon\": \"calendar\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100002\",\n" +
                "    \"name\": \"机构管理\",\n" +
                "    \"res_url\": \"org\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 1,\n" +
                "    \"extra\": \"/admin/org/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"AdminOrg\",\n" +
                "    \"icon\": \"fork\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100003\",\n" +
                "    \"name\": \"资源管理\",\n" +
                "    \"res_url\": \"menu\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 1,\n" +
                "    \"extra\": \"/admin/menu/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"AdminMenu\",\n" +
                "    \"icon\": \"discount\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100004\",\n" +
                "    \"name\": \"角色管理\",\n" +
                "    \"res_url\": \"role\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 2,\n" +
                "    \"extra\": \"/admin/role/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"AdminRole\",\n" +
                "    \"icon\": \"usergroup\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100005\",\n" +
                "    \"name\": \"全局字典\",\n" +
                "    \"res_url\": \"dic\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 6000,\n" +
                "    \"extra\": \"/admin/dic/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"AdminDic\",\n" +
                "    \"icon\": \"no-expression\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"100006\",\n" +
                "    \"name\": \"通知中心\",\n" +
                "    \"res_url\": \"notification\",\n" +
                "    \"parent_id\": \"100\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 0,\n" +
                "    \"extra\": \"/notification/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"Notification\",\n" +
                "    \"icon\": \"sound-high\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"101\",\n" +
                "    \"name\": \"服务监控\",\n" +
                "    \"res_url\": \"/server\",\n" +
                "    \"parent_id\": \"\",\n" +
                "    \"is_leaf\": \"0\",\n" +
                "    \"sort\": 6000,\n" +
                "    \"extra\": \"LAYOUT\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"server\",\n" +
                "    \"icon\": \"desktop-1\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"101001\",\n" +
                "    \"name\": \"本机概览仪表盘\",\n" +
                "    \"res_url\": \"base\",\n" +
                "    \"parent_id\": \"101\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 7000,\n" +
                "    \"extra\": \"/server/base/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"ServerBase\",\n" +
                "    \"icon\": \"server\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"101002\",\n" +
                "    \"name\": \"云服务器管理\",\n" +
                "    \"res_url\": \"cloud\",\n" +
                "    \"parent_id\": \"101\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 8000,\n" +
                "    \"extra\": \"/server/cloud/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"CloudList\",\n" +
                "    \"icon\": \"data\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102\",\n" +
                "    \"name\": \"诊所诊疗\",\n" +
                "    \"res_url\": \"/his\",\n" +
                "    \"parent_id\": \"\",\n" +
                "    \"is_leaf\": \"0\",\n" +
                "    \"sort\": 1,\n" +
                "    \"extra\": \"LAYOUT\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"his\",\n" +
                "    \"icon\": \"logo-adobe-illustrate\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102001\",\n" +
                "    \"name\": \"库存管理\",\n" +
                "    \"res_url\": \"Inventory\",\n" +
                "    \"parent_id\": \"102\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 4,\n" +
                "    \"extra\": \"/his/Inventory/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"HisInventory\",\n" +
                "    \"icon\": \"shop\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102002\",\n" +
                "    \"name\": \"医嘱执行\",\n" +
                "    \"res_url\": \"Execute\",\n" +
                "    \"parent_id\": \"102\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 3,\n" +
                "    \"extra\": \"/his/Execute/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"HisExecute\",\n" +
                "    \"icon\": \"gesture-left-slip\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102003\",\n" +
                "    \"name\": \"工作台\",\n" +
                "    \"res_url\": \"Workbench\",\n" +
                "    \"parent_id\": \"102\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 0,\n" +
                "    \"extra\": \"/his/Workbench/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"HisWorkbench\",\n" +
                "    \"icon\": \"app\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102004\",\n" +
                "    \"name\": \"患者档案\",\n" +
                "    \"res_url\": \"Patient\",\n" +
                "    \"parent_id\": \"102\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 1,\n" +
                "    \"extra\": \"/his/Patient/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"HisPatient\",\n" +
                "    \"icon\": \"user\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"102005\",\n" +
                "    \"name\": \"门诊接诊\",\n" +
                "    \"res_url\": \"Outpatient\",\n" +
                "    \"parent_id\": \"102\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 2,\n" +
                "    \"extra\": \"/his/Outpatient/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"HisOutpatient\",\n" +
                "    \"icon\": \"undertake-hold-up\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"103\",\n" +
                "    \"name\": \"日常助手\",\n" +
                "    \"res_url\": \"/stock\",\n" +
                "    \"parent_id\": \"\",\n" +
                "    \"is_leaf\": \"0\",\n" +
                "    \"sort\": 0,\n" +
                "    \"extra\": \"LAYOUT\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"stock\",\n" +
                "    \"icon\": \"notification\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"103001\",\n" +
                "    \"name\": \"股行总览\",\n" +
                "    \"res_url\": \"dashboard\",\n" +
                "    \"parent_id\": \"103\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 0,\n" +
                "    \"extra\": \"/stock/dashboard/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"dashboard\",\n" +
                "    \"icon\": \"chart-line-data-1\",\n" +
                "    \"is_stop\": 0\n" +
                "  },\n" +
                "  {\n" +
                "    \"oid\": \"103002\",\n" +
                "    \"name\": \"自选股\",\n" +
                "    \"res_url\": \"watchlist\",\n" +
                "    \"parent_id\": \"103\",\n" +
                "    \"is_leaf\": \"1\",\n" +
                "    \"sort\": 0,\n" +
                "    \"extra\": \"/stock/watchlist/index\",\n" +
                "    \"is_btn\": 0,\n" +
                "    \"front_name\": \"watchlist\",\n" +
                "    \"icon\": \"gesture-applause\",\n" +
                "    \"is_stop\": 0\n" +
                "  }\n" +
                "]";
        resources = JSON.parseArray(s, Res.class);
//        if (user.getIsAdmin() == 1) {
//            resources = resMapper.getResources();
//        } else {
//            resources = resMapper.getResourcesByUserid(user.getOid());
//        }
        //处理一下资源列表，把没有用的父节点去掉。
        handler(resources);

        List<MenuVO> menuVOList = new ArrayList<>();
        Map<String, List<MenuVO>> parent_children_rela_map = new HashMap<>();
        resources.forEach(res -> {
            MenuVO menu = translate(res);
            List<MenuVO> children = parent_children_rela_map.get(res.getParentId());
            if (children == null) {
                children = new ArrayList<>();
            }
            children.add(menu);
            parent_children_rela_map.put(res.getParentId(), children);

            menuVOList.add(menu);
        });

        List<MenuVO> finalMenuVOList = new ArrayList<>();
        menuVOList.forEach(menu -> {
            if (CUtil.isEmpty(menu.getParentId())) {
                if (menu.getLeaf() != 1) {
                    List<MenuVO> children = getChildrenInfo(parent_children_rela_map, menu.getOid());
                    if(!children.isEmpty()) {
                        menu.setRedirect(menu.getPath()+"/" + children.get(0).getPath());
                    }
                    menu.setChildren(children);
                } else {
                    menu.setChildren(null);
                }
                finalMenuVOList.add(menu);
            }

        });
        //更新权限
        user.setResList(resources);
        redisSrv.setToken(user, tokenEffTime);
        log.info("菜单返回：{}", JSON.toJSONString(finalMenuVOList));
        return new ResponseVO<>(finalMenuVOList);
    }

    @Override
    public ResponseVO<User> switchOrg(String orgOid, HttpServletRequest request, User user) {
        Assert.assertNotNull("传递的机构id为空，请重新尝试！", orgOid);
        List<Org> orgs = user.getOrgList();

        Org currentOrg = null;
        for (Org org : orgs) {
            if(CUtil.isNotEmpty(org.getOid()) && orgOid.equals(org.getOid())) {
                //这里不这么写，会有问题，必须新建一个对象，否则原来列表的对象会变空。
                currentOrg = JSON.parseObject(JSON.toJSONString(org), Org.class);
            }
        }
        if(currentOrg == null) {
            throw new BusinessException("未找到对应的机构ID,请重新登录系统后重试！");
        }
        if(userOrgService.validateOrg(currentOrg)) {
            currentOrg.setChoose(1);
            if (userOrgService.setDefaultLoginOrg(orgOid, user.getOid())) {
                user.setCurrentLoginOrg(currentOrg);
                redisSrv.setToken(user, tokenEffTime);
                return new ResponseVO<>(user);
            } else {
                throw new BusinessException("更新用户机构默认登录标识失败！");
            }
        } else {
            throw new BusinessException(HandlerType.ORG_NO_PAY);
        }
    }

    // {managerTeam:[users],loginRecord:[userLoginRecord]}
    @Override
    public ResponseVO<Object> getLoginRecordAndManager(User user) {
        List<UserLoginRecord> records = userLoginRecordService.getUserLoginRecords(user.getOid());
        List<User> managers = this.getOrgManagers(user.getCurrentLoginOrg().getOid());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("userLoginRecord", records);
        resultMap.put("managerTeam",managers);
        return new ResponseVO<>(resultMap);
    }

    /**
     * 获取机构在线成员
     * @param orgs 10101,10002
     * @return
     */
    public List<User> getOrgOnlineUser(String orgs) {
        // SQL注入检查并格式化参数
        String safeOrgs = CUtil.handleSqlInParams(orgs);
        List<User> users = userMapper.getOrgOnlineUser(safeOrgs);
        return users;
    }


    @Override
    public List<User> getOrgManagers(String orgOid) {
        List<User> orgManagers = userMapper.getOrgManagers(orgOid);
        return orgManagers;
    }


    private List<Res> handler(List<Res> resources) {
        Map<String, String> leafParent = new HashMap<>();
        Map<String, Boolean> hadPut = new HashMap<>();
        for (Res resource : resources) {
            leafParent.put(resource.getOid(), resource.getParentId());
            if (resource.getIsLeaf() == 1) {
                //叶子节点先放进去
                hadPut.put(resource.getOid(), true);
                //涉及该子节点的父节点都记录下来
                this.getParent(hadPut, leafParent, resource.getOid());
            }
        }
        //这样可以保障数据
        for (int i = resources.size() - 1; i >= 0; i--) {
            if (hadPut.get(resources.get(i).getOid()) == null) {
                resources.remove(i);
            }
        }
        return resources;
    }

    private void getParent(Map<String, Boolean> hadPut, Map<String, String> leafParent, String leafId) {
        String parentId = leafParent.get(leafId);
        while (CUtil.isNotEmpty(parentId)) {
            hadPut.putIfAbsent(parentId, true);
            parentId = leafParent.get(parentId);
        }
    }

    private List<MenuVO> getChildrenInfo(Map<String, List<MenuVO>> parent_children_rela_map, String parentId) {
        List<MenuVO> children = parent_children_rela_map.get(parentId);
        if (children != null) {
            children.forEach(menu -> {
                if (menu.getLeaf() != 1) {
                    List<MenuVO> subChildren = getChildrenInfo(parent_children_rela_map, menu.getOid());
                    menu.setChildren(subChildren);
                } else {
                    menu.setChildren(null);
                }
            });
        }
        return children;
    }

    private MenuVO translate(Res res) {
        MenuVO menu = new MenuVO();
        menu.setName(res.getFrontName());
        menu.setPath(res.getResUrl());
        menu.setOid(res.getOid());
        menu.setComponent(res.getExtra());
        menu.setParentId(res.getParentId());
        menu.setLeaf(res.getIsLeaf());
        MenuMetaVO menuMetaVO = new MenuMetaVO();
        JSONArray roles = new JSONArray();
        roles.add("*");
//        menuMetaVO.setIcon(CUtil.capitalizeFirstLetter(res.getIcon()));
        menuMetaVO.setIcon(res.getIcon());
        menuMetaVO.setLocale(res.getName());
        menuMetaVO.setTitle(res.getName());
        menuMetaVO.setOrder(res.getSort());
        menuMetaVO.setRoles(roles);
        menuMetaVO.setRequiresAuth(true);
        menuMetaVO.setKeepAlive(true);
        menu.setMeta(menuMetaVO);

        return menu;
    }
}
