package com.frame;

import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DruidConfig {
	@Value("${druid.loginUsername}")
    private String loginUsername;
	@Value("${druid.loginPassword}")
	private String loginPassword;
//    @Bean
//    public ServletRegistrationBean druidServlet() {
//
//        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new StatViewServlet(), "/druid/*");
//        //登录查看信息的账号密码.
//
//        servletRegistrationBean.addInitParameter("loginUsername",loginUsername);
//
//        servletRegistrationBean.addInitParameter("loginPassword",loginPassword);
//        return servletRegistrationBean;
//    }

    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new WebStatFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,*.ttf,*.woff2,*.htm,*.map,/druid/*");
        return filterRegistrationBean;
    }
}
