package com.domino.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.domino.common.constant.ChatConstants;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.exception.ServiceException;
import com.domino.common.utils.DateUtils;
import com.domino.common.utils.StringUtils;
import com.domino.common.utils.spring.SpringUtils;
import com.domino.common.utils.uuid.IdUtils;
import com.domino.common.qh.domain.SysMessage;
import com.domino.common.qh.domain.SysMessageReceiver;
import com.domino.system.mapper.SysMessageMapper;
import com.domino.system.mapper.SysMessageReceiverMapper;
import com.domino.system.service.ISysMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
public class SysMessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements ISysMessageService {

    @Resource
    private SysMessageReceiverMapper messageReceiverMapper;
    
    @Resource
    private SysMessageMapper messageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendMessage(SysMessage message, SysUser user) {
        // 权限校验
        if (!user.isAdmin()) {
            // 非超级管理员只能发本机构消息
            if (ChatConstants.RECEIVE_SCOPE_ALL.equals(message.getReceiveScope())) {
                throw new ServiceException("非管理员，没有发送全部机构消息的权限");
            }
            if ((ChatConstants.RECEIVE_SCOPE_ORG.equals(message.getReceiveScope()) ||
                 ChatConstants.RECEIVE_SCOPE_CURRORG.equals(message.getReceiveScope()))) {
                throw new ServiceException("越权访问，没有发送机构通知的权限");
            }
        }

        if(ChatConstants.MSG_TYPE_CHAT.equals(message.getType())) {
            message.setReceiveScope(ChatConstants.RECEIVE_SCOPE_USER);
            // 同步删除自己发送的图片，只保留3天图片内容防止内容过多
            this.removeChatPic(user.getUserId().toString(), message.getReceiveTarget());
        }

        // 设置消息基本信息
        message.setMessageId(IdUtils.fastSimpleUUID());
        message.setSenderId(user.getUserId().toString());
        message.setSenderName(user.getNickName());
        //message.setOrgId(user.getDeptId().toString());
        message.setCreateTime(new Date());
        message.setDelFlag(ChatConstants.DEL_FLAG_NORMAL);
        
        // 保存消息
        messageMapper.insertMessage(message);
        return AjaxResult.success("发送成功", message.getMessageId());
    }

    @Override
    public AjaxResult pageMessages(int pageNum, int pageSize, String type,
                                   String keyword, Integer readStatus, String userOid, SysUser user) {
        Page<SysMessage> page = new Page<>(pageNum, pageSize);
        IPage<SysMessage> messagePage = messageMapper.pageMessages(page, type, keyword, readStatus, userOid, user);
        
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        dataTable.setRows(messagePage.getRecords());
        dataTable.setTotal(messagePage.getTotal());
        
        return AjaxResult.success(dataTable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult markAsRead(List<String> messageIds, SysUser user) {
        // 创建已读记录
        for (String messageId : messageIds) {
            // 检查是否已经标记为已读
            if (!messageReceiverMapper.checkMessageRead(messageId, user.getUserId().toString())) {
                SysMessageReceiver receiver = new SysMessageReceiver();
                receiver.setReceiverId(IdUtils.fastSimpleUUID());
                receiver.setMessageId(messageId);
                receiver.setUserId(user.getUserId().toString());
                receiver.setReadTime(new Date());
                receiver.setCreateTime(new Date());
                messageReceiverMapper.insertMessageReceiver(receiver);
            }
        }

        return AjaxResult.success("标记成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult markAllAsRead(String type, String userOid, SysUser user) {
        // 聊天消息标记已读
        if(ChatConstants.MSG_TYPE_CHAT.equals(type)) {
            LambdaUpdateWrapper<SysMessage> update = new LambdaUpdateWrapper<>();
            update.set(SysMessage::getChatReadStatus, ChatConstants.READ_STATUS_READ);
            update.eq(SysMessage::getSenderId, userOid);
            update.eq(SysMessage::getReceiveTarget, user.getUserId().toString());
            update.eq(SysMessage::getChatReadStatus, ChatConstants.READ_STATUS_UNREAD);
            this.update(update);
        } else {
            // 获取已读消息ID列表
            SysMessageReceiver queryReceiver = new SysMessageReceiver();
            queryReceiver.setUserId(user.getUserId().toString());
            List<String> readMessageIds = messageReceiverMapper.selectMessageReceiverList(queryReceiver)
                    .stream().map(SysMessageReceiver::getMessageId).collect(Collectors.toList());

            // 查询未读消息
            LambdaQueryWrapper<SysMessage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.and(wrapper ->
                wrapper.eq(SysMessage::getReceiveScope, ChatConstants.RECEIVE_SCOPE_ALL)
                    .or().and(w -> w.eq(SysMessage::getReceiveScope, ChatConstants.RECEIVE_SCOPE_ORG)
                        .like(SysMessage::getReceiveTarget, user.getDeptId().toString()))
                    .or().and(w -> w.eq(SysMessage::getReceiveScope, ChatConstants.RECEIVE_SCOPE_USER)
                        .eq(SysMessage::getReceiveTarget, user.getUserId().toString()))
            );
            queryWrapper.eq(SysMessage::getDelFlag, ChatConstants.DEL_FLAG_NORMAL);
            if (!readMessageIds.isEmpty()) {
                queryWrapper.notIn(SysMessage::getMessageId, readMessageIds);
            }

            List<SysMessage> unreadMessages = this.list(queryWrapper);

            // 批量创建已读记录
            List<SysMessageReceiver> receiversToInsert = new ArrayList<>();
            Date now = new Date();

            for (SysMessage message : unreadMessages) {
                // 检查是否已经标记为已读
                if (!messageReceiverMapper.checkMessageRead(message.getMessageId(), user.getUserId().toString())) {
                    SysMessageReceiver receiver = new SysMessageReceiver();
                    receiver.setReceiverId(IdUtils.fastSimpleUUID());
                    receiver.setMessageId(message.getMessageId());
                    receiver.setUserId(user.getUserId().toString());
                    receiver.setReadTime(now);
                    receiver.setCreateTime(now);
                    receiversToInsert.add(receiver);
                }
            }

            // 批量插入已读记录
            if (!receiversToInsert.isEmpty()) {
                messageReceiverMapper.batchInsertMessageReceiver(receiversToInsert);
            }
        }

        return AjaxResult.success("标记成功");
    }

    @Override
    public AjaxResult getUnreadCount(SysUser user) {
        Map<String, Map<String, BigDecimal>> result = messageMapper.getUnreadCount(user);
        Map<String, Integer> finalResult = new HashMap<>();
        
        BigDecimal unreadCount = getMapValue(getMapValue(result, "SYSTEM"), "nums");
        int unreadCountInt = unreadCount != null ? unreadCount.intValue() : 0;

        BigDecimal unreadChatCount = getMapValue(getMapValue(result, "CHAT"), "chatNums");
        int unreadChatCountInt = unreadChatCount != null ? unreadChatCount.intValue() : 0;

        finalResult.put("unreadCount", unreadCountInt);
        finalResult.put("unreadChatCount", unreadChatCountInt);

        return AjaxResult.success(finalResult);
    }

    @Override
    public AjaxResult deleteChatMessage(SysUser user, String userOid) {
        LambdaUpdateWrapper<SysMessage> update = new LambdaUpdateWrapper<>();
        update.set(SysMessage::getDelFlag, ChatConstants.DEL_FLAG_DELETED);
        update.eq(SysMessage::getType, ChatConstants.MSG_TYPE_CHAT);
        
        if(StringUtils.isEmpty(userOid)) {
            update.and(w -> w.eq(SysMessage::getSenderId, user.getUserId().toString())
                    .or().eq(SysMessage::getReceiveTarget, user.getUserId().toString()));
        } else {
            update.and(w ->
                    w.or(a -> a.eq(SysMessage::getSenderId, user.getUserId().toString())
                            .eq(SysMessage::getReceiveTarget, userOid))
                    .or(a -> a.eq(SysMessage::getSenderId, userOid)
                            .eq(SysMessage::getReceiveTarget, user.getUserId().toString())));
        }
        this.remove(update);
        
        return AjaxResult.success("删除成功");
    }



    @Override
    public void removeChatPic(String oid, String target) {
        LambdaUpdateWrapper<SysMessage> update = new LambdaUpdateWrapper<>();
        update.set(SysMessage::getContent, "[图片已过期]");
        update.eq(SysMessage::getType, ChatConstants.MSG_TYPE_CHAT);
        update.eq(SysMessage::getSenderId, oid);
        if(StringUtils.isNotEmpty(target)) {
            update.eq(SysMessage::getReceiveTarget, target);
        }
        update.le(SysMessage::getCreateTime, DateUtils.addDays(new Date(), -3));
        update.ne(SysMessage::getContent,"[图片已过期]");
        this.update(update);
    }

    /**
     * 安全获取Map中的值
     */
    private <T> T getMapValue(Map<String, T> map, String key) {
        return map != null ? map.get(key) : null;
    }
}
