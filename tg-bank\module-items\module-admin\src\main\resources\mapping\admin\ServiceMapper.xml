<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.ServiceMapper">

    <select id="listMonitorService" resultType="com.main.pojo.admin.Service">
        select a.*,b.name as cloud_name, c.name as server_name from d_service a left join d_cloud b on a.cloud_oid = b.oid
        left join d_server c on a.server_oid = c.oid where a.is_monitor = 1
    </select>
</mapper>
