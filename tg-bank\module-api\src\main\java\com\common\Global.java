package com.common;


import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.UUID;

/**
 * 通用类
 *
 * <AUTHOR>
 */
public class Global {
	public static final String NULLSTRING = "";//""字符串
	public static final String TOKENHEADER = "Authorization";
	public static final DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//日期format
	public static final DateFormat year_month = new SimpleDateFormat("yyyy-MM");//日期format
	public static final DateFormat year_month_day = new SimpleDateFormat("yyyy-MM-dd");//日期format
	public static final DateFormat dfpath = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");//日期format
	public static final DateFormat year_month_day_no_ = new SimpleDateFormat("yyyyMMdd");//日期format
	public static final DateFormat year_month_day_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//日期format
	public static final DateFormat year_month_day_time_no_ = new SimpleDateFormat("yyyyMMddHHmmss");//日期format
	public static final DateFormat dtimecode = new SimpleDateFormat("mm_sss");//日期format
	public static final String TOKEN_REDIS = "DvP:Admin:token:";
	//加密次数
    public static final int HASHITERATIONS = 2;
	public static final String FORMULAJSONTOP = "JSON:";
	public static final String FORMULADATETOP = "DATE:";
	public static final int TABLE_MAX_RECORD = 9000000;
	public static final String defaultPwd = "qqkj****";
    public static final String SOCKET_REIDS = "DvP:Admin:Socket:";

    /**
	 * 创建UUID
	 *
	 * @return UUID
	 */
    public static String createUUID() {
		String uuid = UUID.randomUUID().toString();
		uuid = uuid.toUpperCase();
		uuid = uuid.replaceAll("-", Global.NULLSTRING);
		return uuid;
	}


}
