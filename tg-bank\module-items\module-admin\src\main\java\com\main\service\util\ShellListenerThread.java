package com.main.service.util;

import com.config.websocket.WebSocketHandler;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.Session;
import com.main.util.CUtil;
import com.main.util.SSHConnectInfo;
import com.main.util.SshHost;
import com.main.util.WebSSHService;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class ShellListenerThread implements Runnable {
    private final AtomicBoolean running = new AtomicBoolean(true);
    private String token;
    private Thread worker;
    private Session session;
    private SshHost host;
    private WebSSHService webSshService;
    private WebSocketHandler webSocketHandler;

    public ShellListenerThread(String token, Session session, SshHost host, WebSSHService webSshService, WebSocketHandler webSocketHandler) {
        this.token = token;
        this.session = session;
        this.host = host;
        this.webSshService = webSshService;
        this.webSocketHandler = webSocketHandler;
    }

    public void start() {
        running.set(true);
        worker = new Thread(this);
        worker.start();
    }

    @Override
    public void run() {
        SSHConnectInfo sshConnectInfo = (SSHConnectInfo) WebSSHService.sshMap.get(token);
        Channel channel = sshConnectInfo.getChannel();
        InputStream inputStream = null;
        //读取终端返回的信息流
        log.info("开始监听返参");
        try {
            inputStream = channel.getInputStream();
            //循环读取
            byte[] buffer = new byte[1024];
            int i = 0;
            //如果没有数据来，线程会一直阻塞在这个地方等待数据。
            while (running.get() && (i = inputStream.read(buffer)) != -1 && channel.isConnected()) {
                log.info("Received:{}", new String(Arrays.copyOfRange(buffer, 0, i)));
                webSshService.sendMessage(host.getSshId(), new String(Arrays.copyOfRange(buffer, 0, i)), webSocketHandler);
                if (channel.isClosed()) {
                    break;
                }
            }
            log.warn("shell监听线程已经关闭！！！");
        } catch (IOException e) {
            log.warn("线程已经被打断终止，shell监听线程已经关闭！！！");
        } finally {
            //断开连接后关闭会话
            session.disconnect();
            channel.disconnect();
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage());
                    log.error(CUtil.getStackTraceString(e));
                }
            }
        }
    }

    public void stop() {
        log.info("开始关闭监听返参");
        running.set(false);
        if (worker != null) {
            worker.interrupt();
        }
    }
}