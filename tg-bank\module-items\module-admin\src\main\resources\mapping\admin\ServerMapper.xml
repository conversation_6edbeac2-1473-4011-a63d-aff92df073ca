<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.ServerMapper">

    <select id="getPageOfRecords" resultType="com.main.pojo.admin.Server">
        select *, b.name createUserName, c.name updateUserName from d_server a left join d_user b on a.create_user = b.oid left join d_user c on a.update_user = c.oid
        <where> cloud_oid = #{cloudOid}
            <if test="params.name != null and params.name != ''"> and a.name like concat('%', #{params.name}, '%')</if>
            <if test="params.isStop != null">and a.is_stop = #{params.isStop}</if>
            <if test="params.ip != null and params.ip != ''">and (inner_ip like concat('%', #{params.ip}, '%') or ssh_ip like concat('%', #{params.ip}, '%'))</if>
        </where>
        order by create_time asc, a.name asc, inner_ip asc
    </select>
</mapper>
