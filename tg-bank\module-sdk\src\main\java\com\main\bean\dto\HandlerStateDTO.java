package com.main.bean.dto;

import com.main.myenum.HandlerType;
import com.main.myenum.ItemConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务处理状态
 *
 * <AUTHOR>
 */
@Data
public class HandlerStateDTO<T> implements Serializable {
	private static final long serialVersionUID = -243551697818046991L;
	/**
	 * 处理结果状态码 参考 BusinessRetMsgEnum
	 */
	private int code;

	/**
	 * 处理结果消息
	 */
	private String msg;

	// 上传文件使用error判断是否成功，其他方法无需考虑此字段
	private String error;
	private String status = ItemConstant.OK;
	private T data;

	/**
	 * 构造方法
	 */
	public HandlerStateDTO() {
		super();
		//创建的时候默认是成功的
		this.code = HandlerType.SUCCESS.getRetCode();
		this.msg = HandlerType.SUCCESS.getRetMsg();
	}

	public HandlerStateDTO(HandlerType handlerType) {
		super();
		//创建的时候默认是成功的
		this.code = handlerType.getRetCode();
		this.msg = handlerType.getRetMsg();
	}


	/**
	 * 设置状态
	 * @param handlerStateDTO 对象
	 */
	public void setRetInfo(HandlerStateDTO handlerStateDTO) {
		//创建的时候默认是成功的
		this.code = handlerStateDTO.getCode();
		this.msg = handlerStateDTO.getMsg();
		if(this.code != HandlerType.SUCCESS.getRetCode()) {
			this.status = ItemConstant.FAIL;
			this.error = this.msg;
		}else {
			this.status = ItemConstant.SUCC;
			this.error = null;
		}
	}

	public void setRetInfo(HandlerType handlerType) {
		HandlerStateDTO handlerStateDTO = new HandlerStateDTO(handlerType);
		setRetInfo(handlerStateDTO);
	}

	public void addException(Exception e) {
		this.code = HandlerType.SYSTEM_ERROR.getRetCode();
		this.msg = e.getMessage();
		this.status = ItemConstant.FAIL;
		this.error = this.msg;
	}
	public void addExceptionByMsg(String msg) {
		this.code = HandlerType.SYSTEM_ERROR.getRetCode();
		this.msg = msg;
		this.status = ItemConstant.FAIL;
	}

	public void setCode(String code) {
		try {
			this.code = Integer.parseInt(code);
		}catch (NumberFormatException nfe) {
			this.code = HandlerType.SYSTEM_ERROR.getRetCode();
		}
	}

	public void setCode(int code) {
		this.code = code;
	}

	@Override
	public String toString() {
		return "code = " + code + ", message = " + msg;
	}
}
