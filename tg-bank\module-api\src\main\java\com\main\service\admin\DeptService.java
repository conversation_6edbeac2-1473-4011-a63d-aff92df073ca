package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Dept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
public interface DeptService extends IService<Dept> {

    ResponseVO<Map<String, Object>> getDeptTreeData(String oid, String orgOid);

    ResponseVO<String> saveDept(JSONObject dept);
    ResponseVO<String> saveDept(Dept dept);

    boolean updateLeaf(String oid, String orgOid, int isLeaf);

    String getDeptOId(String orgOid, String parentId);

    ResponseVO<String> delete(String orgOid, String oid, String parentId);
}
