package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName d_log
 */
@TableName(value ="d_log")
@Data
public class LogInfo implements Serializable {
    /**
     *
     */
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    @TableId
    private String uuid;

    /**
     * 用户Id
     */
    private String userOid;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 类型 0 用户操作  1 系统日志
     */
    private String type;

    /**
     * 执行方法
     */
    private String method;

    /**
     * 入参
     */
    private String params;

    /**
     * 出参
     */
    private String resp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 日期
     */
    private String dateStr;



    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", uuid=").append(uuid);
        sb.append(", userOid=").append(userOid);
        sb.append(", userName=").append(userName);
        sb.append(", type=").append(type);
        sb.append(", method=").append(method);
        sb.append(", params=").append(params);
        sb.append(", resp=").append(resp);
        sb.append(", remark=").append(remark);
        sb.append(", dateStr=").append(dateStr);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
