package com.main.service.his;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.Global;
import com.main.bean.vo.ResponseVO;
import com.main.handler.Api;
import com.main.myenum.HandlerType;
import com.main.pojo.his.Patient;
import com.main.pojo.his.dto.PatientDTO;
import com.main.pojo.admin.User;
import com.main.mapper.his.PatientMapper;
import com.main.util.CUtil;
import com.main.util.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class PatientServiceImpl extends ServiceImpl<PatientMapper, Patient> implements PatientService {
    
    @Resource
    private PatientMapper patientMapper;

    @Override
    public ResponseVO<String> createPatient(PatientDTO patientDTO, User user) {
        ResponseVO<String> resp = new ResponseVO<>();
        try {
            Patient patient = new Patient();
            BeanUtils.copyProperties(patientDTO, patient);
            // 设置UUID
            patient.setOid(Global.createUUID());
            // 设置机构ID
            patient.setOrgId(user.getCurrentLoginOrg().getOid());
            patientMapper.insert(patient);
            resp.setMsg("新增患者成功");
        } catch (Exception e) {
            resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
            resp.setMsg("新增患者失败：" + e.getMessage());
        }
        return resp;
    }

    @Override
    public ResponseVO<String> updatePatient(String id, PatientDTO patientDTO, User user) {
        ResponseVO<String> resp = new ResponseVO<>();
        try {
            Patient patient = patientMapper.selectById(id);
            if (patient == null) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("患者不存在");
                return resp;
            }

            // 机构管理员只能操作本机构数据
            if (user.getIsAdmin() == 0 && !user.getCurrentLoginOrg().getOid().equals(patient.getOrgId())) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("您没有权限操作其他机构的数据");
                return resp;
            }

            BeanUtils.copyProperties(patientDTO, patient);
            patient.setUpdateTime(DateUtil.getCurrentDate());
            patientMapper.updateById(patient);
            resp.setMsg("更新患者信息成功");
        } catch (Exception e) {
            resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
            resp.setMsg("更新患者信息失败：" + e.getMessage());
        }
        return resp;
    }

    @Override
    public ResponseVO<String> deletePatient(String id, User user) {
        ResponseVO<String> resp = new ResponseVO<>();
        try {
            // 检查权限
            if (user.getIsAdmin() == -1) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("您没有权限进行此操作");
                return resp;
            }

            Patient patient = patientMapper.selectById(id);
            if (patient == null) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("患者不存在");
                return resp;
            }

            // 机构管理员只能操作本机构数据
            if (user.getIsAdmin() == 0 && !user.getCurrentLoginOrg().getOid().equals(patient.getOrgId())) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("您没有权限操作其他机构的数据");
                return resp;
            }

            patientMapper.deleteById(id);
            resp.setMsg("删除患者成功");
        } catch (Exception e) {
            resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
            resp.setMsg("删除患者失败：" + e.getMessage());
        }
        return resp;
    }

    @Override
    public ResponseVO<Map<String, Object>> pagePatients(Integer pageNum, Integer pageSize, String keyWord, String typeKey, User user) {
        try {
            Page<Patient> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<Patient> wrapper = new LambdaQueryWrapper<>();
            
            // 机构管理员或普通人员只能查看本机构数据
            if (user.getIsAdmin() != 1) {
                wrapper.eq(Patient::getOrgId, user.getCurrentLoginOrg().getOid());
            }
            if(CUtil.isNotEmpty(keyWord)) {
                wrapper.and(wrapper1 -> wrapper1.like(Patient::getName, keyWord).or().like(Patient::getMobile, keyWord));
            }
            if(!typeKey.equals("all")) {
                if(typeKey.equals("today")) {
                    wrapper.and(wrapper2 -> wrapper2.like(Patient::getUpdateTime, DateUtil.getMyDate()).or().like(Patient::getCreateTime, DateUtil.getMyDate()));
                }else if(typeKey.equals("vip")) {
                    wrapper.eq(Patient::getIsVip, 1);
                }
            }
            wrapper.orderByDesc(Patient::getIsVip, Patient::getUpdateTime, Patient::getCreateTime);

            IPage<Patient> patientPage = patientMapper.selectPage(page, wrapper);
            return Api.packageTable(patientPage);
        } catch (Exception e) {
            ResponseVO<Map<String, Object>> resp = new ResponseVO<>();
            resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
            resp.setMsg("查询患者列表失败：" + e.getMessage());
            return resp;
        }
    }

    @Override
    public ResponseVO<PatientDTO> getById(String id, User user) {
        ResponseVO<PatientDTO> resp = new ResponseVO<>();
        try {
            Patient patient = patientMapper.selectById(id);
            if (patient == null) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("患者不存在");
                return resp;
            }

            // 机构管理员只能查看本机构数据
            if (user.getIsAdmin() == 0 && !user.getCurrentLoginOrg().getOid().equals(patient.getOrgId())) {
                resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
                resp.setMsg("您没有权限查看其他机构的数据");
                return resp;
            }
            PatientDTO patientDTO = new PatientDTO();
            BeanUtils.copyProperties(patient, patientDTO);
            resp.setData(patientDTO);
        } catch (Exception e) {
            resp.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
            resp.setMsg("查询患者详情失败：" + e.getMessage());
        }
        return resp;
    }

    @Override
    public void updateVIP(String patientId, int vip) {
        LambdaUpdateWrapper<Patient> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Patient::getIsVip, vip);
        updateWrapper.eq(Patient::getOid, patientId);
        this.update(updateWrapper);
    }
} 