package com.main.handler;

import com.main.myenum.ActuatorSM4Utils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Order(-5)
@Slf4j
public class ActuatorInterceptor {

    /**
     * 定义一个切入点.
     * 解释下：
     * ~ 第一个 * 代表任意修饰符及任意返回值.
     * ~ 第二个 * 任意包名
     * ~ 第三个 * 代表任意方法.
     * ~ 第四个 * 定义在web包或者子包
     * ~ 第五个 * 任意方法
     * ~ .. 匹配任意数量的参数.
     */

    @Pointcut("execution(public * com.main.handler.ActuatorHandler.*(..))")
    public void actuatorAop() {
    }


    @Before("actuatorAop()")
    public void doBefore(JoinPoint joinPoint) throws Exception {

        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Object[] args = joinPoint.getArgs();
        String tokenParam = request.getParameter("auth");
        if (tokenParam == null && args != null && args.length > 0) {
            tokenParam = String.valueOf(args[0]);
        }
        if (tokenParam == null || !ActuatorSM4Utils.validAuth(tokenParam, 15000)) {
            throw new RuntimeException("非法请求auth!");
        }
    }
}
