package com.main.util;

import com.config.websocket.WebSocketHandler;
import com.constant.DPConstant;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.main.pojo.admin.Message;
import com.main.service.util.AsyncProcesser;
import com.main.service.util.ShellListenerThread;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@Data
public class WebSSHService {
    @Resource
    private AsyncProcesser asyncProcesser;
    
    // 存放ssh连接信息的map
    public static Map<String, Object> sshMap = new ConcurrentHashMap<>();
    private static Map<String, ShellListenerThread> tokenThreadMap = new ConcurrentHashMap<>();

    // 初始化 ssh 连接
    public void initConnection(String token) {
        JSch jSch = new JSch();
        SSHConnectInfo sshConnectInfo = new SSHConnectInfo();
        sshConnectInfo.setJSch(jSch);
        //将这个ssh连接信息放入map中
        sshMap.put(token, sshConnectInfo);
    }

    // 处理客户端发送的数据
    public void recvHandle(String token, String content, SshHost host, WebSocketHandler webSocketHandler) {
        SSHConnectInfo sshConnectInfo = (SSHConnectInfo) sshMap.get(token);
        if(sshConnectInfo == null) {
            initConnection(token);
            sshConnectInfo = (SSHConnectInfo) sshMap.get(token);
        }
        // 连接 ssh：connect 指令
        if (sshConnectInfo != null && (sshConnectInfo.getChannel() == null || !sshConnectInfo.getChannel().isConnected())) {
            //找到刚才存储的ssh连接对象
            try {
                connectToSSH(token, host, webSocketHandler);
            } catch (JSchException | IOException e) {
                log.error("webssh连接异常");
                log.error("异常信息:{}", e.getMessage());
            }
        }
        // 输入命令（把命令输到后台终端）command 指令
        if (content != null) {
            if (sshConnectInfo != null) {
                try {
                    content += "\n";
                    log.info("发送命令：{}", content);
                    transToSSH(sshConnectInfo.getChannel(), content);
                } catch (IOException e) {
                    log.error("webssh连接异常");
                    log.error("异常信息:{}", e.getMessage());
                }
            } else {
                log.error("webssh: sshConnectInfo is null !");
            }
        } else {
            log.error("不支持的操作");
        }
    }

    // 使用jsch连接终端
    protected void connectToSSH(String token, SshHost host, WebSocketHandler webSocketHandler) throws JSchException, IOException {
        SSHConnectInfo sshConnectInfo = (SSHConnectInfo) sshMap.get(token);
        //获取jsch的会话
        Session session = sshConnectInfo.getJSch().getSession(host.getUser(), host.getIp(), host.getPort());
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        //设置密码
        session.setPassword(host.getPwd());
        //连接  超时时间30s
        session.connect(30000);
        //开启shell通道
        Channel channel = session.openChannel("shell");
        //通道连接 超时时间3s
        channel.connect(3000);
        //设置channel
        sshConnectInfo.setChannel(channel);
        //转发消息给终端
        transToSSH(channel, "\r");
        log.warn("开始创建异步线程！");

        ShellListenerThread thread = new ShellListenerThread(token, session, host, this, webSocketHandler);
        thread.start();
        tokenThreadMap.put(token, thread);
//        asyncProcesser.listener(this, token, host, webSocketHandler, session);
    }


    public void sendMessage(String token, String msg, WebSocketHandler webSocketServer) {
        if (webSocketServer != null) {
            webSocketServer.sendMessage(token, Message.getOne(DPConstant.WS_MSG_TYPE_SHELL, msg));
        } else {
            log.warn("客户端已退出");
        }
    }

    // 将消息转发到终端
    private void transToSSH(Channel channel, String command) throws IOException {
        if (channel != null && channel.isConnected()) {
            OutputStream outputStream = channel.getOutputStream();
            outputStream.write(command.getBytes());
            outputStream.flush();
        }
    }

    // 关闭连接
    public void close(String token) {
        ShellListenerThread thread = tokenThreadMap.get(token);
        if (thread != null) {
            thread.stop();
        }
        SSHConnectInfo sshConnectInfo = (SSHConnectInfo) sshMap.get(token);
        if (sshConnectInfo != null) {
            //断开连接
            if (sshConnectInfo.getChannel() != null) {
                sshConnectInfo.getChannel().disconnect();
            }
            sshConnectInfo.setJSch(null);
            //map中移除
            sshMap.remove(token);
        }
    }
}