package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Role;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface RoleService extends IService<Role> {

    IPage<Role> getPage(int pageNum, int pageSize, String keyWord, User user);

    ResponseVO<Map<String, Object>> getSelectList(String oid, User user);

    ResponseVO<String> saveRole(JSONObject role, User user);

    ResponseVO<String> deleteRole(String oid, User user);

    ResponseVO<String> deleteRoles(List<String> oids);

    ResponseVO<String> changeStatus(String oid, String isStop);

    ResponseVO<Role> getOne(String oid);
}
