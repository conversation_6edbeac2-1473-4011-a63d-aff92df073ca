<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="2024-01-patient-tag-01" author="claude">
        <!-- 患者-标签关联表 - 用于存储患者和标签的对应关系 -->
        <createTable tableName="t_patient_tag_rel" remarks="患者-标签关联表">
            <column name="oid" type="varchar(32)" remarks="主键ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="patient_id" type="varchar(32)" remarks="患者ID">
                <constraints nullable="false"/>
            </column>
            <column name="tag_id" type="varchar(32)" remarks="标标签">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="datetime" remarks="创建时间"/>
            <column name="create_by" type="varchar(32)" remarks="创建人ID"/>
        </createTable>
        
        <!-- 添加索引 -->
        <createIndex tableName="t_patient_tag_rel" indexName="idx_patient_tag_rel_patient_id">
            <column name="patient_id"/>
        </createIndex>
        <createIndex tableName="t_patient_tag_rel" indexName="idx_patient_tag_rel_tag_id">
            <column name="tag_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog> 