package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.handler.Api;
import com.main.pojo.admin.User;
import com.main.service.admin.UserService;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Controller
@Slf4j
public class UserHandler {

    @Resource
    private UserService userService;

    @GetMapping(value = "/main/user/page")
    public String getPage() {
        return "/pages/main/user/table";
    }


    @RequestMapping("/user/chat/list")
    @ResponseBody
    public ResponseVO<Map<String, Object>> chatList(@RequestBody JSONObject params, @Login User user) {
        String start = "1";
        String limit = "100";// 最大只能获取100个用户列表
        int pageNum = Integer.parseInt(CUtil.nullToZero(start));
        int pageSize = Integer.parseInt(CUtil.nullToZero(limit));
        IPage<User> userLoginRecords = userService.getChatPage(pageNum, pageSize, params, user);
        return Api.packageTable(userLoginRecords);
    }

    @RequestMapping("/user/list")
    @ResponseBody
    public ResponseVO<Map<String, Object>> list(@RequestBody JSONObject params, @Login User user) {
        String start = String.valueOf(params.get("current"));
        String limit = String.valueOf(params.get("pageSize"));
        int pageNum = Integer.parseInt(CUtil.nullToZero(start));
        int pageSize = Integer.parseInt(CUtil.nullToZero(limit));
        IPage<User> userLoginRecords = userService.getPage(pageNum, pageSize, params, user);
        return Api.packageTable(userLoginRecords);
    }

    @MyAuthority(auth = "/admin/user/index")
    @RequestMapping("/user/status")
    @ResponseBody
    public ResponseVO<Map<String, Object>> status(@RequestBody JSONObject params) {
        //FIXME 校验权限
        return userService.changeStatus(params.getString("oid"), params.getString("status"));
    }

//    @RequestMapping("/user/edit")
//    public ModelAndView goEdit() {
//        return new ModelAndView("/pages/main/user/edit");
//    }

    @LogAnnotation(option = "新增用户")
    @MyAuthority(auth = "/admin/user/index")
    @RequestMapping("/user/save")
    @ResponseBody
    public ResponseVO<String> save(@RequestBody JSONObject params, @Login User user) {
        //FIXME 校验权限
        return userService.mySave(params, user);
    }

    @LogAnnotation(option = "删除用户")
    @MyAuthority(auth = "/admin/user/index")
    @RequestMapping("/user/delete")
    @ResponseBody
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        String userOid = String.valueOf(params.get("userOid"));
        if (user.getIsAdmin() == 1) {
            if (user.getOid().equals(userOid)) {
                throw new BusinessException("不能删除自己！请登录其他账号重试！");
            } else {
                return userService.delete(userOid);
            }
        } else {
            throw new BusinessException("当前用户非管理员，无权限删除用户！");
        }
    }

}

