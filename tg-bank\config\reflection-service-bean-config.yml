#用于对外发布服务到开放平台，定义method对应的服务类和方法，服务类入参和出参必须规范。
#对外发布的服务需要定义入参的验证规则，参考 params-valid-config.json
admin:
  login:
    getUserByToken:
      #测试服务
      1.0.0: {"bean":"openApiServiceImpl","method":"getUserInfoByToken"}
test:
  service:
    sayHi:
      #测试服务
      1.0.0: {"bean":"testSrvImpl","method":"sayHi"}
user:
  register:
    #注册接口
    1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"register"}
  login:
    url:
      #获取登录地址接口
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"getLoginPageUrl"}
    withOpenId:
      #openId免密登录
      1.0.0: { "bean": "userInfoServiceFacadeImpl","method": "loginWithOpenId" }
  info:
    syncUserAuth:
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"syncUserAuthInfo"}
    key:
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"getUserInfoByKey"}
    token:
      #前台用户通过token获取用户信息
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"getUserInfoByToken"}
  admin:
    login:
      url:
        #获取管理页面登录地址
        1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"getAdminLoginPageUrl"}
    save:
      #后端用户-保存或者修改
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"adminSave"}
    setPwd:
      #后端用户-修改密码
      1.0.0: { "bean": "userInfoServiceFacadeImpl","method": "adminSetPwd" }
    remove:
      #后端用户-删除接口
      1.0.0: {"bean":"userInfoServiceFacadeImpl","method":"adminDelete"}
