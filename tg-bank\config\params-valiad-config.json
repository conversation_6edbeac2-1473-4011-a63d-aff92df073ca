{"user/admin/save/1.0.0": {"email": {"name": "email", "required": false, "maxLength": 25, "validType": "notnull", "range": []}, "password": {"name": "密码", "required": false, "maxLength": 50, "validType": "string", "range": []}, "nickBh": {"name": "账号", "required": true, "maxLength": 20, "validType": "string", "range": []}, "nickName": {"name": "昵称", "required": true, "maxLength": 20, "validType": "string", "range": []}, "userName": {"name": "用户姓名", "required": false, "maxLength": 20, "validType": "string", "range": []}, "userSex": {"name": "性别", "required": false, "maxLength": 2, "validType": "number", "range": [0, 1, 2]}, "userType": {"name": "用户类型", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1]}, "userBirthday": {"name": "生日", "required": false, "maxLength": 10, "validType": "datetime(yyyyMMdd)", "range": []}, "authLevel": {"name": "实名等级", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1]}, "idType": {"name": "证件类型", "required": false, "maxLength": 1, "validType": "number", "range": [0]}, "idNo": {"name": "证件号", "required": false, "maxLength": 32, "validType": "idno", "range": []}, "mobilePhone": {"name": "手机号", "required": true, "maxLength": 11, "validType": "notnull", "range": []}, "status": {"name": "状态", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1, 2, 3]}, "crtDatetime": {"name": "创建日期", "required": false, "maxLength": 17, "validType": "datetime(yyyyMMddHHmmssSSS)", "range": []}, "crtAppId": {"name": "访问应用", "required": false, "maxLength": 32, "validType": "string", "range": []}}, "user/admin/remove/1.0.0": {"mindexId": {"name": "mindexId", "required": true, "maxLength": 32, "validType": "string", "range": []}}, "user/admin/login/url/1.0.0": {"callBackUrl": {"name": "回调URL", "required": true, "maxLength": 100, "validType": "notnull", "range": []}, "extParamMap": {"name": "额外参数", "required": false}, "channel": {"name": "渠道", "required": true, "maxLength": 36, "validType": "notnull", "range": ["WX_PUB", "H5"]}, "allowPwdLogin": {"name": "是否允许密码登录", "required": false, "maxLength": 36, "validType": "notnull", "range": ["0", "1"]}, "way": {"name": "默认登录方式", "required": false, "maxLength": 36, "validType": "notnull", "range": ["wx", "pwd", "code"]}}, "user/register/1.0.0": {"email": {"name": "email", "required": true, "maxLength": 25, "validType": "notnull", "range": []}, "password": {"name": "密码", "required": true, "maxLength": 50, "validType": "string", "range": []}, "nickBh": {"name": "账号", "required": true, "maxLength": 20, "validType": "string", "range": []}, "nickName": {"name": "昵称", "required": true, "maxLength": 20, "validType": "string", "range": []}, "userName": {"name": "用户姓名", "required": false, "maxLength": 20, "validType": "string", "range": []}, "userSex": {"name": "性别", "required": false, "maxLength": 2, "validType": "number", "range": [0, 1, 2]}, "userType": {"name": "用户类型", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1]}, "userBirthday": {"name": "生日", "required": false, "maxLength": 10, "validType": "datetime(yyyyMMdd)", "range": []}, "authLevel": {"name": "实名等级", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1]}, "idType": {"name": "证件类型", "required": false, "maxLength": 1, "validType": "number", "range": [0]}, "idNo": {"name": "证件号", "required": false, "maxLength": 32, "validType": "idno", "range": []}, "mobilePhone": {"name": "手机号", "required": true, "maxLength": 11, "validType": "notnull", "range": []}, "status": {"name": "状态", "required": false, "maxLength": 1, "validType": "number", "range": [0, 1, 2, 3]}, "crtDatetime": {"name": "创建日期", "required": false, "maxLength": 17, "validType": "datetime(yyyyMMddHHmmssSSS)", "range": []}, "crtAppId": {"name": "访问应用", "required": false, "maxLength": 32, "validType": "string", "range": []}}, "user/login/url/1.0.0": {"callBackUrl": {"name": "回调URL", "required": true, "maxLength": 100, "validType": "notnull", "range": []}, "extParamMap": {"name": "额外参数", "required": false}, "channel": {"name": "渠道", "required": true, "maxLength": 36, "validType": "notnull", "range": ["WX_PUB", "H5"]}}, "user/login/withOpenId/1.0.0": {"openId": {"name": "微信openId", "required": true, "maxLength": 50, "validType": "notnull", "range": []}, "wxAppId": {"name": "微信公众号appId", "required": true, "maxLength": 50, "validType": "notnull", "range": []}}, "user/info/token/1.0.0": {"token": {"name": "token", "required": true, "maxLength": 120, "validType": "notnull", "range": []}}, "user/info/syncUserAuth/1.0.0": {"mIndexId": {"name": "mIndexId", "required": true, "maxLength": 64, "validType": "notnull", "range": []}, "idNo": {"name": "身份证号码", "required": true, "maxLength": 18, "validType": "notnull", "range": []}, "mobilePhone": {"name": "手机号码", "required": true, "maxLength": 11, "validType": "notnull", "range": []}, "userName": {"name": "用户姓名", "required": true, "maxLength": 18, "validType": "notnull", "range": []}, "userSex": {"name": "性别", "required": true, "maxLength": 2, "validType": "number", "range": [0, 1, 2]}}}