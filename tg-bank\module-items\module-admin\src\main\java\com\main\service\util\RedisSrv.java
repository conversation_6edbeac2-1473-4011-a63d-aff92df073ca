package com.main.service.util;

import com.alibaba.fastjson.JSONObject;
import com.constant.DPConstant;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.User;
import com.main.util.CUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis封装的服务类，让业务更关注业务逻辑
 *
 * <AUTHOR>
 * 2021/10/20 14:45
 */
@Component
public class RedisSrv {
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Value("${sys.login.retry-fail-lock-time:5}")
    private int retryLockTime;


    /**
     * 放临时参数
     *
     * @param key   键
     * @param value 值
     * @param time  单位秒
     */
    public void setParam(String key, String value, long time) {
        redisTemplate.opsForValue().set(DPConstant.RK_TEMP + key, value, time, TimeUnit.SECONDS);
    }

    public void setParam(String key, String value, long time, TimeUnit unit) {
        redisTemplate.opsForValue().set(DPConstant.RK_TEMP + key, value, time, unit);
    }

    public void setObj(String key, String value, long time, TimeUnit unit) {
        redisTemplate.opsForValue().set(DPConstant.RK_TEMP + key, value, time, unit);
    }


    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 获取临时参数
     *
     * @param key 键
     * @return 值
     */
    public String getParam(String key) {
        return redisTemplate.opsForValue().get(DPConstant.RK_TEMP + key);
    }

    public Object getObj(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 通过手机号获取验证码
     *
     * @param phoneNo 手机号
     * @return 验证码
     */
    public String getVcodeByPhoneNo(String phoneNo) {
        return redisTemplate.opsForValue().get(DPConstant.RK_SMS_CODE + phoneNo);
    }

    /**
     * 保存手机验证码
     *
     * @param phoneNo      手机号
     * @param code         验证码
     * @param vcodeEfftime 生效时间 单位分
     */
    public void setVcode(String phoneNo, String code, long vcodeEfftime) {
        redisTemplate.opsForValue().set(DPConstant.RK_SMS_CODE + phoneNo, code, vcodeEfftime, TimeUnit.MINUTES);
    }

    /**
     * 保存token 关联的用户信息
     *
     * @param userInfo     用户对象
     * @param tokenEffTime 生效时间 单位天
     */
    public void setToken(User userInfo, long tokenEffTime) {
        redisTemplate.opsForValue().set(DPConstant.RK_DP_TOKEN + userInfo.getToken(), JSONObject.toJSONString(userInfo), tokenEffTime, TimeUnit.DAYS);

        String token = redisTemplate.opsForValue().get(DPConstant.RK_DP_ID_TOKEN + userInfo.getOid());
        //说明当前已经登录了该账号
        if (CUtil.isNotEmpty(token) && !token.equals(userInfo.getToken())) {
            User userOld = this.getUserByToken(token);
            if (userOld != null) {
                //挤下线
                userOld.setStatus("3");
                redisTemplate.opsForValue().set(DPConstant.RK_DP_TOKEN + token, JSONObject.toJSONString(userOld), tokenEffTime, TimeUnit.DAYS);
            }
        }
        redisTemplate.opsForValue().set(DPConstant.RK_DP_ID_TOKEN + userInfo.getOid(), userInfo.getToken(), tokenEffTime, TimeUnit.DAYS);
    }

    /**
     * 通过token 获取保存的用户信息
     *
     * @param token token
     * @return 用户信息
     */
    public User getUserByToken(String token) {
        return JSONObject.parseObject(redisTemplate.opsForValue().get(DPConstant.RK_DP_TOKEN + token), User.class);
    }

    /**
     * 通过oid获取当前登录的token
     *
     * @param oid 用户主索引
     * @return
     */
    public String getTokenByOid(String oid) {
        return redisTemplate.opsForValue().get(DPConstant.RK_DP_ID_TOKEN + oid);
    }

    /**
     * 清除连续登录失败次数
     *
     * @param requestId 请求唯一标识
     */
    public void clearRetryTimes(String requestId) {
        redisTemplate.delete(DPConstant.RK_RETRY_TIMES + requestId);
    }

    /**
     * 增加连续登录失败次数
     *
     * @param requestId 请求唯一标识
     */
    public void addRetryTimes(String requestId, int times) {
        redisTemplate.opsForValue().set(DPConstant.RK_RETRY_TIMES + requestId, String.valueOf(times), retryLockTime, TimeUnit.MINUTES);
    }

    /**
     * 检查连续登录失败次数是否超过标准
     *
     * @param requestId 请求唯一标识
     */
    public int checkRetryTimes(String requestId, long loginRetryTimes) throws BusinessException {
        String timesAlreadyFaild = redisTemplate.opsForValue().get(DPConstant.RK_RETRY_TIMES + requestId);
        int times = Integer.parseInt(CUtil.nullToZero(timesAlreadyFaild));
        if (times >= loginRetryTimes) {
            throw new BusinessException(HandlerType.OUT_SYSTEM_ERROR.getRetCode(), "当前登录失败错误次数太多，已被锁定" + retryLockTime + "分钟！请稍候重试！");
        }
        return times;
    }

}
