package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.DicMapper;
import com.main.pojo.admin.Dic;
import com.main.pojo.admin.User;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.util.BeanUtils;
import com.main.util.CUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 通用字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
public class DicServiceImpl extends ServiceImpl<DicMapper, Dic> implements DicService {
    @Resource
    private DicMapper dicMapper;
    @Override
    public ResponseVO<Map<String, Object>> getTreeData(String parentId, User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Dic> queryWrapper = new LambdaQueryWrapper<>();
        if (user.getIsAdmin() == 0 || user.getIsAdmin() == 1) {
            if(CUtil.isNotEmpty(parentId)) {
                queryWrapper.likeRight(Dic::getParentId, parentId);
            }else {
                queryWrapper.eq(Dic::getParentId, 0);
            }
        } else if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看机构信息！请联系管理员");
        }
        List<Dic> list = this.list(queryWrapper);

        Map<String, List<Dic>> parentChildrenMap = new HashMap<>();
        list.forEach(dic -> {
            String parentIdTemp = dic.getParentId();
            if (CUtil.isEmpty(parentIdTemp)) {
                parentIdTemp = "0";
            }else {
                if(parentIdTemp.equals(parentId) && CUtil.isNotEmpty(parentId)) {
                    parentIdTemp = "0";
                }
            }
            List<Dic> children = parentChildrenMap.get(parentIdTemp);
            if (children == null) children = new ArrayList<>();
            children.add(dic);
            parentChildrenMap.put(parentIdTemp, children);
        });
        List<Dic> topDicList = parentChildrenMap.get("0");
        List<Map<String, Object>> treeData = new ArrayList<>();
        if (topDicList != null && topDicList.size() > 0) {
            handlerChildren(parentChildrenMap, treeData, topDicList);
        }

        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        retDataMap.put("dVal", null);

        response.setData(retDataMap);
        return response;
    }

    private List<Map<String, Object>> getOrgTreeChildrenData(Map<String, List<Dic>> parentChildrenMap, String oid) {
        List<Map<String, Object>> childrenTreeNode = new ArrayList<>();
        List<Dic> children = parentChildrenMap.get(oid);
        handlerChildren(parentChildrenMap, childrenTreeNode, children);
        return childrenTreeNode;
    }

    private void handlerChildren(Map<String, List<Dic>> parentChildrenMap, List<Map<String, Object>> childrenTreeNode, List<Dic> children) {
        if (children != null && children.size() > 0) {
            children.forEach(dic -> {
                Map<String, Object> treeNode = BeanUtils.autoBeanToMap(dic);
                treeNode.put("label", dic.getOid() + " " + dic.getName());
                treeNode.put("value", dic.getOid());
                treeNode.put("disabled", dic.getIsStop() != 0);

                if (dic.getIsLeaf() == 0) {
                    List<Map<String, Object>> childrenTreeData = getOrgTreeChildrenData(parentChildrenMap, dic.getOid());
                    treeNode.put("children", childrenTreeData);
                } else {
                    treeNode.put("children", null);
                }
                childrenTreeNode.add(treeNode);
            });
        }
    }

    @Override
    public ResponseVO<String> save(JSONObject obj) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Dic dicInfo = obj.toJavaObject(Dic.class);
        if (CUtil.isEmpty(dicInfo.getOid())) {
            //ADD
            String oid = this.getOid(dicInfo.getParentId());
            dicInfo.setOid(oid);
            dicInfo.setIsLeaf(1);
            this.save(dicInfo);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(dicInfo.getParentId())) {
                this.updateLeaf(dicInfo.getParentId(), 0);
            }
        } else {
            //UPDATE
            this.updateById(dicInfo);
        }

        return responseVO;
    }

    @Override
    public boolean updateLeaf(String oid, int isLeaf) {
        LambdaUpdateWrapper<Dic> update = new LambdaUpdateWrapper<>();
        update.set(Dic::getIsLeaf, isLeaf);
        update.eq(Dic::getOid, oid);
        return this.update(update);
    }

    @Override
    public String getOid(String parentId) {
        String oid = dicMapper.getAutoGeneralID(parentId);
        return CUtil.handlerId(oid, parentId);
    }

    @Override
    public ResponseVO<String> delete(String oid, String parentId) {
        ResponseVO<String> response = new ResponseVO<>();
        //删除他和他的子节点
        LambdaQueryWrapper<Dic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(Dic::getOid, oid);
        this.remove(queryWrapper);
        //判断他的老父亲是不是只有他这个儿子，如果是，就给他老父亲叶子标记为1
        if (CUtil.isNotEmpty(parentId)) {
            LambdaQueryWrapper<Dic> queryParentWrapper = new LambdaQueryWrapper<>();
            queryParentWrapper.eq(Dic::getParentId, CUtil.nullToStr(parentId));
            List<Dic> orgs = this.list(queryParentWrapper);
            if (orgs == null || orgs.size() == 0) {
                this.updateLeaf(parentId, 1);
            }
        }
        return response;
    }
}
