package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户登录记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_user_login_record")
public class UserLoginRecord implements Serializable {

    private static final long serialVersionUID=1L;

    private String oid;

    /**
     * 请求入参集合
     */
    private String paramInfos;

    /**
     * 用户主索引
     */
    private String userOid;

    /**
     * 登录时间 yyyyMMddHHmmss
     */
    private String loginTime;

    /**
     * 登录失败次数
     */
    private Integer loginFailTimes;

    /**
     * toke
     */
    private String token;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 主机IP
     */
    private String hostIp;

    /**
     * 登录地点
     */
    private String location;

    /**
     * 浏览器名称
     */
    private String webName;

    /**
     * 操作系统名称
     */
    private String oprSys;

    /**
     * 会话过期时间
     */
    private String logoutTime;


}
