package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Cloud;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

import java.util.Map;

/**
 * <p>
 * 受监控的云资源基本信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
public interface CloudService extends IService<Cloud> {

    ResponseVO<Map<String, Object>> getTreeData(User user);

    ResponseVO<String> saveCloud(JSONObject cloud);

    ResponseVO<String> deleteCloud(String oid, String parentId);
}
