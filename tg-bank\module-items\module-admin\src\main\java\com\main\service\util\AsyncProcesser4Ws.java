package com.main.service.util;

import com.config.websocket.WebSocketHandler;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.User;
import com.main.service.admin.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class AsyncProcesser4Ws {
    @Resource
    private UserServiceImpl userServiceImpl;
    @Resource
    private RedisSrv redisSrv;
    @Resource
    private WebSocketHandler webSocketHandler;
    /**
     * 发送机构或者系统通知
     * @param message 消息
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Async
    public void sendMessage(Message message) {
        //判断消息范围，全部还是机构内
        String receiveTarget = message.getReceiveTarget();
        //接收范围(ALL-全部人员, ORG-机构, CURRORG-当前登录机构,USER-指定用户)
        String receiveScope = message.getReceiveScope();
        //获取范围内的用户
        switch (receiveScope) {
            case "ALL":
                // key 空就是当前登录的用户全部发送
                webSocketHandler.sendMessage("ALL", message);
                break;
            case "ORG":
            case "CURRORG":
                //先找出当前归属这个机构的在线用户，找到他们对应的token然后发送信息
                List<User> users = userServiceImpl.getOrgOnlineUser(receiveTarget);
                log.info("User " + users.size() + "");
                users.forEach(user -> {
                    String token = redisSrv.getTokenByOid(user.getOid());
                    log.info("token="+token);
                    webSocketHandler.sendMessage(token, message);
                    log.info("send success");
                });
                break;
            case "USER":
                String token = redisSrv.getTokenByOid(receiveTarget);
                webSocketHandler.sendMessage(token, message);
                break;
            default:
                break;

        }
    }
}
