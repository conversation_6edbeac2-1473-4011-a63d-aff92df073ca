package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.service.admin.CloudService;
import org.junit.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 受监控的云资源基本信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@RestController
@RequestMapping("/cloud")
public class CloudHandler {
    @Resource
    private CloudService cloudService;
    @PostMapping("/tree")
    public ResponseVO<Map<String,Object>> getTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        return cloudService.getTreeData(user);
    }

    @LogAnnotation(option = "保存云环境")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@RequestBody JSONObject cloud, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行此操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return cloudService.saveCloud(cloud);
    }

    @LogAnnotation(option = "删除云环境")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return cloudService.deleteCloud(params.getString("oid"), params.getString("parentId"));
    }
}

