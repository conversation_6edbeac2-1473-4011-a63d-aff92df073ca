package com.main.mapper.admin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.User;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 消息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {
    IPage<Message> pageMessages(Page<Message> page, @Param("type") String type, @Param("keyword") String keyword, @Param("readStatus") Integer readStatus,@Param("userOid") String userOid, @Param("user") User user);

    @MapKey("type")
    Map<String, Map<String, BigDecimal>> getUnreadCount(@Param("user") User user);
}