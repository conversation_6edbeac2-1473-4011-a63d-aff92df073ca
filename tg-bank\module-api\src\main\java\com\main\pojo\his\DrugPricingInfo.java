package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 药品定价信息实体类
 * <AUTHOR>
 */
@Data
@TableName("his_drug_pricing_info")
public class DrugPricingInfo {
    /**
     * 诊所ID，联合主键
     */
    private String companyId;
    
    /**
     * 库房ID，联合主键
     */
    private String warehouseId;
    
    /**
     * 药品ID，联合主键
     */
    @TableId
    private String oid;
    
    /**
     * 进货价
     */
    private Double purchasePrice;
    
    /**
     * 零售价
     */
    private Double retailPrice;
    
    /**
     * 是否允许拆零销售
     */
    private Boolean allowSplit;
    
    /**
     * 拆零价
     */
    private Double splitRetailPrice;
    
    /**
     * 定价方式
     */
    private String pricingMode;
    
    /**
     * 进项税率
     */
    private Double purchaseTaxRate;
    
    /**
     * 销项税率
     */
    private Double saleTaxRate;

    /**
     * 定价加成比例
     */
    private Double retailPercent;
} 