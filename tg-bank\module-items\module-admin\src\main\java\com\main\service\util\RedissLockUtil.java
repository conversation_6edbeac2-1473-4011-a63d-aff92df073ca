package com.main.service.util;


import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * redis分布式锁帮助类
 */
public class RedissLockUtil {
	private static RedissonClient redissonClient;

	public void setRedissonClient(RedissonClient locker) {
		redissonClient = locker;
	}

	/**
	 * 加锁
	 *
	 * @param lockKey
	 * @return
	 */
	public static RLock lock(String lockKey) {
		RLock lock = redissonClient.getLock(lockKey);
		lock.lock();
		return lock;
	}

	/**
	 * 释放锁
	 *
	 * @param lockKey
	 */
	public static void unlock(String lockKey) {
		RLock lock = redissonClient.getLock(lockKey);
		if(lock.isLocked()){ // 是否还是锁定状态
			if(lock.isHeldByCurrentThread()){ // 时候是当前执行线程的锁
				lock.unlock(); // 释放锁
			}
		}
	}

	/**
	 * 释放锁
	 *
	 * @param lock
	 */
	public static void unlock(RLock lock) {
		lock.unlock();
	}

	/**
	 * 带超时的锁
	 *
	 * @param lockKey
	 * @param timeout 超时时间   单位：秒
	 */
	public static RLock lock(String lockKey, int timeout) {
		RLock lock = redissonClient.getLock(lockKey);
		lock.lock(timeout, TimeUnit.SECONDS);
		return lock;
	}

	/**
	 * 带超时的锁
	 *
	 * @param lockKey
	 * @param unit    时间单位
	 * @param timeout 超时时间
	 */
	public static RLock lock(String lockKey, TimeUnit unit, int timeout) {
		RLock lock = redissonClient.getLock(lockKey);
		lock.lock(timeout, unit);
		return lock;
	}

	/**
	 * 尝试获取锁 拿不到直接放弃继续拿锁
	 *
	 * @param lockKey
	 * @return
	 */
	public static boolean tryLock(String lockKey) {
		RLock lock = redissonClient.getLock(lockKey);
		return lock.tryLock();
	}

	/**
	 * 尝试获取锁 拿不到等待 waitTime 秒，不会主动释放锁
	 *
	 * @param lockKey
	 * @param waitTime 最多等待时间
	 * @return
	 */
	public static boolean tryLock(String lockKey, int waitTime) {
		RLock lock = redissonClient.getLock(lockKey);
		try {
			return lock.tryLock(waitTime, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			return false;
		}
	}

	/**
	 * 尝试获取锁
	 *
	 * @param lockKey
	 * @param waitTime  最多等待时间
	 * @param leaseTime 上锁后自动释放锁时间
	 * @return
	 */
	public static boolean tryLock(String lockKey, int waitTime, int leaseTime) {
		RLock lock = redissonClient.getLock(lockKey);
		try {
			return lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			return false;
		}
	}

	/**
	 * 尝试获取锁
	 *
	 * @param lockKey
	 * @param unit      时间单位
	 * @param waitTime  最多等待时间
	 * @param leaseTime 上锁后自动释放锁时间
	 * @return
	 */
	@Deprecated
	public static boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
		RLock lock = redissonClient.getLock(lockKey);
		try {
			return lock.tryLock(waitTime, leaseTime, unit);
		} catch (InterruptedException e) {
			return false;
		}
	}

}
