<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.ResMapper">

    <select id="getAutoGeneralID" resultType="java.lang.String">
        select max(oid) as id
        from d_res
        <where>
            <choose>
                <when test="parentId != null and parentId != 'root' and parentId != 'null' and parentId != '' ">
                    and parent_id = #{parentId}
                </when>
                <otherwise>
                    and (parent_id is null or parent_id = '' or parent_id = '0')
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getResources" resultType="com.main.pojo.admin.Res">
        select distinct *
        from (select c.* from d_res c where is_stop = 0) a
        order by sort, oid
    </select>
    <select id="getResourcesByUserid" resultType="com.main.pojo.admin.Res">
        select distinct *
        from (
        select a.*
        from d_res a
        where is_leaf = 0 and is_stop = 0
        union all
        select d.*
        from d_user a
        left join d_user_role b on a.oid = b.user_oid
        left join d_role_res c on b.role_oid = c.role_oid
        left join d_res d on c.res_oid = d.oid
        left join d_role f on b.role_oid = f.oid
        where d.oid is not null and d.is_stop = 0 and f.is_stop = 0 and a.status = 1
        and a.oid = #{userid}) a
        order by sort, oid
    </select>
</mapper>
