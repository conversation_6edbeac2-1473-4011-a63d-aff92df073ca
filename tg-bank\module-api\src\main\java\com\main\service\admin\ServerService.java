package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Server;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

/**
 * <p>
 * 云资源的服务器信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
public interface ServerService extends IService<Server> {

    /**
     * 获取云服务器对应的服务器列表
     *
     * @param pageNum 分页
     * @param pageSize 分页大小
     * @param params 关键参数
     * @param user 登录用户
     * @return 列表
     */
    IPage<Server> getPage(int pageNum, int pageSize, JSONObject params, User user);

    ResponseVO<String> saveServer(JSONObject server, String userOid);

    ResponseVO<String> deleteServer(String oid);

    /**
     * 根据云环境批量删除服务器配置
     * @param oid
     */
    void removeByCloudOid(String oid);
}
