package com.main.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MenuVO implements Serializable {

    private static final long serialVersionUID = -8320269163037720535L;
    private String name;//菜单名称
    private String path;//路径
    private String oid;//编号
    private String component;//编号
    private String redirect; //跳转 父节点专用
    private String parentId;//父节点
    private int leaf;//叶子节点
    private MenuMetaVO meta;
    private List<MenuVO> children;//子节点

}
