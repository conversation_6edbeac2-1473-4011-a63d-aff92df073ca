package com.main.util;


import org.bouncycastle.jce.provider.BouncyCastleProvider;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;

/**
 * <AUTHOR>
 * 2022/3/21 10:56
 */
public class RSAUtils4UC {


	private static final String RSA_ALGORITHM = "RSA";
	private static final int MAX_DECRYPT_BLOCK = 128;
	private static final int MAX_ENCRYPT_BLOCK = 117;
	private static RSAPublicKey publicKey;
	private static RSAPrivateKey privateKey;

	public RSAPublicKey getPublicKey() {
		return RSAUtils4UC.publicKey;
	}

	public RSAPrivateKey getPrivateKey() {
		return RSAUtils4UC.privateKey;
	}

	public void getKeys() throws Exception {
		// 从 公钥保存的文件 读取 公钥的Base64文本
		String pubKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCy2XUgGQVKUrpm3bgaPtLvrmxVHohQP414uy+A1uKuzzdhiDHZwqpiO1s37tFl8+ksx+r4HTjiFvAjeXSTcl3F3N3axoAvKcDjBCPQ6IA3PJr77mZkEGm8fKpB6zHmvPo+ZrROHVFqbASXYLCAjWqlfLnDXDAI1DD3HRSYWHe31QIDAQAB";
		// 把 公钥的Base64文本 转换为已编码的 公钥bytes
		byte[] encPubKey = new BASE64Decoder().decodeBuffer(pubKeyBase64);

		// 创建 已编码的公钥规格
		X509EncodedKeySpec encPubKeySpec = new X509EncodedKeySpec(encPubKey);

		// 获取指定算法的密钥工厂, 根据 已编码的公钥规格, 生成公钥对象
		publicKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(encPubKeySpec);

		// 从 私钥保存的文件 读取 私钥的base文本  提供给前端！
		String priKeyBase64 = "MIICdAIBADANBgkqhkiG9w0BAQEFAASCAl4wggJaAgEAAoGBALLZdSAZBUpSumbduBo+0u+ubFUeiFA/jXi7L4DW4q7PN2GIMdnCqmI7Wzfu0WXz6SzH6vgdOOIW8CN5dJNyXcXc3drGgC8pwOMEI9DogDc8mvvuZmQQabx8qkHrMea8+j5mtE4dUWpsBJdgsICNaqV8ucNcMAjUMPcdFJhYd7fVAgMBAAECf1cqG8CRgj5H4DuGSxn26sXUZwjy4QUIFlnJaUPQh8wQ/my0/GOCt/703arT00sp0IMtUk5373zrn+p26UBIhVlUDAU2Te1WgnFf4B3Jh1uvy0g1mE5on3k2I0VnSEEtd6XgZFaHgQ3yq/JfasIwc8tQs+yv4HYh+18YtISZd8ECQQDkZ3G2JuV7Hkbg2eLzY0Tpm20Q3uhAn1s3JGQ3wpgTXAPiCPQZGNFdZjroQeyndJdzkw+UAzIS2qjOTFesWsD1AkEAyHVI3Z4mGUFS/RZPfvjPRk57F6Sn6ygd5qMJqYuA4MRomCXz7bOJJrVIwAv1Et+5Sxbj0MpAZ6bMIRML3MVPYQJAN+pLIDitfVKQL4On18W5nSb7sGGhohBhGELEdhIhgLvKB5ABkhNNTfw0958L/ILCgPzDl4yguZTmNnriC8O78QJAdEeUfPnAH+ZEvYqzknPByIa94Hki7sXWknMAp9jEaZ2FDrz1Qm1ioZGHId/NyaT9VxFwFS09LuMShh0GRIowAQJBANQLem8QnfERJV7kTftOv4O/OsFZY7w/14meVWbwnZzUnpTgcPaBPq4h5YOR5oO5ryP9yfWJGo3eU75f1/kY3Yc=";

		// 把 私钥的Base64文本 转换为已编码的 私钥bytes
		byte[] encPriKey = new BASE64Decoder().decodeBuffer(priKeyBase64);

		// 创建 已编码的私钥规格
		PKCS8EncodedKeySpec encPriKeySpec = new PKCS8EncodedKeySpec(encPriKey);

		// 获取指定算法的密钥工厂, 根据 已编码的私钥规格, 生成私钥对象
		privateKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(encPriKeySpec);
	}

	public void geneKeys() throws NoSuchAlgorithmException {
		KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM, new BouncyCastleProvider());
		keyPairGenerator.initialize(1024);
		KeyPair keyPair = keyPairGenerator.generateKeyPair();
		privateKey = (RSAPrivateKey) keyPair.getPrivate();
		publicKey = (RSAPublicKey) keyPair.getPublic();
	}


	public String encodeByPrivateKey(String body) throws Exception {
		Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
		cipher.init(Cipher.ENCRYPT_MODE, privateKey);
		byte[] inputArray = body.getBytes();
		int inputLength = inputArray.length;
		System.out.println("加密字节数：" + inputLength);
		// 标识
		int offSet = 0;
		byte[] resultBytes = {};
		byte[] cache = {};
		while (inputLength - offSet > 0) {
			if (inputLength - offSet > MAX_ENCRYPT_BLOCK) {
				cache = cipher.doFinal(inputArray, offSet, MAX_ENCRYPT_BLOCK);
				offSet += MAX_ENCRYPT_BLOCK;
			} else {
				cache = cipher.doFinal(inputArray, offSet, inputLength - offSet);
				offSet = inputLength;
			}
			resultBytes = Arrays.copyOf(resultBytes, resultBytes.length + cache.length);
			System.arraycopy(cache, 0, resultBytes, resultBytes.length - cache.length, cache.length);
		}
		return Base64.getEncoder().encodeToString(resultBytes);
	}

	public String encodeByPublicKey(String body) throws Exception {
		Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
		cipher.init(Cipher.ENCRYPT_MODE, publicKey);
		byte[] inputArray = body.getBytes();
		int inputLength = inputArray.length;
		System.out.println("加密字节数：" + inputLength);
		// 标识
		int offSet = 0;
		byte[] resultBytes = {};
		byte[] cache = {};
		while (inputLength - offSet > 0) {
			if (inputLength - offSet > MAX_ENCRYPT_BLOCK) {
				cache = cipher.doFinal(inputArray, offSet, MAX_ENCRYPT_BLOCK);
				offSet += MAX_ENCRYPT_BLOCK;
			} else {
				cache = cipher.doFinal(inputArray, offSet, inputLength - offSet);
				offSet = inputLength;
			}
			resultBytes = Arrays.copyOf(resultBytes, resultBytes.length + cache.length);
			System.arraycopy(cache, 0, resultBytes, resultBytes.length - cache.length, cache.length);
		}
		return Base64.getEncoder().encodeToString(resultBytes);
	}

	public String decodeByPublicKey(String body) throws Exception {
		Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
		cipher.init(Cipher.DECRYPT_MODE, publicKey);
		return decryptByPublicKey(body);
	}

	public String decodeByPrivateKey(String body) throws Exception {
		Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
		cipher.init(Cipher.DECRYPT_MODE, privateKey);
		return decryptByPrivateKey(body);
	}

	public String decryptByPublicKey(String encryptedStr) {
		try {
			// 对公钥解密
			byte[] privateKeyBytes = publicKey.getEncoded();
			// 获得公钥
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(privateKeyBytes);
			// 获得待解密数据
			byte[] data = decryptBase64(encryptedStr);
			KeyFactory factory = KeyFactory.getInstance("RSA");
			PublicKey publicKey = factory.generatePublic(keySpec);
			// 对数据解密
			Cipher cipher = Cipher.getInstance(factory.getAlgorithm());
			cipher.init(Cipher.DECRYPT_MODE, publicKey);
			// 返回UTF-8编码的解密信息
			int inputLen = data.length;
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			int offSet = 0;
			byte[] cache;
			int i = 0;
			// 对数据分段解密
			while (inputLen - offSet > 0) {
				if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
					cache = cipher.doFinal(data, offSet, MAX_DECRYPT_BLOCK);
				} else {
					cache = cipher.doFinal(data, offSet, inputLen - offSet);
				}
				out.write(cache, 0, cache.length);
				i++;
				offSet = i * MAX_DECRYPT_BLOCK;
			}
			byte[] decryptedData = out.toByteArray();
			out.close();
			return new String(decryptedData, "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	/**
	 * 私钥解密
	 *
	 * @param encryptedStr
	 * @return
	 */
	public String decryptByPrivateKey(String encryptedStr) {
		try {
			// 对私钥解密
			byte[] privateKeyBytes = privateKey.getEncoded();
			// 获得私钥
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
			// 获得待解密数据
			byte[] data = decryptBase64(encryptedStr);
			KeyFactory factory = KeyFactory.getInstance("RSA");
			PrivateKey privateKey = factory.generatePrivate(keySpec);
			// 对数据解密
			Cipher cipher = Cipher.getInstance(factory.getAlgorithm());
			cipher.init(Cipher.DECRYPT_MODE, privateKey);
			// 返回UTF-8编码的解密信息
			int inputLen = data.length;
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			int offSet = 0;
			byte[] cache;
			int i = 0;
			// 对数据分段解密
			while (inputLen - offSet > 0) {
				if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
					cache = cipher.doFinal(data, offSet, MAX_DECRYPT_BLOCK);
				} else {
					cache = cipher.doFinal(data, offSet, inputLen - offSet);
				}
				out.write(cache, 0, cache.length);
				i++;
				offSet = i * MAX_DECRYPT_BLOCK;
			}
			byte[] decryptedData = out.toByteArray();
			out.close();
			return new String(decryptedData, "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * BASE64 解码
	 *
	 * @param key 需要Base64解码的字符串
	 * @return 字节数组
	 */
	public static byte[] decryptBase64(String key) {
		return Base64.getDecoder().decode(key);
	}


	public static void main(String[] args) throws Exception {
		RSAUtils4UC rsaUtils = new RSAUtils4UC();
		//使用固定key
		rsaUtils.getKeys();
		//使用自动生成key 一台机器会生成一次并固定，后续可以后端开放获取公钥接口给前端，用来解决公钥变化问题。也可以直接使用上面固定公私钥
//		rsaUtils.geneKeys();
//		String plain = "mfHXYfK3AAy3vjjrBsYNoYY7WrSfNhsJu1qSlBvBBARiaTph5K9harhwA1jMiyfHLghjWxdtlT5ocUr3jserNEMlZJp45eRUG9F7Gj7KqOqSF9i3NYgEm/JkUBRevoWInVRX7LezZ4zHCQGXRZbqUSMw2aUhpgPvquLB36sekTc=";
//		String encryptData = rsaUtils.encodeByPublicKey(plain);
//		String encryptData2 = rsaUtils.encodeByPrivateKey(plain);
//		System.out.println("公钥加密：" + encryptData);
//		System.out.println("私钥加密：" + encryptData2);
//		System.out.println("私钥解密后:" + rsaUtils.decodeByPrivateKey(encryptData));
//		System.out.println("公钥解密后:" + rsaUtils.decodeByPublicKey(encryptData2));
		String enc = "ViWS2oczVtzpxh848X4KxjMM8P58vbXYmtkH2BB9RDS6BeX1HWniPVLB3oyXoFCxfEZgnrIaoPKpjZpykyOiAFVbbyuc790icp71lop6cGs0vw1gbD5sBHwekdZLrMT1JhjvgZEjZrMQk8GSwSZ/hqlOGfFd1umJ0avKhSH0ic4=";
		System.out.println("加密前:" + enc);
		System.out.println("公钥:" + Base64.getEncoder().encodeToString(RSAUtils4UC.publicKey.getEncoded()));
		System.out.println("私钥:" + Base64.getEncoder().encodeToString(RSAUtils4UC.privateKey.getEncoded()));
//		System.out.println("私钥加密后:" + encodes);
//		System.out.println("公钥加密后:" + rsaUtils.encodeByPublicKey("123"));
		System.out.println("公钥解密后:" + rsaUtils.decodeByPrivateKey(enc));
	}
}

