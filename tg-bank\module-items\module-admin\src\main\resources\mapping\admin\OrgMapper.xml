<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.OrgMapper">
    <select id="getAutoGeneralID" resultType="java.lang.String">
        select max(oid) as id
        from d_org
        <where>
            <choose>
                <when test="parentId != null and parentId != 'root' and parentId != 'null' and parentId != '' ">
                    and parent_id = #{parentId}
                </when>
                <otherwise>
                    and (parent_id is null or parent_id = '' or parent_id = '0')
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>
