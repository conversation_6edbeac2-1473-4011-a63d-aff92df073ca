package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务器部署的服务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_service")
public class Service implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId
    private String oid;

    /**
     * 归属云
     */
    private String cloudOid;
    @TableField(exist = false)
    private String cloudName;

    /**
     * 归属服务器
     */
    @TableField(exist = false)
    private String serverName;
    private String serverOid;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 监控路径
     */
    private String path;

    /**
     * 通知手机号
     */
    private String noticePhone;

    /**
     * 备用手机号
     */
    private String noticePhoneBak;

    /**
     * 通知openid
     */
    private String noticeOpenId;

    /**
     * 是否集成actuator
     */
    private Integer isServerView;

    /**
     * 是否监控
     */
    private Integer isMonitor;

    /**
     * 是否停用
     */
    private Integer isStop;

}
