<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.UserMapper">

    <select id="getPageOfRecords" resultType="com.main.pojo.admin.User">
        select * from d_user
        <where>
            <if test="userOid != null" >
                and oid in (select user_oid from d_user_org where org_oid in (select org_oid from d_user_org where user_oid = #{userOid})) and is_admin = -1
            </if>
            <if test="params.keyWord!=null and params.keyWord != ''">
                and (
                    oid like concat('%', #{params.keyWord}, '%')
                    or
                    obh like concat('%', #{params.keyWord}, '%')
                    or
                    name like concat('%', #{params.keyWord}, '%')
                    or
                    phone like concat('%', #{params.keyWord}, '%')
                    or
                    oid in (select user_oid from d_user_org where org_oid in (select oid from d_org where org_name like concat('%', #{params.keyWord}, '%') or org_short_name like concat('%', #{params.keyWord}, '%')))
                )
            </if>
            <if test="params.beginDay!=null and params.beginDay != ''">
               and entry_day &gt;= #{params.beginDay} and entry_day &lt;= #{params.endDay}
            </if>
            <if test="params.status!=null and params.status != ''">
                and status = #{params.status}
            </if>
            <if test="params.isAdmin!=null and params.isAdmin != ''">
                and is_admin = #{params.isAdmin}
            </if>
        </where>
        order by online desc,is_admin desc,oid asc
    </select>
    <select id="getOrgManagers" resultType="com.main.pojo.admin.User">
        select distinct b.* from  d_user_org a left join d_user b on a.user_oid = b.oid and b.is_admin != -1
        where a.org_oid = #{orgOid} and b.oid is not null order by b.is_admin desc
    </select>
    <select id="getOrgOnlineUser" resultType="com.main.pojo.admin.User">
        select distinct b.* from  d_user_org a left join d_user b on a.user_oid = b.oid and b.online = 1
        where a.org_oid in (${orgs}) and b.oid is not null
    </select>
    <select id="getPageOfChatRecords" resultType="com.main.pojo.admin.User">
        select a.unread,a.lastMessage, b.* from (
            select userOid, sum(unread) unread, SUBSTRING_INDEX(group_concat(lastMessage order by create_time desc), ',',1) as lastMessage from
            (
                # 我发送的消息
                SELECT receive_target  userOid,  0 unread,
                    SUBSTRING_INDEX(group_concat(content order by create_time desc), ',',1) as lastMessage,
                    SUBSTRING_INDEX(group_concat(create_time order by create_time desc), ',',1) as create_time
                FROM d_message
                WHERE sender_id = #{userOid}
                    and type = 'CHAT'
                group by receive_target 
                union all
                # 别人发给我的消息
                SELECT sender_id userOid,sum(1-chat_read_status) unread,
                    SUBSTRING_INDEX(group_concat(content order by create_time desc),',', 1) as lastMessage,
                    SUBSTRING_INDEX(group_concat(create_time order by create_time desc), ',',1) as create_time
                FROM d_message
                WHERE receive_target = #{userOid}
                    and type = 'CHAT'
                group by sender_id
            ) a group by userOid
        ) a
        left join d_user b on a.userOid = b.oid and b.oid !=#{userOid}
        where b.oid is not null and b.status != 0 and b.oid !=#{userOid}
        <if test="params.keyWord != null and params.keyWord !=''">
            and (b.name like concat('%',#{params.keyWord},'%') or b.phone like concat('%',#{params.keyWord},'%'))
        </if>
    </select>
</mapper>
