package com.main.service.his;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.mapper.his.PatientTagRelMapper;
import com.main.pojo.his.Patient;
import com.main.pojo.his.PatientTagRel;
import com.main.util.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class PatientTagServiceImpl extends ServiceImpl<PatientTagRelMapper, PatientTagRel> implements PatientTagService {

    @Resource
    private PatientTagRelMapper patientTagRelMapper;
    @Resource
    private PatientService patientService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<String> addTagToPatient(String patientId, List<String> tagIds) {
        ResponseVO<String> resp = new ResponseVO<>();
        // 删除已有关联
        patientTagRelMapper.delete(new LambdaQueryWrapper<PatientTagRel>()
                .eq(PatientTagRel::getPatientId, patientId)
                .in(PatientTagRel::getTagId, tagIds));
        
        // 添加新关联
        for (String tagId : tagIds) {
            PatientTagRel rel = new PatientTagRel();
            rel.setOid(Global.createUUID());
            rel.setPatientId(patientId);
            rel.setTagId(tagId);
            rel.setCreateTime(new Date());
            patientTagRelMapper.insert(rel);
            if(tagId.equals("VIP")) {
                patientService.updateVIP(patientId, 1);
            }
        }
        return resp;
    }

    @Override
    public ResponseVO<String> removeTagFromPatient(String patientId, String tagId) {
        ResponseVO<String> resp = new ResponseVO<>();
        patientTagRelMapper.delete(new LambdaQueryWrapper<PatientTagRel>()
                .eq(PatientTagRel::getPatientId, patientId)
                .eq(PatientTagRel::getTagId, tagId));
        if(tagId.equals("VIP")) {
            patientService.updateVIP(patientId, 0);
        }
        return resp;
    }


    @Override
    public ResponseVO<List<PatientTagRel>> getPatientTags(String patientId) {
        ResponseVO<List<PatientTagRel>> resp = new ResponseVO<>();
        LambdaQueryWrapper<PatientTagRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientTagRel::getPatientId, patientId);
        wrapper.orderByAsc(PatientTagRel::getTagId);
        // 获取患者的标签ID
        List<PatientTagRel> tags = patientTagRelMapper.selectList(wrapper);
        resp.setData(tags);
        // 查询标签详情
        return resp;
    }
} 