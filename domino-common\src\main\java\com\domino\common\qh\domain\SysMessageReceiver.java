package com.domino.common.qh.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.domino.common.annotation.Excel;
import com.domino.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 消息接收记录对象 sys_message_receiver
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class SysMessageReceiver {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String receiverId;

    /**
     * 消息ID
     */
    @Excel(name = "消息ID")
    private String messageId;

    /**
     * 接收人ID
     */
    @Excel(name = "接收人ID")
    private String userId;

    /**
     * 阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

}
