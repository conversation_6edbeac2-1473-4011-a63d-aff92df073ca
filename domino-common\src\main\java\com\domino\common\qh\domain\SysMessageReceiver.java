package com.domino.common.qh.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.domino.common.annotation.Excel;
import com.domino.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 消息接收记录对象 sys_message_receiver
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@TableName("sys_message_receiver")
public class SysMessageReceiver extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String receiverId;

    /**
     * 消息ID
     */
    @Excel(name = "消息ID")
    private String messageId;

    /**
     * 接收人ID
     */
    @Excel(name = "接收人ID")
    private String userId;

    /**
     * 阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    public String getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getReadTime() {
        return readTime;
    }

    public void setReadTime(Date readTime) {
        this.readTime = readTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("receiverId", getReceiverId())
                .append("messageId", getMessageId())
                .append("userId", getUserId())
                .append("readTime", getReadTime())
                .append("createTime", getCreateTime())
                .toString();
    }
}
