package com.config.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
@Slf4j
public class WebSocketConfig implements WebSocketConfigurer {
 
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        log.info("websocket启动成功!"); // 打印日志信息，表示WebSocket成功启动
 
        registry.addHandler(new WebSocketHandler(), "/dvp-socket") // 注册WebSocket处理程序，指定处理程序和对应的URL路径
                .setAllowedOrigins("*"); // 设置允许的跨域来源，这里使用通配符表示允许来自任何域的请求
    }
}