package com.main.handler.his;

import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.his.PatientTagRel;
import com.main.pojo.his.dto.PatientDTO;
import com.main.pojo.admin.User;
import com.main.service.his.PatientService;
import com.main.service.his.PatientTagService;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/patient")
public class PatientHandler {
    
    @Resource
    private PatientService patientService;

    @Resource
    private PatientTagService patientTagService;

    /**
     * 患者管理界面分页列表
     */
    @MyAuthority(auth="/his/Patient/index")
    @RequestMapping("/page/list")
    public ResponseVO<Map<String, Object>> pageList(@RequestBody JSONObject params, @Login User user) {
        String start = String.valueOf(params.get("current"));
        String limit = String.valueOf(params.get("pageSize"));
        String keyWord = String.valueOf(params.get("searchKey"));
        // all today vip 分别表示全部患者，今日就诊，vip
        String typeKey = String.valueOf(params.get("typeKey"));

        int pageNum = Integer.parseInt(CUtil.nullToZero(start));
        int pageSize = Integer.parseInt(CUtil.nullToZero(limit));
        
        return patientService.pagePatients(pageNum, pageSize, keyWord, typeKey, user);
    }

    /**
     * 新增患者
     */
    @LogAnnotation(option = "新增患者")
    @MyAuthority(auth="/his/Patient/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@Valid @RequestBody JSONObject params, @Login User user) {
        PatientDTO patientDTO = params.toJavaObject(PatientDTO.class);
        return patientService.createPatient(patientDTO, user);
    }

    /**
     * 更新患者信息
     */
    @LogAnnotation(option = "更新患者信息")
    @MyAuthority(auth="/admin/patient/index")
    @PostMapping("/update")
    public ResponseVO<String> update(@RequestBody JSONObject params, @Login User user) {
        String oid = params.getString("oid");
        PatientDTO patientDTO = params.toJavaObject(PatientDTO.class);
        return patientService.updatePatient(oid, patientDTO, user);
    }

    /**
     * 删除患者
     */
    @LogAnnotation(option = "删除患者")
    @MyAuthority(auth="/his/Patient/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        String oid = params.getString("oid");
        return patientService.deletePatient(oid, user);
    }

    /**
     * 获取患者详情
     */
    @MyAuthority(auth="/his/Patient/index")
    @PostMapping("/getOne")
    public ResponseVO<PatientDTO> getOne(@RequestBody JSONObject params, @Login User user) {
        String oid = params.getString("oid");
        return patientService.getById(oid, user);
    }
    @PostMapping("/tag/add")
    @LogAnnotation(option = "为患者添加标签")
    public ResponseVO<String> addTags(@RequestBody JSONObject params, @Login User user) {
        String patientId = params.getString("patientId");
        List<String> tagIds = params.getJSONArray("tagIds").toJavaList(String.class);
        return patientTagService.addTagToPatient(patientId, tagIds); 
    }

    @PostMapping("/tag/remove") 
    @LogAnnotation(option = "移除患者标签")
    public ResponseVO<String> removeTags(@RequestBody JSONObject params) {
        String patientId = params.getString("patientId");
        String tagId = params.getString("tagId");
        return patientTagService.removeTagFromPatient(patientId, tagId);
    }

    @PostMapping("/tag/patient")
    public ResponseVO<List<PatientTagRel>> getPatientTags(@RequestBody JSONObject params) {
        String patientId = params.getString("patientId");
        return patientTagService.getPatientTags(patientId);
    }
} 