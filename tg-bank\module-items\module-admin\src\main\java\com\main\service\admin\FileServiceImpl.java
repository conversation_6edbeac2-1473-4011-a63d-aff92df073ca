package com.main.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.config.websocket.WebSocketHandler;
import com.constant.DPConstant;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.Server;
import com.main.pojo.admin.User;
import com.main.service.util.RedisSrv;
import com.main.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FileServiceImpl implements FileService {
    @Resource
    private ServerService serverService;

    @Resource
    private WebSocketHandler webSocketHandler;

    @Resource
    private RedisSrv redisSrv;
    @Resource
    private ExecuteShellUtil sshUtil;
    @Resource
    private WebSSHService webSSHService;

    @Override
    public ResponseVO<Map<String, Object>> getTreeData(User user, String serverOid, String path) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看文件目录！请联系管理员分配管理员权限");
        }
        Server server = serverService.getById(serverOid);
        SftpUtil sftpUtil = new SftpUtil();
        if (CUtil.isEmpty(path)) {
            path = "/";
        }
        SftpObject sftpObject = null;
        List<Map<String, Object>> treeData = new ArrayList<>();
        Map<String, Object> treeTopNode = new HashMap<>();
        List<Map<String, Object>> treeTopChildrenNode = new ArrayList<>();
        try {
            sftpObject = sftpUtil.initSftpClient(server.getSshIp(), server.getSshUser(), Integer.parseInt(server.getSshPort()), server.getSshPass());
            boolean onlyShowParent = true;
            List<String> fileList = sftpUtil.listFiles(sftpObject.getChannelSftp(), path, onlyShowParent);
            String finalPath = path;
            fileList.forEach(data -> {
                Map<String, Object> treeNode = new HashMap<>();
                treeNode.put("label", data);
                treeNode.put("value", finalPath + '/' + data);
                treeNode.put("children", true);
                treeNode.put("disabled", false);
                treeTopChildrenNode.add(treeNode);
            });

            treeTopNode.put("label", "/");
            treeTopNode.put("value", "/");
            treeTopNode.put("children", treeTopChildrenNode);
            treeTopNode.put("disabled", false);
            treeData.add(treeTopNode);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            sftpUtil.closeSftp(sftpObject);
        }
        Map<String, Object> retDataMap = new HashMap<>();
        if (!"/".equals(path)) {
            retDataMap.put("data", treeTopChildrenNode);
        } else {
            retDataMap.put("data", treeData);
        }
        response.setData(retDataMap);
        return response;
    }

    @Override
    public List<com.main.pojo.admin.File> getFileList(JSONObject params, User user) {
        if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看文件目录！请联系管理员分配管理员权限");
        }
        String path = params.getString("path");
        String serverOid = params.getString("serverOid");

        Server server = serverService.getById(serverOid);
        SftpUtil sftpUtil = new SftpUtil();
        if (CUtil.isEmpty(path)) {
            path = "/";
        }
        SftpObject sftpObject = null;
        List<com.main.pojo.admin.File> fileList = null;
        try {
            sftpObject = sftpUtil.initSftpClient(server.getSshIp(), server.getSshUser(), Integer.parseInt(server.getSshPort()), server.getSshPass());
            fileList = sftpUtil.listFiles(sftpObject.getChannelSftp(), path);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            sftpUtil.closeSftp(sftpObject);
        }
        return fileList;
    }

    @Override
    public ResponseVO<String> uploadFile(MultipartFile file, String fileUrl, String serverOid, User user) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        try {
//            下载本地的文件代码
//            java.io.File temp = new java.io.File(fileUrl);
//            if (!temp.isDirectory()) {
//                fileUrl = fileUrl.substring(0, fileUrl.lastIndexOf("/"));
//            }
//            String projectPath = System.getProperty("user.dir");
//            log.info("项目路径：{}", projectPath);
//            java.io.File upload = new java.io.File(projectPath, "temp/");
//            if (!upload.exists()) upload.mkdirs();
//            log.info("缓存文件存放路径: {}", upload.getAbsolutePath());
//            java.io.File newFile = new java.io.File(upload.getAbsolutePath() + java.io.File.separator + file.getOriginalFilename());
//            log.info("开始上传文件：{}", fileUrl + java.io.File.separator + file.getOriginalFilename());
//            file.transferTo(newFile);

            if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
                throw new BusinessException("当前用户没有权限查看文件目录！请联系管理员分配管理员权限");
            }
            Server server = serverService.getById(serverOid);
            //sftp
            SftpUtil sftpUtil = new SftpUtil();
            SftpObject sftpObject = null;
            try {
                sftpObject = sftpUtil.initSftpClient(server.getSshIp(), server.getSshUser(), Integer.parseInt(server.getSshPort()), server.getSshPass());
                sftpUtil.uploadFile(sftpObject.getChannelSftp(), fileUrl, file.getOriginalFilename(), file.getInputStream());
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                sftpUtil.closeSftp(sftpObject);
            }

        } catch (Exception e) {
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg("上传文件失败！" + e.getMessage());
            responseVO.setError(responseVO.getMsg());
        }
        return responseVO;
    }

    @Override
    public void downloadFile(HttpServletResponse resp, String filePath, String fileName, String serverOid, User user) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        try {
            if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
                throw new BusinessException("当前用户没有权限查看文件目录！请联系管理员分配管理员权限");
            }
            Server server = serverService.getById(serverOid);
            //sftp
            SftpUtil sftpUtil = new SftpUtil();
            SftpObject sftpObject = null;
            try {
                sftpObject = sftpUtil.initSftpClient(server.getSshIp(), server.getSshUser(), Integer.parseInt(server.getSshPort()), server.getSshPass());

                resp.reset();
                resp.setContentType("application/octet-stream");
                resp.setCharacterEncoding("utf-8");
                resp.setHeader("Content-Disposition", "attachment;filename=" +
                        new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                //sftp先下载到本地
                sftpUtil.download(sftpObject.getChannelSftp(), filePath, resp.getOutputStream());
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                sftpUtil.closeSftp(sftpObject);
            }
        } catch (Exception e) {
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg("下载文件失败！" + e.getMessage());
            responseVO.setError(responseVO.getMsg());
            try {
                resp.getWriter().write(JSON.toJSONString(responseVO));
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }

    }

    @Override
    public ResponseVO<String> delFile(String filePath, String serverOid, User user) {
        Assert.assertNotNull("请确认入参filePath不为空！", filePath);
        Assert.assertNotNull("请确认入参serverOid不为空！", serverOid);
        ResponseVO<String> responseVO = new ResponseVO<>();
        try {
            if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
                throw new BusinessException("当前用户没有权限查看文件目录！请联系管理员分配管理员权限");
            }
            Server server = serverService.getById(serverOid);
            //sftp
            SftpUtil sftpUtil = new SftpUtil();
            SftpObject sftpObject = null;
            try {
                sftpObject = sftpUtil.initSftpClient(server.getSshIp(), server.getSshUser(), Integer.parseInt(server.getSshPort()), server.getSshPass());
                sftpUtil.delete(sftpObject.getChannelSftp(), filePath);
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                sftpUtil.closeSftp(sftpObject);
            }

        } catch (Exception e) {
            responseVO.setRetInfo(HandlerType.SYSTEM_ERROR);
            responseVO.setMsg("删除文件失败！" + e.getMessage());
            responseVO.setError(responseVO.getMsg());
        }
        return responseVO;
    }

    @Override
    public void receiveWebSocket(JSONObject params) {
        StringBuilder respContent = new StringBuilder();
        String content = params.getString("content");
        String serverOid = params.getString("serverOid");
        Server server = serverService.getById(serverOid);
        String token = params.getString("token");
        try {
            SshHost host = new SshHost(token, "dev", server.getSshIp(), server.getSshUser(), server.getSshPass(), Integer.parseInt(server.getSshPort()));
            if (content.equals("confirm")) {
                respContent.append("\r\n").append("成功连接远程主机，请输入命令 ↵ 确定").append("\r\n");
                respContent.append("[").append(server.getSshUser()).append("@").append(server.getSshIp()).append("]# ");
                webSocketHandler.sendMessage(token, Message.getOne(DPConstant.WS_MSG_TYPE_SHELL, respContent.toString()));
            } else if (content.equals("stop")) {
                webSSHService.recvHandle(token, "\u0003", host, webSocketHandler);
            } else {
                webSSHService.recvHandle(token, content, host, webSocketHandler);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            CUtil.getStackTraceString(e);
            respContent.append("执行命令报错了：").append(e.getMessage()).append("\r\n");
            respContent.append("\r\n").append("[").append(server.getSshUser()).append("@").append(server.getSshIp()).append("]# ");
            webSocketHandler.sendMessage(token, Message.getOne(DPConstant.WS_MSG_TYPE_SHELL, respContent.toString()));
        }
//        log.warn("websocket返回： {}", respContent);
    }

    /**
     * @param resp     HttpServletResponse
     * @param filePath 文件路径含文件名
     * @param fileName 文件名称
     */
    public void downloadLocalFile(HttpServletResponse resp, String filePath, String fileName) {
        File file = new File(filePath);
        resp.reset();
        resp.setContentType("application/octet-stream");
        resp.setCharacterEncoding("utf-8");
        resp.setContentLength((int) file.length());
        resp.setHeader("Content-Disposition", "attachment;filename=" +
                new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        byte[] buff = new byte[1024];
        BufferedInputStream bis = null;
        OutputStream os;
        try {
            os = resp.getOutputStream();
            bis = new BufferedInputStream(Files.newInputStream(file.toPath()));
            int i;
            while ((i = bis.read(buff)) != -1) {
                os.write(buff, 0, i);
                os.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                assert bis != null;
                bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
