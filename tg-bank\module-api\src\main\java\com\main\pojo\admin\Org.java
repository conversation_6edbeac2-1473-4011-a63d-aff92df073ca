package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import com.common.Global;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_org")
public class Org implements Serializable {

    private static final long serialVersionUID=1L;
    @TableId
    private String oid;

    private String orgName;

    private String orgShortName;

    //服务结束时间
    private String endDate;//yyyy-MM-dd

    // 0启用 1停用 2过期
    private Integer isStop;

    private String parentId;

    private Integer isLeaf;

    /**
     * 机构类型 1 医疗机构  2 实体商铺
     */
    private Integer type;

    /**
     * 登录时候，默认登录的机构 0否 1是 信息存储在 d_user_org
     */
    @TableField(exist = false)
    private Integer choose = 0;

    /**
     * 用户拥有的该机构下的部门信息
     */
    @TableField(exist = false)
    private List<Dept> depts;

    public Org(){
        super();
    }

    public Org(String orgName, String orgShortName) {
        this.orgName = orgName;
        this.orgShortName = orgShortName;
        this.isLeaf = 1;
        this.isStop = 0;
        //永久有效
        this.endDate = "9999-01-01";
        this.parentId = Global.NULLSTRING;
        this.type = 0;
    }
}
