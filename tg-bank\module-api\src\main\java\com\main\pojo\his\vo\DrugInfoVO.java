package com.main.pojo.his.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 药品信息视图对象
 */
@Data
public class DrugInfoVO {
    /** 诊所ID */
    private String companyId;
    /** 库房ID */
    private String warehouseId;
    /** 药品ID */
    private String oid;
    
    // 基本信息
    /** 药品名称 */
    private String name;
    /** 药品编码 */
    private String code;
    /** 通用名 */
    private String commonName;
    /** 商品名 */
    private String tradeName;
    /** 助记码 */
    private String zjm;
    /** 药品类型 */
    private String type;
    /** 药品分类 */
    private String category;
    /** 药理分类 */
    private String pharmacologyCategory;
    /** 规格 */
    private String specification;
    /** 单位 */
    private String unit;
    /** 生产厂家 */
    private String manufacturer;
    /** 批准文号 */
    private String approvalNumber;
    /** 条形码 */
    private String barcode;
    /** 商品编码 */
    private String productCode;
    /** 剂型 */
    private String dosageForm;
    /** 剂量 */
    private Double dosage;
    /** 剂量单位 */
    private String dosageUnit;
    /** 最小包装数量 */
    private Integer minPackageQuantity;
    /** 最小包装单位 */
    private String minPackageUnit;
    /** 包装单位 */
    private String packageUnit;
    /** 医保类型 */
    private String insuranceType;
    /** 医保编号 */
    private String insuranceNo;
    /** 医保代码 */
    private String insuranceCode;
    /** 结算优先级 */
    private String settlementPriority;
    /** 是否贵重器械 */
    private Boolean isValuableEquipment;
    /** 产地类型（进口/国产）*/
    private String originType;
    private int enable;
    // 扩展信息
    /** 处方类型 */
    private String prescriptionType;
    /** 特殊药品 */
    private String specialDrug;
    private List<String> specialDrugArr;
    /** 追溯码 */
    private String traceCode;
    /** 上市许可持有人 */
    private String licenseHolder;
    /** 是否基本药物 */
    private String isBasicDrug;
    /** 药品标签 */
    private String drugTags;
    /** 药品标签数组 */
    private List<String> drugTagsArr;
    /** 储存条件 */
    private String storageCondition;
    /** 储存条件数组 */
    private List<String> storageConditionArr;
    /** 养护类型 */
    private String maintenanceType;
    /** 保质期（月） */
    private Integer shelfLife;
    /** 有效期 */
    private Integer validityPeriod;
    /** 有效期单位 */
    private String validityUnit;
    /** 批准有效期 */
    private String approvalValidDate;
    /** 批准有效期数组 */
    private List<String> approvalValidDateArr;
    /** 货位号 */
    private String locationNo;
    /** 备注 */
    private String remark;
    /** 抗菌药类型 */
    private String antibacterialType;
    /** 抗菌药等级 */
    private String antibacterialLevel;
    /** 抗菌药分类 */
    private String antibacterialCategories;
    /** 抗菌药分类数组 */
    private List<String> antibacterialCategoriesArr;
    /** 抗生素剂量 */
    private Double antibioticDosage;
    /** 抗生素单位 */
    private String antibioticUnit;
    /**
     * 医疗器械分类
     */
    private String equipmentClass;

    // 定价信息
    /** 采购价 */
    private Double purchasePrice;
    /** 零售价 */
    private Double retailPrice;
    /** 允许拆零 */
    private Boolean allowSplit;
    /** 拆零售价 */
    private Double splitRetailPrice;
    /** 定价模式 */
    private String pricingMode;
    /** 进项税率 */
    private Double purchaseTaxRate;
    /** 销项税率 */
    private Double saleTaxRate;
    /**
     * 定价加成比例
     */
    private Double retailPercent;

    // 库存信息
    /** 当前库存 */
    private Double currentStock;
    /** 可用库存 */
    private Double availableStock;
    /** 库存金额 */
    private Double stockValue;
    /** 日均消耗 */
    private Double dailyConsumption;
    /** 周转天数 */
    private Integer turnoverDays;
    /** 利润率 */
    private String profitRate;
    /** 有效期至 */
    private String expiryDate;

    public void calcSpecification() {
        this.specification = this.dosage + "*" + this.dosageUnit + "*" + this.minPackageQuantity + this.minPackageUnit + "/" + this.packageUnit;
    }

    public void setSpecialDrug(String content) {
        this.specialDrug = content;
        this.specialDrugArr = Arrays.asList(content.replace("[", "").replace("]", "").split(" "));
    }
    public void setSpecialDrugArr(List<String> arr) {
        this.specialDrugArr = arr;
        // 将列表转换为字符串
        this.specialDrug = arr.stream()
                .filter(item -> item != null && !item.isEmpty()) // 过滤空字符串和 null
                .collect(Collectors.joining(" "));
    }
    public void setDrugTags(String content) {
        this.drugTags = content;
        this.drugTagsArr = Arrays.asList(content.replace("[", "").replace("]", "").split(" "));
    }
    public void setDrugTagsArr(List<String> arr) {
        this.drugTagsArr = arr;
        // 将列表转换为字符串
        this.drugTags = arr.stream()
                .filter(item -> item != null && !item.isEmpty()) // 过滤空字符串和 null
                .collect(Collectors.joining(" "));
    }

    public void setStorageCondition(String content) {
        this.storageCondition = content;
        this.storageConditionArr = Arrays.asList(content.replace("[", "").replace("]", "").split(" "));
    }
    public void setStorageConditionArr(List<String> arr) {
        this.storageConditionArr = arr;
        // 将列表转换为字符串
        this.storageCondition = arr.stream()
                .filter(item -> item != null && !item.isEmpty()) // 过滤空字符串和 null
                .collect(Collectors.joining(" "));
    }

    public void setApprovalValidDate(String content) {
        this.approvalValidDate = content;
        this.approvalValidDateArr = Arrays.asList(content.replace("[", "").replace("]", "").split(" "));
    }
    public void setApprovalValidDateArr(List<String> arr) {
        this.approvalValidDateArr = arr;
        // 将列表转换为字符串
        this.approvalValidDate = arr.stream()
                .filter(item -> item != null && !item.isEmpty()) // 过滤空字符串和 null
                .collect(Collectors.joining(" "));
    }

    public void setAntibacterialCategories(String content) {
        this.antibacterialCategories = content;
        this.antibacterialCategoriesArr = Arrays.asList(content.replace("[", "").replace("]", "").split(" "));
    }
    public void setAntibacterialCategoriesArr(List<String> arr) {
        this.antibacterialCategoriesArr = arr;
        // 将列表转换为字符串
        this.antibacterialCategories = arr.stream()
                .filter(item -> item != null && !item.isEmpty()) // 过滤空字符串和 null
                .collect(Collectors.joining(" "));
    }
} 