package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.myenum.HandlerType;
import com.main.service.inf.SmsService;
import com.main.util.ZwsxMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 2021/10/11 15:32
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {

    @Value("${sys.sms.ecan.webUrl}")
    private String webServiceURL;
    @Value("${sys.sms.ecan.fromType}")
    private String fromType;
    @Value("${sys.sms.ecan.key}")
    private String key;
    @Value("${sys.sms.ecan.orgCode}")
    private String orgCode;
    @Value("${sys.sms.sms-monitor-content}")
    private String smsContent;
    @Value("${sys.sms.sms-uptpwd-content}")
    private String smsPwdContent;


    @Override
    public ResponseVO<Map<String, Object>> sendOneMessage(long phoneNo, String code) throws MalformedURLException, Exception {
        return this.sendOneMessage(phoneNo, code, "0");
    }

    /**
     * 发送单条短信
     *
     * @param phoneNo 手机号
     * @param code    验证码
     * @return
     */
    @Override
    public ResponseVO<Map<String, Object>> sendOneMessage(long phoneNo, String code, String type) throws Exception {

        ResponseVO<Map<String, Object>> responseVO = new ResponseVO<>();
        //对接公司短信平台，只需要实现发送短信功能即可。调试的时候就当发送成功了。不耽误编写登录功能。

        Assert.assertNotNull("未配置短信接口地址", webServiceURL);
        long timestamp = System.currentTimeMillis();
        //指出service所在完整的URL

        //创建服务名称
        String smsContentNew = null;
        //修改密码发送的短信内容
        if ("1".equals(type)) {
            smsContentNew = smsPwdContent.replace("{code}", code);
        } else {
            smsContentNew = smsContent.replace("{code}", code);
        }
        Map<String, String> map = new HashMap<>();
        map.put("orgCode", orgCode);
        map.put("fromType", fromType);
        map.put("timestamp", String.valueOf(timestamp));
        map.put("key", String.valueOf(key));
        map.put("returnType", "1");
        map.put("calledNumber", String.valueOf(phoneNo));
        map.put("content", String.valueOf(smsContentNew));

        String result = ZwsxMessageUtil.sendByOne(map);

        log.info("短信平台-出参：{}", result);
        JSONObject resultObj = JSONObject.parseObject(result);

        int error = 1;
        try {
            JSONObject datas = resultObj.getJSONObject("datas");
            error = datas.getInteger("error");
        } catch (Exception e) {
            log.error("短信转换格式失败了，尝试转换为数组");
            JSONArray datas = resultObj.getJSONArray("datas");
            if (datas.size() > 0) {
                JSONObject data = datas.getJSONObject(0);
                error = data.getInteger("error");
                log.info("成功转换为数组并获取到error = {}", error);
            } else {
                log.info("尝试转换为数组也失败了！{}", JSONObject.toJSONString(resultObj));
            }
        }
        //失败
        if (!("100".equals(resultObj.getString("code")) && 0 == error)) {
            responseVO.setRetInfo(HandlerType.OUT_SYSTEM_ERROR);
            if ("100".equals(resultObj.getString("code"))) {
                responseVO.setMsg("短信平台通信成功！但运营商发送失败！");
            } else {
                responseVO.setMsg("短信平台通信失败！" + resultObj.getString("msg"));
            }
            //错误日志记录
            log.error("短信平台-出参：{}", result);
            log.error("短信平台-发送短信失败！手机号：{}，验证码：{}", phoneNo, code);
        }
        return responseVO;
    }

    /**
     * 生成短信验证码
     *
     * @param length 验证码位数
     * @return
     */
    @Override
    public String createVcode(int length) {
        //验证码
        StringBuffer vcode = new StringBuffer();
        for (int i = 0; i < length; i++) {
            vcode.append((int) (Math.random() * 9));
        }
        return vcode.toString();
    }
}

