package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.service.admin.OrgService;
import org.junit.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 机构表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@RestController
@RequestMapping("/org")
public class OrgHandler {

    @Resource
    private OrgService orgService;

    @PostMapping("/dept/tree")
    public ResponseVO<Map<String,Object>> getOrgDeptTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        Assert.assertNotNull("OID不能为空！",params.get("oid"));
        return orgService.getOrgDeptTreeData(String.valueOf(params.get("oid")), user);
    }

    @PostMapping("/tree")
    public ResponseVO<Map<String,Object>> getTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        Assert.assertNotNull("OID不能为空！",params.get("oid"));
        return orgService.getOrgTreeData(String.valueOf(params.get("oid")), user);
    }

    @LogAnnotation(option = "保存机构")
    @MyAuthority(auth="/admin/org/index")
    @PostMapping("/save")
    public ResponseVO<String> saveOrg(@RequestBody JSONObject org) {
        return orgService.saveOrg(org);
    }

    @LogAnnotation(option = "删除机构")
    @MyAuthority(auth="/admin/org/index")
    @PostMapping("/del")
    public ResponseVO<String> deleteOrg(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return orgService.deleteOrg(params.getString("oid"), params.getString("parentId"));
    }

}

