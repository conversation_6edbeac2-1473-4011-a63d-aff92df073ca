package com.main.service.util;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.common.Global;
import com.config.websocket.WebSocketHandler;
import com.constant.DPConstant;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.Session;
import com.main.pojo.admin.LogInfo;
import com.main.pojo.admin.Service;
import com.main.pojo.admin.User;
import com.main.pojo.admin.UserLoginRecord;
import com.main.service.inf.SmsService;
import com.main.service.admin.LogInfoService;
import com.main.service.admin.ServiceService;
import com.main.service.admin.UserLoginRecordService;
import com.main.service.admin.UserService;
import com.main.util.*;
import com.main.vo.IPEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

/**
 * 异步执行样例
 *
 * <AUTHOR>
 * 2021/11/17 14:30
 */
@Component
@Slf4j
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class AsyncProcesser {
	@Resource
	private RedisSrv redisSrv;

	@Resource
	private LogInfoService logInfoService;
	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private UserLoginRecordService userLoginRecordService;

	@Resource
	private ServiceService serviceService;

	@Resource(name = "smsServiceImpl")
	private SmsService smsServiceImpl;

	@Async
	@Deprecated
	public void listener(WebSSHService webSshService, String token, SshHost host, WebSocketHandler webSocketHandler, Session session) throws IOException {
		SSHConnectInfo sshConnectInfo = (SSHConnectInfo) WebSSHService.sshMap.get(token);
		Channel channel = sshConnectInfo.getChannel();
		//读取终端返回的信息流
		InputStream inputStream = channel.getInputStream();
		log.info("开始监听返参");
		try {
			//循环读取
			byte[] buffer = new byte[1024];
			int i = 0;
			//如果没有数据来，线程会一直阻塞在这个地方等待数据。
			while ((i = inputStream.read(buffer)) != -1 && channel.isConnected()) {
				log.info("Received:{}", new String(Arrays.copyOfRange(buffer, 0, i)));
				webSshService.sendMessage(host.getSshId(), new String(Arrays.copyOfRange(buffer, 0, i)),webSocketHandler);
				//重新获取channel看看是不是关闭了，关闭了就不要循环了
				sshConnectInfo = (SSHConnectInfo) WebSSHService.sshMap.get(token);
				channel = sshConnectInfo.getChannel();
				if(!channel.isConnected()) {
					break;
				}
			}
		} finally {
			//断开连接后关闭会话
			session.disconnect();
			channel.disconnect();
			if (inputStream != null) {
				inputStream.close();
			}
		}
	}

	/**
	 * websocket 连接成功或失败的时候，同步用户的在线离线状态
	 * @param state 状态 1 在线 0 离线 使用 DpConstant常量传递
	 * @param token token
	 */
	@Async
	public void syncUserOnlineState(UserService userService, String state, String token) {
		long a = System.currentTimeMillis();
		log.info("开始异步同步用户状态业务");
		User user = redisSrv.getUserByToken(token);
		if(user != null) {
			LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.set(User::getOnline, state);
			updateWrapper.eq(User::getOid, user.getOid());
			userService.update(updateWrapper);
			//如果离线了，肯定要把他的token清除了
//			if(state.equals(DPConstant.USER_OFFLINE)) {
//				userService.logout(token);
//			}
		}
		long b = System.currentTimeMillis();
		log.info("结束异步同步用户状态业务，耗时:{} ms",b-a);
	}
	@Async
	public void handlerUserLoginInfo(UserService userService, IPEntity ipEntity, User userInfo) {
		LambdaUpdateWrapper<User> userLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		userLambdaUpdateWrapper.set(User::getLastLoginIp, ipEntity.getIpAddress());
		userLambdaUpdateWrapper.set(User::getLastLoginDay, DateUtil.getMyTime());
		userLambdaUpdateWrapper.set(User::getLastLoginLocation, ipEntity.getLocation());
		userLambdaUpdateWrapper.eq(User::getOid, userInfo.getOid());
		userService.update(userLambdaUpdateWrapper);

		//保留登录记录
		UserLoginRecord userLoginRecord = new UserLoginRecord();
		String hostIp = ipEntity.getIpAddress();
		if(CUtil.isEmpty(hostIp)){
			hostIp = "127.0.0.1";
		}
		String location = ipEntity.getLocation();
		if(!CUtil.isEmpty(location)) {
			location = location.replaceAll("null", "");
			location = location.replaceAll(" ", "");
			if(CUtil.isEmpty(location)) {
				location = "本地";
			}
		}
		userLoginRecord.setHostIp(hostIp);
		userLoginRecord.setLocation(location);
		if(CUtil.isNotEmpty(userInfo.getName())) {
			userLoginRecord.setUserName(userInfo.getName());
		}else {
			userLoginRecord.setUserName(userInfo.getPhone());
		}
		String time = DateUtil.getTime();
		String date = DateUtil.getDate();
		userLoginRecord.setLoginTime(date + time);
		userLoginRecord.setLogoutTime(null);
		String timesAlreadyFailed = redisTemplate.opsForValue().get(DPConstant.RK_RETRY_TIMES + userInfo.getObh());
		int times = Integer.parseInt(CUtil.nullToZero(timesAlreadyFailed));
		userLoginRecord.setLoginFailTimes(times);
		//清除连续失败的次数
		redisTemplate.delete(DPConstant.RK_RETRY_TIMES + userInfo.getObh());
		userLoginRecord.setUserOid(userInfo.getOid());
		userLoginRecord.setOprSys(ipEntity.getOsName());
		userLoginRecord.setWebName(ipEntity.getBrowser());
		userLoginRecord.setToken(userInfo.getToken());
		userLoginRecord.setOid(Global.createUUID());
		userLoginRecordService.save(userLoginRecord);
	}

	public void asyncSaveLogInfo(User user, String paramsJson, String ipAdrress, String className, String classMethod, String option, String jsonString) {
		LogInfo logInfo = new LogInfo();
		if (user != null) {
			logInfo.setUserName(user.getName());
			logInfo.setUserOid(user.getOid());
		}else {
			logInfo.setUserName("系统");
			logInfo.setUserOid("system");
		}
		logInfo.setUuid(Global.createUUID());
		logInfo.setMethod(className + ":" + classMethod);
		logInfo.setRemark(ipAdrress);
		logInfo.setDateStr(DateUtil.getCurrentFormatDateLong());
		logInfo.setParams(paramsJson);
		logInfo.setType(option);
		logInfo.setResp(jsonString);
		log.info("准备异步保存日志：{}", JSONObject.toJSONString(logInfo));
		logInfoService.save(logInfo);
	}

	@Async
    public void updateServiceStatus(String oid, int isStop, String warningMessage, String[] phone) {
		LambdaUpdateWrapper<Service> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.set(Service::getIsStop, isStop);
		updateWrapper.eq(Service::getOid, oid);
		serviceService.update(updateWrapper);
		try {
			boolean lock = RedissLockUtil.tryLock("DEV_PLATFORM:SMS:MONITOR:" + oid + ":"+isStop+":");
			if (lock) {
				try {
					//发送短信通知
					for (String singlePhone : phone) {
						if(CUtil.isNotEmpty(singlePhone)) {
							try {
								String value = redisSrv.getParam("DEV_PLATFORM:SMS:MONITOR:" + oid + ":"+isStop+":"+singlePhone);
								if(CUtil.isEmpty(value)) {
									log.info("开始给{},发送短信内容：{}", singlePhone, warningMessage);
									smsServiceImpl.sendOneMessage(Long.parseLong(singlePhone), warningMessage, null );
								}
								redisSrv.setParam("DEV_PLATFORM:SMS:MONITOR:"+ oid + ":"+isStop+":"+singlePhone, "1", 60*60);
							} catch (Exception e) {
								CUtil.getStackTraceString(e);
								log.error("发送短信失败，{}, {}", singlePhone, warningMessage);
							}
						}
					}
				}catch (Exception e) {
					CUtil.getStackTraceString(e);
				}finally {
					RedissLockUtil.unlock("DEV_PLATFORM:SMS:MONITOR:" + oid + ":"+isStop+":");
				}
			}
		}catch (Exception e) {
			CUtil.getStackTraceString(e);
		}

    }

}
