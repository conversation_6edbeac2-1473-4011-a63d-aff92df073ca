package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 患者-标签关联实体类
 * <AUTHOR>
 */
@Data
@TableName("t_patient_tag_rel")
public class PatientTagRel {
    /**
     * 主键ID
     */
    @TableId
    private String oid;
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 标签ID
     */
    private String tagId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人ID
     */
    private String createBy;
} 