package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Res;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

import java.util.Map;

/**
 * <p>
 * 菜单资源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface ResService extends IService<Res> {
    ResponseVO<Map<String, Object>> getTreeData(String oid, String enable, User user);

    ResponseVO<String> save(JSONObject obj);

    /**
     * 更新叶子节点信息
     * @param oid  对应修改的id
     * @param isLeaf 对应修改的叶子节点标识
     */
    boolean updateLeaf(String oid, int isLeaf);

    /**
     * 获取自动编码
     * @param parentId 父节点
     * @return
     */
    String getId(String parentId);

    /**
     * 删除机构
     * @param oid id
     * @return
     */
    ResponseVO<String> delete(String oid, String parentId);
}
