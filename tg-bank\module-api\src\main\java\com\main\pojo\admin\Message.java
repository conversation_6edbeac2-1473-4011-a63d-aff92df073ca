package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.common.Global;
import com.constant.DPConstant;
import lombok.Data;

import java.util.Date;

/**
 * 消息实体类
 * <AUTHOR>
 */
@Data
@TableName("d_message")
public class Message {
    /**
     * 主键ID
     */
    @TableId
    private String oid;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型 消息类型(SYSTEM-系统消息,PERSONAL-公告通知,CHAT-聊天消息)
     */
    private String type;
    
    /**
     * 发送人ID
     */
    private String senderId;
    
    /**
     * 发送人姓名
     */
    private String senderName;

    /**
     * 发送人所属机构ID
     */
    private String orgId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 删除标记
     */
    private Integer delFlag;
    
    /**
     * 接收范围(ALL-全部人员, ORG-机构, CURRORG-当前登录机构,USER-指定用户)
     */
    private String receiveScope;
    
    /**
     * 接收对象ID(机构ID/用户ID列表)
     */
    private String receiveTarget;

    // 前端使用字段-是否已读，0 否 1 是 仅用于通知公告是否已读，聊天请使用chatReadStatus
    @TableField(exist = false)
    private int readStatus;

    //以下字段为聊天消息专用字段，其他消息无需考虑
    private int senderChatSex;
    private String senderChatPhone;
    private int receiveTargetChatSex;
    private String receiveTargetChatPhone;
    private int isImage; //是否是图片
    private String quoteOid; //引用oid
    private String quoteContent; //引用内容
    private Date quoteTimestamp; //引用时间
    private int chatReadStatus; //聊天内容是否已读

    private void init() {
        this.type = DPConstant.WS_MSG_TYPE_MESSAGE;
        this.oid = Global.createUUID();
        this.createTime = new Date();
        this.senderId = "SYSTEM";
        this.senderName = "系统消息";
        this.delFlag = 0;
    }

    private Message() {
        super();
    }

    private Message(String type, String content) {
        this.init();
        this.type = type;
        this.content = content;
    }

    private Message(String content) {
        this.init();
        this.content = content;
    }

    private Message(String type, String title, String content) {
        this.init();
        this.type = type;
        this.title = title;
        this.content = content;
    }

    public static Message getOne(String type, String content) {
        return new Message(type, content);
    }

    public static Message getOne(String content) {
        return new Message(content);
    }
    public static Message getOne(String type, String title, String content) {
        return new Message(type, title, content);
    }
}