package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.UserRole;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserRoleService extends IService<UserRole> {

    boolean delete(String userOid);

    boolean mySave(String userOid, String roles);

    boolean mySave(String userOid, JSONArray roles);

    List<Role> getUserRoleList(String oid);
}
