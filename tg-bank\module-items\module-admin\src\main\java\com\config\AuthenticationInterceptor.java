package com.config;

import com.alibaba.fastjson.JSON;
import com.common.Global;
import com.config.websocket.WebSocketHandler;
import com.constant.DPConstant;
import com.main.bean.vo.ResponseVO;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.User;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor {
    @Value("${sys.login.ttl:24}")
    private long ttl;
    @Value("${sys.login.valid:1}")
    private int enableFilter;
    //登录是否跳转用户中台，是否拦截登录
    @Value("${sys.login.valid}")
    private int valid;
    //调回前端地址
    @Value("${server.port}")
    private String sysPort;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedisSrv redisSrv;

    @Resource
    private WebSocketHandler webSocketHandler;

    public static String getToken(HttpServletRequest request) {
        String tokenParam = request.getParameter(Global.TOKENHEADER);
        if (CUtil.isEmpty(tokenParam)) {
            tokenParam = request.getHeader(Global.TOKENHEADER);
        }
        if (CUtil.isEmpty(tokenParam)) {
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if (cookie.getName().equals(Global.TOKENHEADER) || cookie.getName().equals(Global.TOKENHEADER)) {
                        tokenParam = cookie.getValue();
                    }
                }
            }
        }
        if (CUtil.isEmpty(tokenParam)) {
            String referer = request.getHeader("referer");
            if (CUtil.isNotEmpty(referer) && referer.contains(Global.TOKENHEADER)) {
                tokenParam = referer.substring(referer.indexOf(Global.TOKENHEADER) + Global.TOKENHEADER.length() + 1);
            }
        }
        return tokenParam;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        long a = System.currentTimeMillis();
        String tokenParam = getToken(request);
        if (enableFilter == 0) {
            return true;
        } else {
            if (StringUtils.isNotBlank(tokenParam)) {
                //验证tokenParam是否正确
                try {
                    User user = redisSrv.getUserByToken(tokenParam);
                    if (user != null) {
                        if (user.getStatus().equals("3")) {
                            log.info("该账号在另外一个地方登录！，tokenParam: {}", tokenParam);
                            redisSrv.delete(DPConstant.RK_DP_TOKEN + tokenParam);
                            webSocketHandler.close(tokenParam);
                            return this.responseFalse(request, response, "该账号在另外一个地方登录！当前账号被挤下线！如果不是您的行为则密码可能泄露，请修改密码！");
                        } else if (user.getStatus().equals("4")) {
                            redisSrv.delete(DPConstant.RK_DP_TOKEN + tokenParam);
                            webSocketHandler.close(tokenParam);
                            return this.responseFalse(request, response, "该账号被管理员强制下线！请联系管理员或稍后重登！");
                        } else {
                            long b = System.currentTimeMillis();
//                            log.info("登录拦截器耗时：{}",b-a);
                            //记录用户在线状态
                            return true;
                        }
                    } else {
                        webSocketHandler.close(tokenParam);
                        log.info("登录过期请重新登录，tokenParam: {}", tokenParam);
                        return this.responseFalse(request, response, "登录已过期或尚未登录，请重新登录系统！");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return this.responseFalse(request, response, "系统错误：" + e.getMessage());
                }
            } else {
                log.info("拦截器拦截URL = {}", request.getRequestURI());
                log.info("未发现HEADER入参 ： " + Global.TOKENHEADER);
                return this.responseFalse(request, response, "未发现鉴权标识！请重新登录系统！");
            }
        }
    }


    private boolean responseFalse(HttpServletRequest request, HttpServletResponse response, String error) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        ResponseVO<String> responseVO = new ResponseVO<>();
        responseVO.setRetInfo(HandlerType.SRV_LOGIN_INVALID);
        responseVO.setMsg(error);
        //上传文件用的是这个
        responseVO.setError(error);
        log.info("登录拦截器：登录失败！信息:{}", JSON.toJSONString(responseVO));
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(responseVO));
        out.flush();
        out.close();
        return false;
    }

//    private boolean responseLoginPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        response.setCharacterEncoding("UTF-8");
//        response.setHeader("Access-Control-Allow-Origin", "*");
//        response.setContentType("text/html; charset=utf-8");
//        PrintWriter out = response.getWriter();
//        out.println("<script lang=\"ts\">");
//        out.println(
//                "function setCookie(cname, cvalue, exdays) {\n" +
//                        "    var d = new Date();\n" +
//                        "    d.setTime(d.getTime() + (exdays*24*60*60*1000));\n" +
//                        "    var expires = \"expires=\"+d.toUTCString();\n" +
//                        "    document.cookie = cname + \"=\" + cvalue + \"; \" + expires;\n" +
//                        "}");
//        out.println("setCookie(\"" + Global.TOKENHEADER + "\", \"\", -1);  \n");
//        String fullPath = request.getRequestURL().toString();
//        String url = "http://www.baidu.com";
//        log.info("登录拦截器：登录失败！跳转到登录首页：{}", url);
//        out.println("window.top.location.href='" + url + "'");
//        out.println("</script>");
//        out.flush();
//        out.close();
//        return false;
//    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}
