<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:fragment="ui">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no">
    <!-- 导入样式 -->
    <link rel="stylesheet" th:href="@{/css/pui.v3.min.css}">
    <link rel="stylesheet" th:href="@{/css/remixicon.css}">
    <link rel="shortcut icon" th:href="@{/favicon.ico}">
    <!-- 导入 js -->
    <script type="text/javascript">
        /**
         * 获取url的参数
         * @param name
         * @returns {string|null}
         */
        function getQueryString(name) {
            let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
            let r = window.location.search.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            }
            return null;
        }

        /**
         * 获取项目根路径
         */
        function getRootPath() {
            var strFullPath = window.document.location.href;
            var strPath = document.location.pathname;
            if (strPath == null || strPath == '/') {
                return strFullPath;
            } else {
                var pos = strFullPath.indexOf(strPath);
                var prePath = strFullPath.substring(0, pos);
                var postPath = strPath.substring(0, strPath.substring(1).indexOf("/") + 1);
                return prePath + postPath;
            }
        }

        function getAppContext() {
            var strPath = document.location.pathname;
            if (strPath == null || strPath == '/') {
                return "/#";
            } else {
                var postPath = strPath.substring(0, strPath.substring(1).indexOf("/") + 1);
                return  postPath;
            }
        }

        function getHostIP() {
            var hrefs = getRootPath();
            var temp = hrefs.substring(6);
            return hrefs.substring(0, temp.indexOf(":") + 6);
        }
    </script>
</head>
</html>
