package com.config.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.Global;
import com.constant.DPConstant;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.User;
import com.main.service.admin.FileService;
import com.main.service.admin.UserService;
import com.main.service.util.AsyncProcesser;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import com.main.util.Constants;
import com.main.util.WebSSHService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class WebSocketHandler extends TextWebSocketHandler {
    private static AtomicInteger onlineCount = new AtomicInteger(0);
    
    /**
     * 用来存放每个客户端对应的 WebSocketHandler 对象
     * key: token
     * value: WebSocketHandler实例
     */
    private static final ConcurrentHashMap<String, WebSocketHandler> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 存放 WebSocket Session
     * key: token
     * value: WebSocketSession 
     */
    private static final ConcurrentHashMap<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();

    private String id;
    private StringRedisTemplate template;

    private RedisSrv redisSrv;

    private AsyncProcesser asyncProcesser;

    private FileService fileService;

    private WebSSHService webSSHService;
    private UserService userService;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Map<String, Object> params = session.getAttributes();
        String url = Objects.requireNonNull(session.getUri()).toString();
        String token = url.substring(url.indexOf(Global.TOKENHEADER) + Global.TOKENHEADER.length() + 1);
        
        try {
            ApplicationContext act = ApplicationContextRegister.getApplicationContext();
            template = act.getBean(StringRedisTemplate.class);
            webSSHService = act.getBean(WebSSHService.class);
            redisSrv = act.getBean(RedisSrv.class);
            userService = act.getBean(UserService.class);
            asyncProcesser = act.getBean(AsyncProcesser.class);
            webSSHService.setAsyncProcesser(asyncProcesser);

            User user = redisSrv.getUserByToken(token);
            if (user != null) {
                this.id = token;
                webSSHService.initConnection(this.id);
                
                if (webSocketMap.containsKey(this.id)) {
                    WebSocketHandler oldHandler = webSocketMap.get(this.id);
                    try {
                        oldHandler.close(this.id);
                    } catch (Exception e) {
                        log.error("关闭旧连接失败: {}", e.getMessage());
                    }
                }
                
                webSocketMap.put(this.id, this);
                sessionMap.put(this.id, session);
                addOnlineCount();
                
                log.info("用户连接成功 - token: {}, 当前在线数: {}, 当前连接数: {}", 
                    token, getOnlineCount(), webSocketMap.size());
                
                asyncProcesser.syncUserOnlineState(userService, DPConstant.USER_ONLINE, this.id);
            } else {
                sendMessage(token, Message.getOne("用户未登录"));
                session.close();
            }
        } catch (IOException e) {
            log.error("WebSocket连接异常 - token: {}, error: {}", token, e.getMessage());
            throw e;
        }
    }

    /**
     * 接收客户端的消息请求
     *
     * @param session WebSocketSession
     * @param message TextMessage
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // 处理接收到的文本消息
        String receivedMessage = message.getPayload(); // 获取接收到的消息内容
        log.warn("接收到的消息：{}", receivedMessage); // 打印接收到的消息内容
        JSONObject jsonObject = JSON.parseObject(receivedMessage);
        String type = jsonObject.getString("type");
        String token = jsonObject.getString("token");
        if(CUtil.isEmpty(token) || CUtil.isEmpty(type)) {
            sendMessage(token, Message.getOne(DPConstant.WS_MSG_TYPE_SHELL, "请确保type 和 token 入参不为空！"));
        }else {
            switch (type) {
                case "shell":
                    ApplicationContext act = ApplicationContextRegister.getApplicationContext();
                    fileService = act.getBean(FileService.class);
                    fileService.receiveWebSocket(jsonObject);
                    break;
                case "ping":
                    break;
                default:
                    sendMessage(token, Message.getOne("未找到type对应的实现类！"));
                    break;

            }
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String token = this.id;
        if (token != null) {
            close(token);
            log.info("连接关闭 - token: {}, 当前在线数: {}, 当前连接数: {}", 
                token, getOnlineCount(), webSocketMap.size());
        }
    }

    /**
     * 向客户端发送消息，外部服务需要发送消息的请访问此接口
     * @description: 分布式  使用redis 去发布消息
     */
    public void sendMessage(@NotNull String key, Message message) {
        ApplicationContext act = ApplicationContextRegister.getApplicationContext();
        template = act.getBean(StringRedisTemplate.class);
        String newMessge = null;
        newMessge = new String(JSON.toJSONString(message).getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        Map<String, String> map = new HashMap<>();
        map.put(Constants.REDIS_MESSAGE_KEY, key);
        map.put(Constants.REDIS_MESSAGE_VALUE, newMessge);
        template.convertAndSend(Constants.REDIS_CHANNEL, JSON.toJSONString(map));
    }

    public void close(String token) {
        if (webSocketMap.containsKey(token)) {
            try {
                webSocketMap.remove(token);
                WebSocketSession session = sessionMap.remove(token);
                if (session != null && session.isOpen()) {
                    session.close();
                }
                
                subOnlineCount();

                ApplicationContext act = ApplicationContextRegister.getApplicationContext();
                asyncProcesser = act.getBean(AsyncProcesser.class);
                userService = act.getBean(UserService.class);
                asyncProcesser.syncUserOnlineState(userService, DPConstant.USER_OFFLINE, token);
                
                webSSHService = act.getBean(WebSSHService.class);
                webSSHService.close(token);
                
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败 - token: {}, error: {}", token, e.getMessage());
            }
        }
    }

    /**
     * 向客户端真的发消息 （本地模式）外部服务禁止直接通过此方法发送消息，否则不支持分布式部署，通过sendMessage方法发送。
     *
     * @param key
     * @param message
     */
    void sendMessageByWayBillId(@NotNull String key, String message) {
        if(!"ALL".equals(key)) {
            WebSocketHandler webSocketServer = webSocketMap.get(key);
            if (!CUtil.isEmpty(webSocketServer)) {
                try {
                    webSocketServer.sendMessage(key, message);
                } catch (IOException e) {
                    CUtil.getStackTraceString(e);
                    log.error("编号id为：{}，发送消息：{}。失败！", key, message);
                }
            }
        } else {
            //群发
            webSocketMap.keySet().forEach(keyTemp -> {
                WebSocketHandler webSocketServer = webSocketMap.get(keyTemp);
                if (!CUtil.isEmpty(webSocketServer)) {
                    try {
                        webSocketServer.sendMessage(keyTemp, message);
                    } catch (IOException e) {
                        CUtil.getStackTraceString(e);
                        log.error("编号id为：{}，发送消息：{}。失败！", keyTemp, message);
                    }
                }
            });
        }
//        log.error("WebSocketHandler-编号id号为：" + key + " 未连接");
    }

    /**
     * 实现服务器主动推送
     */
    void sendMessage(String key, String message) throws IOException {
        sessionMap.get(key).sendMessage(new TextMessage(message));
    }

    public static synchronized AtomicInteger getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        WebSocketHandler.onlineCount.getAndIncrement();
    }

    public static synchronized void subOnlineCount() {
        WebSocketHandler.onlineCount.getAndDecrement();
    }
}