package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.File;
import com.main.pojo.admin.User;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface FileService {
    ResponseVO<Map<String, Object>> getTreeData(User user, String serverOid, String path);

    List<File> getFileList(JSONObject params, User user);

    ResponseVO<String> uploadFile(MultipartFile file, String fileUrl,String serverOid, User user);

    void downloadFile(HttpServletResponse response, String filePath, String fileName, String serverOid, User user);

    ResponseVO<String> delFile(String filePath, String serverOid, User user);

    void receiveWebSocket(JSONObject params);
}
