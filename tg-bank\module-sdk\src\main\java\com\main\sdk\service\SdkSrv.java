package com.main.sdk.service;

import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.util.SdkUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/5/23 11:07
 */
public class SdkSrv extends BaseSdkSrv {
	Logger logger = LoggerFactory.getLogger(this.getClass());

	//==============================构造方法==============================//
	public SdkSrv(String AioCloudSrv, String appId, String appSecret, String signType, String encryptType) {
		super(AioCloudSrv, appId, appSecret, signType, encryptType);
	}
	public SdkSrv(String appId, String appSecret, String signType, String encryptType) {
		super(appId, appSecret, signType, encryptType);
	}


	//==============================提供服务==============================//

	/**
	 * 统一入口服务
	 * @param method 方法名 eg: test.service.forfun
	 * @param version 版本号，默认版本号：DEFAULT.1.0
	 * @param param 业务入参
	 * @return
	 * @throws BusinessException
	 */
	public ResponseVO unifyCall(String method, String version, Map<String,Object> param){
		//根据方法名和版本号拼接对应的serviceID
		method = method.replace(".","/");
		if(SdkUtil.isEmpty(version)) version = "DEFAULT.1.0";
		String serviceId = method + "/" + version;
		logger.info("获取SERVICEID: {}",serviceId);

		return sendRequest(null,serviceId,param);
	}
	public ResponseVO unifyCall(String platform,String method,String version,Map<String,Object> param) {
		//根据方法名和版本号拼接对应的serviceID

		method = method.replace(".","/");
		if(SdkUtil.isEmpty(version)) version = "DEFAULT.1.0";
		String serviceId = method + "/" + version;
		logger.info("获取SERVICEID: {}",serviceId);

		return sendRequest(platform,serviceId,param);
	}

}
