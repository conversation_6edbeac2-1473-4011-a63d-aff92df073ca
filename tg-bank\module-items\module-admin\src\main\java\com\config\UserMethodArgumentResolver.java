package com.config;

import com.common.Global;
import com.common.Login;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.User;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class UserMethodArgumentResolver implements HandlerMethodArgumentResolver {
    @Resource
    private RedisSrv redisSrv;
    @Value("${sys.login.valid:1}")
    private int enableFilter;
    @Value("${sys.login.token-efftime:2}")
    private long tokenEffTime;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return (null != methodParameter.getParameterAnnotation(Login.class) && User.class == methodParameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
        User user = null;
        try {
            String tokenParam = AuthenticationInterceptor.getToken(request);


            if (CUtil.isNotEmpty(tokenParam)) {
                user = redisSrv.getUserByToken(tokenParam);
            }
            if (user == null) {
                if (enableFilter == 0) {
                    user = getMonickUserInfo(tokenParam);
                } else {
                    log.error("获取登录用户失败了！redis获取为空！登录失效了！");
                    throw new BusinessException(HandlerType.SRV_LOGIN_INVALID);
                }
            }
            return user;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取登录用户失败！失败原因：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 模拟登录用户
     *
     * @param token 登录标识
     * @return 登录对象
     */
    private User getMonickUserInfo(String token) {
        if (CUtil.isEmpty(token)) {
            token = Global.createUUID();
        }
        log.info("无法获取登录用户信息，模拟返回用户：admin,内部测试用户 要修改检索：LoginHandlerMethodArgumentResolver.java");
        User user = new User();
        user.setOid("test");
        user.setName("模拟返回用户");
        user.setObh("test");
        user.setPhone("***********");
        user.setStatus("1");
        user.setSex(1);
        user.setIsAdmin(1);
        user.setToken(token);
        redisSrv.setToken(user, tokenEffTime);
        return user;
    }
}
