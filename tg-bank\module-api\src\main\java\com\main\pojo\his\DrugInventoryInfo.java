package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 药品库存信息实体类
 * <AUTHOR>
 */
@Data
@TableName("his_drug_inventory_info")
public class DrugInventoryInfo {
    /**
     * 诊所ID，联合主键
     */
    private String companyId;
    
    /**
     * 库房ID，联合主键
     */
    private String warehouseId;
    
    /**
     * 药品ID，联合主键
     */
    @TableId
    private String oid;
    
    /**
     * 当前库存数量
     */
    private Double currentStock;
    
    /**
     * 可用库存数量（当前库存减去已锁定数量）
     */
    private Double availableStock;
    
    /**
     * 库存金额（当前库存 * 进货价）
     */
    private Double stockValue;
    
    /**
     * 日均消耗量
     */
    private Double dailyConsumption;
    
    /**
     * 周转天数
     */
    private Integer turnoverDays;
    
    /**
     * 毛利率
     */
    private String profitRate;
    
    /**
     * 有效期至，格式：yyyy-MM-dd
     */
    private String expiryDate;
} 