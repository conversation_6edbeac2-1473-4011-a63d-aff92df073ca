package com.frame;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * mybatis plus page 监听，用于分页
 * <AUTHOR>
 * 2021/11/18 13:38
 */
@Configuration
public class MybatisPlusConfig {
	// 最新版
	@Bean
	public PaginationInterceptor paginationInterceptor(){
		return new PaginationInterceptor();
	}
}
