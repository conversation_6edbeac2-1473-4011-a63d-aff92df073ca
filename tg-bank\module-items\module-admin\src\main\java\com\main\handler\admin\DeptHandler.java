package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.service.admin.DeptService;
import org.junit.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 机构表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@RestController
@RequestMapping("/dept")
public class DeptHandler {

    @Resource
    private DeptService deptService;

    @PostMapping("/tree")
    public ResponseVO<Map<String,Object>> getTreeData(@RequestBody Map<String, Object> params) {
        Assert.assertNotNull("OID不能为空！",params.get("oid"));
        return deptService.getDeptTreeData(String.valueOf(params.get("oid")),String.valueOf(params.get("orgOid")));
    }

    @LogAnnotation(option = "保存科室")
    @MyAuthority(auth="/admin/org/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@RequestBody JSONObject dept) {
        return deptService.saveDept(dept);
    }

    @LogAnnotation(option = "删除科室")
    @MyAuthority(auth="/admin/org/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params) {
        return deptService.delete(params.getString("orgOid"), params.getString("oid"), params.getString("parentId"));
    }

}

