package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.CloudMapper;
import com.main.pojo.admin.Cloud;
import com.main.pojo.admin.User;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.util.BeanUtils;
import com.main.util.CUtil;
import com.main.util.Global;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 受监控的云资源基本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Service
public class CloudServiceImpl extends ServiceImpl<CloudMapper, Cloud> implements CloudService {

    @Resource
    private ServerService serverService;
    @Resource
    private ServiceService serviceService;

    @Override
    public ResponseVO<Map<String, Object>> getTreeData(User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Cloud> queryWrapper = new LambdaQueryWrapper<>();
        if (user.getIsAdmin() == 0 || user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看机构信息！请联系管理员");
        }
        List<Cloud> clouds = this.list(queryWrapper);
        List<Map<String, Object>> treeData = new ArrayList<>();
        clouds.forEach(cloud -> {
            Map<String, Object> treeNode = new HashMap<>();
            treeNode = BeanUtils.autoBeanToMap(cloud);
            treeNode.put("label", cloud.getName());
            treeNode.put("value", cloud.getOid());
            treeNode.put("oid", cloud.getOid());
            treeNode.put("name", cloud.getName());
            treeNode.put("disabled", cloud.getIsStop() != 0);
            treeData.add(treeNode);
        });
        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        response.setData(retDataMap);
        return response;
    }

    @Override
    public ResponseVO<String> saveCloud(JSONObject cloud) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Cloud cloudDB = cloud.toJavaObject(Cloud.class);
        if (CUtil.isEmpty(cloudDB.getOid())) {
            //ADD
            cloudDB.setOid(Global.createUUID());
            this.save(cloudDB);
        } else {
            //UPDATE
            this.updateById(cloudDB);
        }

        return responseVO;
    }

    @Override
    public ResponseVO<String> deleteCloud(String oid, String parentId) {
        ResponseVO<String> response = new ResponseVO<>();
        if(CUtil.isNotEmpty(oid)) {
            this.removeById(oid);
            serverService.removeByCloudOid(oid);
            serviceService.removeByCloudOid(oid);
        }
        return response;
    }
}
