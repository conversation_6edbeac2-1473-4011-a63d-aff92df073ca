package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.common.Global;
import com.common.StringUtil;
import com.common.ZjmUtil;
import com.main.util.DateUtil;
import com.main.util.MD5Util;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    //必填
    @TableId
    private String oid;

    //必填
    @TableId
    private String obh;

    //必填
    private String phone;

    private String email;

    private String name;

    @TableField(exist = false)
    private String avatar;

    private String zjm;

    private String pwd;

    private String birthday;

    // 0女 1男 2未知
    private Integer sex=2;

    private String idCard;

    /**
     * status 1 enable 0 disable 2 locked 3 挤出登录 4 强制下线
     */
    private String status = "1";

    private String entryDay;

    private Integer online = 0;

    private String lastLoginDay;
    private String lastLoginIp;
    private String lastLoginLocation;

    /**
     * 是否超级管理员 -1普通 0机构 1 超级
     */
    private Integer isAdmin = -1;


    /** ===========以下参数辅助增删改用的，其他时候无数据。======= **/
    @TableField(exist=false)
    private String role;

    @TableField(exist=false)
    private String oriPwd;

    //前台输入的密码
    @TableField(exist=false)
    private String newPwd;

    //前台输入的重复密码
    @TableField(exist=false)
    private String repPwd;

    /** ===========以下参数用户登录的时候会获取。============= **/
    @TableField(exist=false)
    private String token;

    @TableField(exist=false)
    private List<Res> resList;

    @TableField(exist=false)
    private List<String> roleList; //这里只是存储角色的id，历史遗留问题就不该命名了

    @TableField(exist=false)
    private List<Role> roleInfoList;

    @TableField(exist=false)
    private List<Org> orgList;

    @TableField(exist=false)
    private Org currentLoginOrg;

    //聊天专用字段
    //最近一条消息
    @TableField(exist=false)
    private String lastMessage;
    //未读聊天数量
    @TableField(exist=false)
    private int unread;

    public User() {
        super();
    }
    public User(String userBh, String userName, String phone, String pwd) {
        this.oid = Global.createUUID();
        this.obh = userBh;
        this.name = userName;
        this.sex = 1;
        this.phone = phone;
        this.status = "1";
        this.zjm = ZjmUtil.generateZJM(name);
        this.pwd = MD5Util.encryptWithSalt(pwd);
        this.entryDay = DateUtil.getCurrentFormatDateLong();
    }

    public void addRole(String roleStr) {
        if(StringUtil.isEmpty(getRole())) {
            setRole(roleStr);
        }else {
            setRole(getRole()+","+roleStr);
        }
    }
}
