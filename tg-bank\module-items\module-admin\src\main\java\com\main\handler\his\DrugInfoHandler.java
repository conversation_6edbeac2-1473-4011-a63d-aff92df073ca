package com.main.handler.his;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.Login;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Res;
import com.main.pojo.admin.User;
import com.main.pojo.his.DrugBasicInfo;
import com.main.pojo.his.DrugExtendedInfo;
import com.main.pojo.his.DrugInventoryInfo;
import com.main.pojo.his.DrugPricingInfo;
import com.main.pojo.his.vo.DrugInfoVO;
import com.main.service.his.DrugInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 药品库存管理处理器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/his/drug")
@RequiredArgsConstructor
public class DrugInfoHandler {

    /**
     * 药品信息服务
     */
    private final DrugInfoService drugInfoService;

    /**
     * 分页查询药品信息列表
     * @param user 当前登录用户
     * @param param 查询参数，包含：
     *              pageNum - 页码（默认1）
     *              pageSize - 每页数量（默认10）
     *              keyword - 搜索关键字（可选）
     *              warehouseId - 库房ID
     * @return 药品信息分页列表
     */
    @PostMapping("/page")
    public ResponseVO<Map<String, Object>> pageList(
            @Login User user,
            @RequestBody JSONObject param) {
        Integer pageNum = param.getInteger("pageNum");
        Integer pageSize = param.getInteger("pageSize");
        String keyword = param.getString("keyword");
        String warehouseId = param.getString("warehouseId");
        // 设置默认值
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        
        return drugInfoService.pageList(user, pageNum, pageSize, keyword, warehouseId, param);
    }

    /**
     * 根据ID获取药品详细信息
     * @param user 当前登录用户
     * @param warehouseId 库房ID
     * @param oid 药品ID
     * @return 药品详细信息
     */
    @GetMapping("/{warehouseId}/{oid}")
    public ResponseVO<DrugInfoVO> getById(
            @Login User user,
            @PathVariable String warehouseId,
            @PathVariable String oid) {
        return drugInfoService.getById(user.getCurrentLoginOrg().getOid(), warehouseId, oid);
    }


    /**
     * 保存或更新药品基本信息
     * @param user 当前登录用户
     * @param drugBasicInfo 药品基本信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    @PostMapping("/basic/{warehouseId}")
    public ResponseVO<Boolean> saveOrUpdateBasicInfo(
            @Login User user,
            @RequestBody DrugBasicInfo drugBasicInfo,
            @PathVariable String warehouseId) {
        return drugInfoService.saveOrUpdateBasicInfo(user, drugBasicInfo, warehouseId);
    }

    /**
     * 保存或更新药品扩展信息
     * @param user 当前登录用户
     * @param drugExtendedInfo 药品扩展信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    @PostMapping("/extended/{warehouseId}")
    public ResponseVO<Boolean> saveOrUpdateExtendedInfo(
            @Login User user,
            @RequestBody DrugExtendedInfo drugExtendedInfo,
            @PathVariable String warehouseId) {
        return drugInfoService.saveOrUpdateExtendedInfo(user, drugExtendedInfo, warehouseId);
    }

    /**
     * 保存或更新药品定价信息
     * @param user 当前登录用户
     * @param drugPricingInfo 药品定价信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    @PostMapping("/pricing/{warehouseId}")
    public ResponseVO<Boolean> saveOrUpdatePricingInfo(
            @Login User user,
            @RequestBody DrugPricingInfo drugPricingInfo,
            @PathVariable String warehouseId) {
        return drugInfoService.saveOrUpdatePricingInfo(user, drugPricingInfo, warehouseId);
    }

    /**
     * 保存或更新药品库存信息
     * @param user 当前登录用户
     * @param drugInventoryInfo 药品库存信息对象
     * @param warehouseId 库房ID
     * @return 操作结果
     */
    @PostMapping("/inventory/{warehouseId}")
    public ResponseVO<Boolean> saveOrUpdateInventoryInfo(
            @Login User user,
            @RequestBody DrugInventoryInfo drugInventoryInfo,
            @PathVariable String warehouseId) {
        return drugInfoService.saveOrUpdateInventoryInfo(user, drugInventoryInfo, warehouseId);
    }

    /**
     * 删除药品信息
     * @param user 当前登录用户
     * @param warehouseId 库房ID
     * @param oid 药品ID
     * @return 操作结果
     */
    @DeleteMapping("/{warehouseId}/{oid}")
    public ResponseVO<Boolean> delete(
            @Login User user,
            @PathVariable String warehouseId,
            @PathVariable String oid) {
        return drugInfoService.delete(user.getCurrentLoginOrg().getOid(), warehouseId, oid);
    }

    /**
     * 保存药品完整信息
     * @param user 当前登录用户
     * @param drugInfo 药品完整信息
     * @return 操作结果
     */
    @LogAnnotation(option = "保存药品信息")
    @PostMapping("/save")
    public ResponseVO<Boolean> saveDrugInfo(
            @Login User user,
            @RequestBody DrugInfoVO drugInfo) {
        return drugInfoService.saveDrugInfo(user, drugInfo);
    }

    @LogAnnotation(option = "更改药品状态")
    @PostMapping("/changeStatus")
    public ResponseVO<String> changeStatus(@Login User user, @RequestBody DrugInfoVO drugInfoVO) {
        return drugInfoService.changeDrugStatus(drugInfoVO.getOid(), 1-drugInfoVO.getEnable());
    }
} 