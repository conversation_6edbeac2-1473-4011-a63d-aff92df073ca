package com.main.sdk.service;

import com.main.bean.vo.RequestVO;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.myenum.URLType;
import com.main.util.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 2019/5/23 11:01
 */
public class BaseSdkSrv {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private final String API_URI = "/api/gateway.do";
	/**
	 * 服务地址
	 */
	private String web_pay_url = "http://127.0.0.1:8007/web-pay";

	/**
	 * 接口服务地址
	 */
	private String apiUrl = web_pay_url + API_URI;

	/**
	 * 应用id
	 */
	private String appId;

	/**
	 * 应用安全key
	 */
	private String appSecret;

	/**
	 * 签名类型
	 */
	private String signType;

	/**
	 * 加密类型
	 */
	private String encryptType;

	//==============================构造方法==============================//

	BaseSdkSrv(String web_pay_url, String appId, String appSecret, String signType, String encryptType) {
		super();
		this.web_pay_url = web_pay_url;
		this.apiUrl = web_pay_url + API_URI;
		this.appId = appId;
		this.appSecret = appSecret;
		this.signType = signType;
		this.encryptType = encryptType;
	}

	BaseSdkSrv(String appId, String appSecret, String signType, String encryptType) {
		this.appId = appId;
		this.appSecret = appSecret;
		this.signType = signType;
		this.encryptType = encryptType;
	}

	//===============================内部服务===============================//


	/**
	 * 验证签名
	 */
	private boolean verifySign(String oldSign, JSONObject res) throws BusinessException {
		String newSign = null;
		try {
			newSign = Signature.createSign(res, appSecret);
			return oldSign.equals(newSign);
		} catch (Exception e) {
			throw new BusinessException("验证签名失败,新签名："+newSign+",传递签名："+oldSign+",错误信息："+e.getMessage());
		}
	}

	/**
	 * 验证签名
	 *
	 * @throws BusinessException 支付业务异常
	 */
	public boolean verifySign(String oldSign, String responseMsg) throws BusinessException {
		return verifySign(oldSign, JSON.parseObject(responseMsg));
	}

	/**
	 * 验证签名
	 *
	 * @throws BusinessException 支付业务异常
	 */
	public boolean verifySign(String oldSign, ResponseVO<?> responseVO) throws BusinessException {
		String sign = responseVO.getSign();
		String bizContent = responseVO.getBizContent();
		//去除一些字段信息，不参与签名
		responseVO.setSign(null);
		responseVO.setBizContent(null);
		boolean result = verifySign(oldSign,JSON.parseObject(JSON.toJSONString(responseVO)));
		responseVO.setSign(sign);
		responseVO.setBizContent(bizContent);
		return result;
	}

	/**
	 * 验证请求签名
	 *
	 * @param requestMessage 请求消息
	 * @throws BusinessException 支付业务异常
	 */
	public boolean verifyRequestSign(String oldSign, String requestMessage) throws BusinessException {
		return verifySign(oldSign,JSON.parseObject(requestMessage));
	}

	//===============================提供服务===============================//

	/**
	 * 通用业务处理方法
	 */
	public <T> ResponseVO<T> sendRequest(String platform,String method, Object params) {
		// 创建请求参数对象
		RequestVO<Object> requestVO = new RequestVO<>();
		ResponseVO<T> responseVO = new ResponseVO<>();
		try {
			String timestamp = DateUtil.getCurrentDateTime();

			requestVO.setAppId(appId);
			requestVO.setTimestamp(timestamp);
			requestVO.setBizObj(params);
			requestVO.setMethod(method);
			requestVO.setPlatform(platform);
			requestVO.setSignType(signType);
			requestVO.setEncryptType(encryptType);

			// 创建签名信息
			logger.info("准备加密报文：入参明文与密钥：{},{}",JSONObject.toJSONString(requestVO),appSecret);
			String sign = Signature.createSign(requestVO, appSecret);
			requestVO.setSign(sign);

			// 加密报文
			try {
//				logger.info("加密前报文：{}", JSONObject.toJSONString(requestVO));
				String encryptData = SecurityUtil.encrypt(JSONObject.toJSONString(requestVO.getBizObj()), encryptType, appSecret, appId);
//				logger.info("加密后报文：{}", encryptData);
				requestVO.setBizContent(encryptData);
			} catch (Exception e) {
				e.printStackTrace();
				throw new BusinessException("请求报文加密失败");
			}

			// 清空明文
			requestVO.setBizObj(null);

			// 创建请求报文并发送请求
			String requestMessage = JSON.toJSONString(requestVO);
//			logger.info("请求报文【已加密】：{}", requestMessage);
			URLType urlType = URLType.HTTP;

			if (apiUrl.toLowerCase().startsWith("https")) {
				urlType = URLType.HTTPS;
			}

			//增加日志追踪号
			Map<String,String> headers = new HashMap<>();
			headers.put("traceId", MDC.get("traceId"));
			logger.info("OP-SDK获取到本地traceId即将传递给开放平台。traceId={}",MDC.get("traceId"));

			String responseMsg = HttpUtil.request(apiUrl,requestMessage, urlType,headers);

			if (SdkUtil.isEmpty(responseMsg)) {
				throw new BusinessException("请求错误");
			}
			// 参数转换
			JSONObject res = JSON.parseObject(responseMsg);

			responseVO.setSign(res.getString("sign"));
			responseVO.setSignType(res.getString("signType"));
			responseVO.setEncryptType(res.getString("encryptType"));
			responseVO.setTimestamp(res.getString("timestamp"));
			responseVO.setCode(res.getString("retCode"));
			responseVO.setMsg(res.getString("retMsg"));
			responseVO.setBizContent(res.getString("bizContent"));

			// 解密报文
			try {
				String encData = responseVO.getBizContent();

				if (SdkUtil.isNotEmpty(encData)) {
//					logger.info("返回报文解密前：{}", JSONObject.toJSONString(responseVO));
					String decryptData = SecurityUtil.decrypt(encData, encryptType, appSecret, appId);
					res.put("param", JSON.parseObject(decryptData));
					responseVO.setBizObj((T) JSON.parseObject(decryptData));
					responseVO.setBizContent(null);
					logger.info("返回报文解密后：{}", JSONObject.toJSONString(responseVO));
				}
			} catch (Exception e) {
				e.printStackTrace();
				throw new BusinessException("响应报文解密失败");
			}
			// 校验返回签名
			if (HandlerType.isSuccessful(responseVO) && !verifySign(responseVO.getSign(),responseVO)) {
				throw new BusinessException("验签失败");
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("发送请求SDK错误：{}",e.getMessage());
			responseVO.addException(e);
		}
		return responseVO;
	}


}
