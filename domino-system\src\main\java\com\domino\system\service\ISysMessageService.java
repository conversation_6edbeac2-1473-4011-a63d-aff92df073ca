package com.domino.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.qh.domain.SysMessage;

import java.util.List;

/**
 * 消息服务接口
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface ISysMessageService extends IService<SysMessage> {
    
    /**
     * 发送消息
     *
     * @param message 消息内容(包含接收范围和目标)
     * @param user 当前登录用户
     * @return 发送结果
     */
    AjaxResult sendMessage(SysMessage message, SysUser user);
    
    /**
     * 分页获取消息列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param type 消息类型
     * @param keyword 搜索关键字
     * @param readStatus 阅读状态
     * @param userOid 获取与谁的聊天记录
     * @param user 当前登录用户
     * @return 消息列表
     */
    AjaxResult pageMessages(int pageNum, int pageSize, String type, 
            String keyword, Integer readStatus, String userOid, SysUser user);
    
    /**
     * 标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @param user 当前登录用户
     * @return 操作结果
     */
    AjaxResult markAsRead(List<String> messageIds, SysUser user);
    
    /**
     * 一键标记所有消息为已读
     *
     * @param type CHAT or null 空表示系统消息或者机构通知
     * @param userOid 对方发给我的消息，仅针对聊天消息标记已读
     * @param user 当前登录用户
     * @return 操作结果
     */
    AjaxResult markAllAsRead(String type, String userOid, SysUser user);
    
    /**
     * 获取未读消息数量
     *
     * @param user 当前登录用户
     * @return 未读数量
     */
    AjaxResult getUnreadCount(SysUser user);

    /**
     * 删除聊天消息
     *
     * @param user 当前用户对象
     * @param userOid 需要被删除的对象 可能是发送者也可能是接收者
     * @return 操作结果
     */
    AjaxResult deleteChatMessage(SysUser user, String userOid);

    /**
     * 删除三天前的聊天图片
     *
     * @param oid 发送人
     * @param targetOid 目标用户
     */
    void removeChatPic(String oid, String targetOid);
}
