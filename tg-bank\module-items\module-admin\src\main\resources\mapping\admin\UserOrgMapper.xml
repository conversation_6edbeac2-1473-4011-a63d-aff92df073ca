<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.UserOrgMapper">

    <select id="getUserOrgList" resultType="com.main.pojo.admin.Org">
        select distinct a.choose, b.* from d_user_org a left join d_org b on a.org_oid = b.oid
        where a.user_oid = #{oid} and b.oid is not null
    </select>
    <select id="getUserDeptList" resultType="com.main.pojo.admin.Dept">
        select b.* from d_user_org a left join d_dept b on a.dept_oid = b.oid and a.org_oid = b.org_oid
        where a.user_oid = #{oid} and b.oid is not null
    </select>
</mapper>
