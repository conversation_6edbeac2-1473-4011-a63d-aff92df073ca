<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.system.mapper.SysMessageReceiverMapper">

    <resultMap type="com.domino.common.qh.domain.SysMessageReceiver" id="SysMessageReceiverResult">
        <id property="receiverId" column="receiver_id"/>
        <result property="messageId" column="message_id"/>
        <result property="userId" column="user_id"/>
        <result property="readTime" column="read_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 插入消息接收记录 -->
    <insert id="insertMessageReceiver" parameterType="com.domino.common.qh.domain.SysMessageReceiver">
        insert into sys_message_receiver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="receiverId != null and receiverId != ''">receiver_id,</if>
            <if test="messageId != null and messageId != ''">message_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="readTime != null">read_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="receiverId != null and receiverId != ''">#{receiverId},</if>
            <if test="messageId != null and messageId != ''">#{messageId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <!-- 批量插入消息接收记录 -->
    <insert id="batchInsertMessageReceiver" parameterType="java.util.List">
        insert into sys_message_receiver (receiver_id, message_id, user_id, read_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.receiverId}, #{item.messageId}, #{item.userId}, #{item.readTime}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 根据ID查询消息接收记录 -->
    <select id="selectMessageReceiverById" parameterType="String" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        where receiver_id = #{receiverId}
    </select>

    <!-- 查询消息接收记录列表 -->
    <select id="selectMessageReceiverList" parameterType="com.domino.common.qh.domain.SysMessageReceiver" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        <where>
            <if test="messageId != null and messageId != ''">
                and message_id = #{messageId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="readTime != null">
                and read_time = #{readTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 更新消息接收记录 -->
    <update id="updateMessageReceiver" parameterType="com.domino.common.qh.domain.SysMessageReceiver">
        update sys_message_receiver
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageId != null and messageId != ''">message_id = #{messageId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where receiver_id = #{receiverId}
    </update>

    <!-- 删除消息接收记录 -->
    <delete id="deleteMessageReceiverById" parameterType="String">
        delete from sys_message_receiver where receiver_id = #{receiverId}
    </delete>

    <!-- 批量删除消息接收记录 -->
    <delete id="deleteMessageReceiverByIds" parameterType="String">
        delete from sys_message_receiver where receiver_id in
        <foreach item="receiverId" collection="array" open="(" separator="," close=")">
            #{receiverId}
        </foreach>
    </delete>

    <!-- 根据消息ID删除接收记录 -->
    <delete id="deleteByMessageId" parameterType="String">
        delete from sys_message_receiver where message_id = #{messageId}
    </delete>

    <!-- 根据消息ID数组批量删除接收记录 -->
    <delete id="deleteByMessageIds" parameterType="String">
        delete from sys_message_receiver where message_id in
        <foreach item="messageId" collection="array" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </delete>

    <!-- 根据用户ID删除接收记录 -->
    <delete id="deleteByUserId" parameterType="String">
        delete from sys_message_receiver where user_id = #{userId}
    </delete>

    <!-- 根据消息ID和用户ID查询接收记录 -->
    <select id="selectByMessageIdAndUserId" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        where message_id = #{messageId} and user_id = #{userId}
    </select>

    <!-- 检查消息是否已读 -->
    <select id="checkMessageRead" resultType="boolean">
        select count(*) > 0
        from sys_message_receiver
        where message_id = #{messageId} and user_id = #{userId}
    </select>

    <!-- 根据消息ID查询所有接收者 -->
    <select id="selectReceiversByMessageId" parameterType="String" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        where message_id = #{messageId}
        order by create_time desc
    </select>

    <!-- 根据用户ID查询所有已读消息 -->
    <select id="selectReadMessagesByUserId" parameterType="String" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        where user_id = #{userId}
        order by read_time desc
    </select>

    <!-- 统计消息的已读人数 -->
    <select id="countReadByMessageId" parameterType="String" resultType="int">
        select count(*)
        from sys_message_receiver
        where message_id = #{messageId}
    </select>

    <!-- 统计用户的已读消息数量 -->
    <select id="countReadByUserId" parameterType="String" resultType="int">
        select count(*)
        from sys_message_receiver
        where user_id = #{userId}
    </select>

    <!-- 根据时间范围查询接收记录 -->
    <select id="selectByTimeRange" resultMap="SysMessageReceiverResult">
        select receiver_id, message_id, user_id, read_time, create_time
        from sys_message_receiver
        <where>
            <if test="startTime != null">
                and read_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and read_time &lt;= #{endTime}
            </if>
        </where>
        order by read_time desc
    </select>

    <!-- 清理过期的接收记录 -->
    <delete id="cleanExpiredRecords">
        delete from sys_message_receiver
        where create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
