<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.domino.system.mapper.SysMessageReceiverMapper">

    <resultMap type="com.domino.common.qh.domain.SysMessageReceiver" id="SysMessageReceiverResult">
        <id property="receiverId" column="receiver_id"/>
        <result property="messageId" column="message_id"/>
        <result property="userId" column="user_id"/>
        <result property="readTime" column="read_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

</mapper>
