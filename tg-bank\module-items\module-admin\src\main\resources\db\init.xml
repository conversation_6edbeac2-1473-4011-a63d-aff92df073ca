<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet author="killalak (generated)" id="generator-001">
        <createTable tableName="d_org" remarks="机构表">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="org_name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="org_short_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="is_stop" type="INT"/>
            <column name="parent_id" type="VARCHAR(64)"/>
            <column name="is_leaf" type="INT"/>
            <column name="type" type="INT" remarks="机构类型"/>
        </createTable>
        <createTable tableName="d_dic" remarks="通用字典表">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="icon" type="VARCHAR(64)" />
            <column name="val" type="VARCHAR(64)" />
            <column name="extra" type="VARCHAR(64)" />
            <column name="parent_id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="is_leaf" type="INT"/>
            <column name="is_stop" type="INT"/>
        </createTable>
        <createTable tableName="d_res" remarks="菜单资源表">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="res_url" type="VARCHAR(1000)"/>
            <column name="parent_id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="is_leaf" type="VARCHAR(64)"/>
            <column name="sort" type="INT"/>
            <column name="extra" type="VARCHAR(64)"/>
            <column name="is_btn" type="INT"/>
        </createTable>

        <createTable tableName="d_role">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="d_role_res">
            <column name="role_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="res_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="d_role_dic">
            <column name="role_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="dic_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="killalak (generated)" id="1663219780417-38">
        <createTable tableName="d_user">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="obh" type="VARCHAR(64)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="phone" type="VARCHAR(11)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="email" type="VARCHAR(64)"/>
            <column name="name" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="zjm" type="VARCHAR(64)"/>
            <column name="pwd" type="VARCHAR(64)"/>
            <column name="birthday" type="VARCHAR(10)"/>
            <column name="sex" type="INT"/>
            <column name="id_card" type="VARCHAR(64)"/>
            <column name="status" type="VARCHAR(64)"/>
            <column name="entry_day" type="VARCHAR(30)"/>
            <column name="online" type="INT"/>
            <column name="last_login_day" type="VARCHAR(30)"/>
            <column name="is_admin" type="INT" remarks="是否超级管理员 0否 1 是" defaultValue="0"/>
        </createTable>
    </changeSet>
    <changeSet author="killalak (generated)" id="1663219780417-39">
        <createTable tableName="d_user_org" remarks="用户关联机构">
            <column name="user_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="org_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="choose" type="INT" remarks="0否 1是"/>
        </createTable>
    </changeSet>

    <changeSet author="killalak (generated)" id="1663219780417-41">
        <createTable tableName="d_user_dic">
            <column name="user_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="dic_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="type_name" type="VARCHAR(64)"/>
            <column name="extra" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>
    <changeSet author="killalak (generated)" id="1663219780417-42">
        <createTable tableName="d_user_notice">
            <column name="user_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="notice_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="is_read" type="INT" remarks="0 未读  1 已读"/>
        </createTable>
    </changeSet>
    <changeSet author="killalak (generated)" id="1663219780417-45">
        <createTable tableName="d_user_role">
            <column name="user_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="role_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1663219780417-47" author="killalak">
        <createTable tableName="d_user_login_record" remarks="用户登录记录信息">
            <column name="oid" type="VARCHAR(60)" remarks="请求ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="param_infos" type="VARCHAR(2000)" remarks="请求入参集合"/>
            <column name="user_oid" type="VARCHAR(128)" remarks="用户主索引">
                <constraints nullable="false"/>
            </column>
            <column name="login_time" type="CHAR(14)" remarks="登录时间 yyyyMMddHHmmss"/>
            <column name="login_fail_times" type="INTEGER" remarks="登录失败次数"/>
            <column name="token" type="VARCHAR(60)" remarks="toke">
                <constraints nullable="false"/>
            </column>
            <column name="user_name" type="VARCHAR(20)" remarks="用户名称"/>
            <column name="host_ip" type="VARCHAR(80)" remarks="主机IP"/>
            <column name="location" type="VARCHAR(50)" remarks="登录地点"/>
            <column name="web_name" type="VARCHAR(36)" remarks="浏览器名称"/>
            <column name="opr_sys" type="VARCHAR(36)" remarks="操作系统名称"/>
            <column name="logout_time" type="CHAR(14)" remarks="会话过期时间"/>
        </createTable>
    </changeSet>

    <changeSet id="20231207153911" author="killalak">
        <createTable tableName="d_cloud" remarks="受监控的云资源基本信息">
            <column name="oid" type="VARCHAR(60)" remarks="请求ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(60)" remarks="云资源名称：鲲鹏云"/>
            <column name="is_stop" type="INT" remarks="是否停用"/>
        </createTable>
        <createTable tableName="d_server" remarks="云资源的服务器信息">
            <column name="oid" type="VARCHAR(60)" remarks="请求ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="cloud_oid" type="VARCHAR(60)" remarks="归属云" />
            <column name="name" type="VARCHAR(60)" remarks="服务器别称" />
            <column name="inner_ip" type="VARCHAR(32)" remarks="内网ip" />
            <column name="ssh_ip" type="VARCHAR(32)" remarks="ip" />
            <column name="ssh_port" type="VARCHAR(10)" remarks="ssh 端口" />
            <column name="ssh_user" type="VARCHAR(32)" remarks="ssh 账号" />
            <column name="ssh_pass" type="VARCHAR(32)" remarks="ssh 密码" />
            <column name="create_user" type="VARCHAR(64)" remarks="创建人" />
            <column name="create_time" type="CHAR(19)" remarks="创建时间"/>
            <column name="update_user" type="VARCHAR(64)" remarks="修改人" />
            <column name="update_time" type="CHAR(19)" remarks="修改时间"/>
            <column name="is_stop" type="INT" remarks="是否停用"/>
        </createTable>
        <createTable tableName="d_service" remarks="服务器部署的服务信息">
            <column name="oid" type="VARCHAR(60)" remarks="请求ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="cloud_oid" type="VARCHAR(60)" remarks="归属云" />
            <column name="server_oid" type="VARCHAR(60)" remarks="归属服务器" />
            <column name="name" type="VARCHAR(60)" remarks="应用名称"/>
            <column name="path" type="VARCHAR(500)" remarks="监控路径"/>
            <column name="description" type="VARCHAR(500)" remarks="应用描述"/>
            <column name="notice_phone" type="VARCHAR(11)" remarks="通知手机号"/>
            <column name="notice_phone_bak" type="VARCHAR(11)" remarks="备用手机号"/>
            <column name="notice_open_id" type="VARCHAR(64)" remarks="通知openid"/>
            <column name="is_server_view" type="INT" remarks="是否集成actuator"/>
            <column name="is_monitor" type="INT" remarks="是否监控"/>
            <column name="is_stop" type="INT" remarks="是否停用"/>
        </createTable>
    </changeSet>
    <changeSet author="killalak (generated)" id="20231208142811">
        <createTable tableName="d_log">
            <column name="uuid" type="VARCHAR(64)" remarks="主键">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="user_oid" type="VARCHAR(50)" remarks="用户Id">
                <constraints nullable="false"/>
            </column>
            <column name="user_name" type="VARCHAR(50)" remarks="用户姓名">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(10)" remarks="类型 0 用户操作  1 系统日志">
                <constraints nullable="false"/>
            </column>
            <column name="method" type="VARCHAR(100)" remarks="执行方法"/>
            <column name="params" type="VARCHAR(3500)" remarks="入参"/>
            <column name="resp" type="VARCHAR(3500)" remarks="出参"/>
            <column name="remark" type="VARCHAR(500)" remarks="备注"/>
            <column name="date_str" type="VARCHAR(25)" remarks="日期"/>
        </createTable>
    </changeSet>

    <changeSet author="chenfuqiang" id="d_res_upt_01">
        <addColumn tableName="d_res">
            <column name="front_name" type="VARCHAR(256)" remarks="头像地址"/>
        </addColumn>
    </changeSet>
    <changeSet author="chenfuqiang" id="d_res_upt_02">
        <addColumn tableName="d_res">
            <column name="icon" type="VARCHAR(64)" remarks="图标"/>
        </addColumn>
    </changeSet>
    <changeSet author="chenfuqiang" id="d_res_upt_03">
        <addColumn tableName="d_org">
            <column name="end_date" type="VARCHAR(30)" remarks="服务结束时间"/>
        </addColumn>
    </changeSet>
    <changeSet author="killalak" id="d_dept_upt_04">
        <createTable tableName="d_dept">
            <column name="oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="org_oid" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="dept_name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="is_stop" type="INT"/>
            <column name="parent_id" type="VARCHAR(64)"/>
            <column name="is_leaf" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="chenfuqiang" id="d_user_org_upt_230714">
        <addColumn tableName="d_user_org">
            <column name="dept_oid" type="VARCHAR(64)" remarks="所在部门">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="chenfuqiang" id="d_user_org_upt_230725">
        <addColumn tableName="d_res">
            <column name="is_stop" type="INT" remarks="是否停用 0 启用 1 停用">
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="killalak" id="d_role_upt_230728">
        <addColumn tableName="d_role">
            <column name="is_stop" type="INT" remarks="是否停用 0 启用 1 停用" defaultValue="0" />
        </addColumn>
    </changeSet>
    <changeSet author="killalak" id="d_role_upt_231020">
        <addColumn tableName="d_role">
            <column name="create_user_oid" type="VARCHAR(64)" remarks="创建人ID " />
        </addColumn>
    </changeSet>
    <changeSet author="killalak" id="d_user_upt_231115">
        <addColumn tableName="d_user">
            <column name="last_login_ip" type="VARCHAR(30)" remarks="最近登录ip " />
            <column name="last_login_location" type="VARCHAR(64)" remarks="最近登录地址 " />
        </addColumn>
    </changeSet>
    <!-- 消息通知 -->
    <include file="changelog/message.xml" relativeToChangelogFile="true"/>
</databaseChangeLog>
