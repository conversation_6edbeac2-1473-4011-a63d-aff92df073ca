module:
  #fixme 改项目名称 注意与config/application-config [sys.xxxx.]的xxxx匹配
  name: module-admin
server:
  servlet:
    context-path: /${sys.${module.name}.app-name}
    encoding:
      charset: UTF-8
      force: true
  #项目端口
  port: ${sys.${module.name}.app-port}
dubbo:
  application:
    id: ${sys.${module.name}.app-name}
    name: ${sys.${module.name}.app-name}
#  metadata-report:
#    address: ${sys.${module.name}.app-name}
  protocol:
    name: dubbo
    #Dubbo端口
    port: ${sys.${module.name}.dubbo-port}
  provider:
    loadbalance: roundrobin
    threadpool: fixed
    threads: 20
  server: true
# 服务端点详细监控信息
management:
#  trace:
#    http:
#      enabled: true
  endpoints:
    web:
      exposure:
        include: "health"
  endpoint:
    health:
      show-details: always
spring:
  application:
    name: ${sys.${module.name}.app-name}
  datasource:
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    filters: stat,slf4j
    initialSize: 10
    maxActive: 100
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 300000
    minIdle: 3
    name: ${module.name}-ds
    poolPreparedStatements: true
    timeBetweenEvictionRunsMillis: 300000
    useGlobalDataSourceStat: true
  liquibase:
    change-log: classpath:/db/master.xml
    enabled: true
  profiles:
    include: db,sys,self
  #资源缓存设置，首次加载后续就不用加载了。
  web:
    resources:
      chain:
        cache: true
        enabled: true
      cache:
        cachecontrol:
          max-age: 604800
          no-cache: false
          no-store: false
        period: 604800
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5
    prefix: classpath:/templates
    suffix: .html
  servlet:
    multipart:
      enabled: true #是否启用http上传处理
      max-request-size: 1500MB #最大请求文件的大小
      max-file-size: 1500MB #设置单个文件最大长度
      file-size-threshold: 20MB #当文件达到多少时进行磁盘写入
#jms:
#  pub-sub-domain: false
mybatis-plus:
  configuration:
    cache-enabled: false
    database-id: mysql
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      capital-mode: true
      column-underline: true
      field-strategy: not_empty
      id-type: uuid
    refresh: true
  mapper-locations: classpath:mapping/**/*.xml
  type-aliases-package: com.main.pojo,com.main.mapper

#logback配置
logging:
  config: classpath:logback-prd.xml
  level:
    com:
      baomidou:
        mybatisplus: DEBUG
      main:
        mapper: DEBUG
    org:
      springframework:
        security: DEBUG
