package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.handler.Api;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.User;
import com.main.service.admin.RoleService;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@RestController
@Slf4j
@RequestMapping("/role")
public class RoleHandler {

    @Resource
    private RoleService roleService;

    /**
     * 角色管理界面分页列表
     * @param params
     * @return
     */
    @RequestMapping("/page/list")
    public ResponseVO<Map<String, Object>> pageList(@RequestBody JSONObject params, @Login User user) {
        String start = String.valueOf(params.get("current"));
        String limit = String.valueOf(params.get("pageSize"));
        String keyWord = String.valueOf(params.get("keyWord"));
        int pageNum = Integer.parseInt(CUtil.nullToZero(start));
        int pageSize = Integer.parseInt(CUtil.nullToZero(limit));
        IPage<Role> roleListRecords = roleService.getPage(pageNum, pageSize, keyWord, user);
        return Api.packageTable(roleListRecords);
    }

    /**
     * 用户新增界面下拉角色列表接口
     * @param params
     * @return
     */
    @RequestMapping("/list")
    public ResponseVO<Map<String, Object>> list(@RequestBody Map<String, Object> params, @Login User user) {
        String oid = null;
        if(params.size() == 0) {
            oid = "";
        }else {
            oid = String.valueOf(params.get("oid"));
        }
        return roleService.getSelectList(oid, user);
    }

    @LogAnnotation(option = "保存角色")
    @MyAuthority(auth="/admin/role/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@RequestBody JSONObject role, @Login User user) {
        return roleService.saveRole(role, user);
    }

    @LogAnnotation(option = "删除角色")
    @MyAuthority(auth="/admin/role/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        return roleService.deleteRole(params.getString("oid"), user);
    }

    @MyAuthority(auth="/admin/role/index")
    @PostMapping("/getOne")
    public ResponseVO<Role> getOne(@RequestBody JSONObject params) {
        long a = System.currentTimeMillis();
        ResponseVO<Role> resp = roleService.getOne(params.getString("oid"));
        long b = System.currentTimeMillis();
        log.info("接口耗时：{}", b-a);
        return resp;
    }

    @LogAnnotation(option = "批量删除角色")
    @MyAuthority(auth="/admin/role/index")
    @PostMapping("/dels")
    public ResponseVO<String> deletes(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        ArrayList<String> oids = (ArrayList<String>) params.get("oids");
        return roleService.deleteRoles(oids);
    }

    @LogAnnotation(option = "编辑角色状态")
    @MyAuthority(auth="/admin/role/index")
    @PostMapping("/changeStatus")
    public ResponseVO<String> changeStatus(@RequestBody JSONObject params) {
        return roleService.changeStatus(params.getString("oid"), params.getString("isStop"));
    }
}

