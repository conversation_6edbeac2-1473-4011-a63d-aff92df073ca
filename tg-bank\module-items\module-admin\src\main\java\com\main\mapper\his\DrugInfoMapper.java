package com.main.mapper.his;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.pojo.his.DrugBasicInfo;
import com.main.pojo.his.vo.DrugInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 药品基本信息Mapper
 * <AUTHOR>
 */
@Mapper
public interface DrugInfoMapper extends BaseMapper<DrugBasicInfo> {
    
    /**
     * 分页查询药品信息
     * @param page 分页对象
     * @param keyword 搜索关键字
     * @param companyId 诊所ID
     * @param warehouseId 库房ID
     * @return 药品信息分页列表
     */
    Page<DrugInfoVO> selectDrugInfoPage(
            @Param("page") Page<DrugInfoVO> page,
            @Param("keyword") String keyword,
            @Param("companyId") String companyId,
            @Param("warehouseId") String warehouseId,
            @Param("param")JSONObject param
            );

    /**
     * 根据ID查询药品详细信息
     * @param companyId 诊所ID
     * @param warehouseId 库房ID
     * @param oid 药品ID
     * @return 药品详细信息
     */
    DrugInfoVO selectDrugInfoById(
            @Param("companyId") String companyId,
            @Param("warehouseId") String warehouseId,
            @Param("oid") String oid
    );
} 