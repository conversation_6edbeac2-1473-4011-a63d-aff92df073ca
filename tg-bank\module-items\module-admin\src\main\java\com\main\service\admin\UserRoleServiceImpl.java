package com.main.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.mapper.admin.UserRoleMapper;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.UserRole;
import com.main.util.CUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
@Slf4j
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    private final UserRoleMapper userRoleMapper;

    public UserRoleServiceImpl(UserRoleMapper userRoleMapper) {
        this.userRoleMapper = userRoleMapper;
    }

    @Override
    public boolean delete(String userOid) {
        LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
        if (userOid.contains(",")) {
            String[] oidArray = CUtil.splitArray(userOid);
            Assert.assertNotNull("当前数据为空，请勾选对应的数据进行删除！", oidArray);
            queryWrapper.in(UserRole::getUserOid, new ArrayList<>(Arrays.asList(oidArray)));
        } else {
            queryWrapper.eq(UserRole::getUserOid, userOid);
        }
        return this.remove(queryWrapper);
    }

    @Override
    public boolean mySave(String userOid, String roles) {
        String[] roleArray = CUtil.splitArray(roles);
        //删除 后新增
        if (roleArray != null) {
            this.delete(userOid);
            List<UserRole> userRoleList = new ArrayList<>();
            for (String s : roleArray) {
                userRoleList.add(new UserRole(userOid, s));
            }
            return this.saveBatch(userRoleList);
        } else {
            log.error("用户角色关联保存失败：传递的角色信息为空！roles={}", roles);
            return false;
        }
    }

    @Override
    public boolean mySave(String userOid, JSONArray roles) {
        //删除 后新增
        if (roles != null && roles.size() > 0) {
            this.delete(userOid);
            List<UserRole> userRoleList = new ArrayList<>();
            for (Object role : roles) {
                userRoleList.add(new UserRole(userOid, String.valueOf(role)));
            }
            return this.saveBatch(userRoleList);
        } else {
            log.error("用户角色关联保存失败：传递的角色信息为空！roles={}", JSON.toJSONString(roles));
            return false;
        }
    }

    @Override
    public List<Role> getUserRoleList(String oid) {
        return userRoleMapper.getUserRoleList(oid);
    }
}
