package com.domino.framework.websocket;

import com.domino.common.constant.Constants;
import com.domino.common.core.domain.model.LoginUser;
import com.domino.common.utils.StringUtils;
import com.domino.framework.web.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AuthHandshakeInterceptor implements HandshakeInterceptor {

    private final TokenService tokenService;

    public AuthHandshakeInterceptor(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
        try {
            // 从HTTP请求中提取Token
            String token = extractTokenFromRequest(request);

            if (StringUtils.isEmpty(token)) {
                log.warn("WebSocket连接失败：未提供Token");
                return false;
            }

            // 使用TokenService验证Token并获取用户信息
            LoginUser loginUser = validateToken(token);
            if (loginUser == null) {
                log.warn("WebSocket连接失败：无效的Token {}", token);
                return false;
            }

            // 验证Token有效期
            tokenService.verifyToken(loginUser);

            // 将用户信息存储到会话属性中，供WebSocketHandler使用
            attributes.put("userId", loginUser.getUser().getUserId().toString());
            attributes.put("userName", loginUser.getUser().getUserName());
            attributes.put("nickName", loginUser.getUser().getNickName());
            attributes.put("token", token);
            attributes.put("loginUser", loginUser);
            attributes.put("authenticated", true);

            // 设置Spring Security认证上下文
            setAuthenticationContext(loginUser);

            log.info("WebSocket Token验证成功，用户: {}", loginUser.getUser().getUserName());
            return true;

        } catch (Exception e) {
            log.error("WebSocket身份验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        // 握手后的处理，可以记录日志或执行清理操作
        if (exception != null) {
            log.error("WebSocket握手过程中发生异常: {}", exception.getMessage(), exception);
        } else {
            log.debug("WebSocket握手成功完成");
        }
    }

    /**
     * 从请求中提取Token
     * 支持多种方式：Authorization头、自定义Token头、查询参数
     */
    private String extractTokenFromRequest(ServerHttpRequest request) {
        try {
            // 方式1: 从Authorization Header中获取 (Bearer token)
            List<String> authHeaders = request.getHeaders().get("Authorization");
            if (authHeaders != null && !authHeaders.isEmpty()) {
                String authHeader = authHeaders.get(0);
                if (authHeader.startsWith(Constants.TOKEN_PREFIX)) {
                    return authHeader.substring(Constants.TOKEN_PREFIX.length()).trim();
                }
            }

            // 方式2: 从自定义Header中获取
            List<String> tokenHeaders = request.getHeaders().get("X-WebSocket-Token");
            if (tokenHeaders != null && !tokenHeaders.isEmpty()) {
                return tokenHeaders.get(0);
            }

            // 方式3: 从查询参数中获取
            String query = request.getURI().getQuery();
            if (query != null && query.contains("token=")) {
                String[] params = query.split("&");
                for (String param : params) {
                    if (param.startsWith("token=")) {
                        return param.substring(6); // "token=".length() = 6
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("提取Token失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证Token并返回用户信息
     * 使用现有的TokenService进行验证
     */
    private LoginUser validateToken(String token) {
        try {
            // 使用TokenService获取用户信息
            LoginUser loginUser = tokenService.getLoginUserByToken(token);

            if (loginUser != null) {
                // 检查Token是否过期
                long currentTime = System.currentTimeMillis();
                if (loginUser.getExpireTime() > currentTime) {
                    return loginUser;
                } else {
                    log.warn("Token已过期: {}", token);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("验证Token失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 设置Spring Security认证上下文
     * 这样在WebSocketHandler中可以通过SecurityContextHolder获取当前用户
     */
    private void setAuthenticationContext(LoginUser loginUser) {
        try {
            UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
        } catch (Exception e) {
            log.warn("设置Spring Security认证上下文失败: {}", e.getMessage());
            // 这不是致命错误，可以继续
        }
    }
}