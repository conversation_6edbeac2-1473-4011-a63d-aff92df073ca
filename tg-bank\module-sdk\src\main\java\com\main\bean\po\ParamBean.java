package com.main.bean.po;

import com.main.auto.Column;
import com.main.auto.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * PARAM BEAN
 *
 * <AUTHOR>
 */

@Data
@Table(name = "MCM_PARAMS")
public class ParamBean implements Serializable {

	private static final long serialVersionUID = 5132311058497963363L;

	@Column
	private String paramsno;

	@Column(flag = "primary")
	private String paramskey;

	@Column(type = "VARCHAR2(4000)")
	private String paramsvalue;

	@Column
	private int paramstype;

	@Column
	private String reverse1;

	@Column
	private String reverse2;

}
