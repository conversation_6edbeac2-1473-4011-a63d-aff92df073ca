package com.main.service.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.main.mapper.admin.UserLoginRecordMapper;
import com.main.pojo.admin.UserLoginRecord;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.util.DateUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户登录记录信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Service
public class UserLoginRecordServiceImpl extends ServiceImpl<UserLoginRecordMapper, UserLoginRecord> implements UserLoginRecordService {

    @Override
    public List<UserLoginRecord> getUserLoginRecords(String userOid) {
        LambdaQueryWrapper<UserLoginRecord> query = new LambdaQueryWrapper<>();
        query.eq(UserLoginRecord::getUserOid, userOid);
        query.between(UserLoginRecord::getLoginTime,DateUtil.subtractWithoutSplit(60)+"000000",DateUtil.getMyDateWithoutSplit()+"235959");
        query.orderByDesc(UserLoginRecord::getLoginTime);
        query.last("LIMIT 7");
        return this.list(query);
    }
}
