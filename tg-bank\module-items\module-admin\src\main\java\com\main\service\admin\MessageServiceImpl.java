package com.main.service.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.handler.Api;
import com.main.mapper.admin.MessageMapper;
import com.main.mapper.admin.MessageReceiverMapper;
import com.main.pojo.admin.Message;
import com.main.pojo.admin.MessageReceiver;
import com.main.pojo.admin.Org;
import com.main.pojo.admin.User;
import com.main.service.util.AsyncProcesser4Ws;
import com.main.util.CUtil;
import com.main.util.DateUtil;
import com.main.util.Global;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Slf4j
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    @Resource
    private MessageReceiverMapper messageReceiverMapper;
    @Resource
    private MessageMapper messageMapper;
    @Autowired
    private AsyncProcesser4Ws asyncProcesser4Ws;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<String> sendMessage(Message message, User user) {
        // 权限校验
        if (user.getIsAdmin() != 1) {
            // 非超级管理员只能发本机构消息
            if ("ALL".equals(message.getReceiveScope())) {
                throw new BusinessException("非管理员，没有发送全部机构消息的权限");
            }
            if (("ORG".equals(message.getReceiveScope()) || "CURRORG".equals(message.getReceiveScope())) && user.getIsAdmin() != 0) {
                throw new BusinessException("越权访问，没有发送机构通知的权限");
            }
        }

        if("CHAT".equals(message.getType())) {
            message.setReceiveScope("USER");
            // 同步删除自己发送的图片，只保留3天图片内容防止内容过多
            this.removeChatPic(user.getOid(), message.getReceiveTarget());
        }

        // 设置消息基本信息
        message.setOid(Global.createUUID());
        message.setSenderId(user.getOid());
        message.setSenderName(user.getName());
        message.setSenderChatPhone(user.getPhone());
        message.setSenderChatSex(user.getSex());
        message.setOrgId(user.getCurrentLoginOrg().getOid());
        if ("ORG".equals(message.getReceiveScope())) {
            message.setReceiveTarget(user.getOrgList().stream().map(Org::getOid).collect(Collectors.joining(",")));
        } else if ("CURRORG".equals(message.getReceiveScope())) {
            message.setReceiveTarget(user.getCurrentLoginOrg().getOid());
        } else if("ALL".equals(message.getReceiveScope())) {
            message.setReceiveTarget("ALL");
        } else {
            if(CUtil.isEmpty(message.getReceiveTarget())) {
                throw new BusinessException("当前消息类型接收对象不允许为空！");
            }
        }
        message.setCreateTime(new Date());
        message.setDelFlag(0);
        save(message);
        asyncProcesser4Ws.sendMessage(message);
        ResponseVO<String> response = new ResponseVO<>();
        response.setData(message.getOid());
        return response;
    }

    @Override
    public ResponseVO<Map<String, Object>> pageMessages(int pageNum, int pageSize, String type,
                                                        String keyword, Integer readStatus, String userOid, User user) {
        Page<Message> page = new Page<>(pageNum, pageSize);
        IPage<Message> messagePage = messageMapper.pageMessages(page, type, keyword, readStatus,userOid, user);
        return Api.packageTable(messagePage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<String> markAsRead(List<String> messageIds, User user) {
        // 创建已读记录
        for (String messageId : messageIds) {
            MessageReceiver receiver = new MessageReceiver();
            receiver.setOid(Global.createUUID());
            receiver.setMessageId(messageId);
            receiver.setReceiverId(user.getOid());
            receiver.setReadTime(new Date());
            receiver.setCreateTime(new Date());
            messageReceiverMapper.insert(receiver);
        }

        ResponseVO<String> response = new ResponseVO<>();
        response.setData("标记成功");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<String> markAllAsRead(String type, String userOid, User user) {
        // 聊天消息标记已读
        if("CHAT".equals(type)) {
            LambdaUpdateWrapper<Message> update = new LambdaUpdateWrapper<>();
            update.set(Message::getChatReadStatus, 1);
            update.eq(Message::getSenderId, userOid);
            update.eq(Message::getReceiveTarget, user.getOid());
            update.eq(Message::getChatReadStatus, 0);
            this.update(update);
        }else {
            // 获取已读消息ID列表
            List<String> readMessageIds = messageReceiverMapper.selectList(
                    new LambdaQueryWrapper<MessageReceiver>()
                            .eq(MessageReceiver::getReceiverId, user.getOid())
            ).stream().map(MessageReceiver::getMessageId).collect(Collectors.toList());

            // 查询当前用户可见的所有未读消息
            LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
            wrapper.and(w -> w
                    .eq(Message::getReceiveScope, "ALL")
                    .or(o -> o.eq(Message::getReceiveScope, "ORG")
                            .like(Message::getReceiveTarget, user.getCurrentLoginOrg().getOid()))
                    .or(o -> o.eq(Message::getReceiveScope, "USER")
                            .like(Message::getReceiveTarget, user.getOid()))
            );

            if (!readMessageIds.isEmpty()) {
                wrapper.notIn(Message::getOid, readMessageIds);
            }

            wrapper.eq(Message::getDelFlag, 0);

            // 为所有未读消息创建已读记录
            List<Message> unreadMessages = list(wrapper);
            Date now = new Date();

            for (Message message : unreadMessages) {
                MessageReceiver receiver = new MessageReceiver();
                receiver.setOid(Global.createUUID());
                receiver.setMessageId(message.getOid());
                receiver.setReceiverId(user.getOid());
                receiver.setReadTime(now);
                receiver.setCreateTime(now);
                messageReceiverMapper.insert(receiver);
            }
        }
        ResponseVO<String> response = new ResponseVO<>();
        response.setData("标记成功");
        return response;
    }

    @Override
    public ResponseVO<Map<String, Integer>> getUnreadCount(User user) {

        Map<String, Map<String, BigDecimal>> result = messageMapper.getUnreadCount(user);
        Map<String, Integer> finalResult = new HashMap<>();
        BigDecimal unreadCount = CUtil.getMV(CUtil.getMV(result, "SYSTEM"),"nums");
        int unreadCountInt = 0;
        if(unreadCount != null) {
            unreadCountInt = unreadCount.intValue();
        }

        BigDecimal unreadChatCount = CUtil.getMV(CUtil.getMV(result, "CHAT"),"chatNums");
        int unreadChatCountInt = 0;
        if(unreadChatCount != null) {
            unreadChatCountInt = unreadChatCount.intValue();
        }
        finalResult.put("notificationUnread", unreadCountInt);
        finalResult.put("chatUnread", unreadChatCountInt);
        ResponseVO<Map<String, Integer>> response = new ResponseVO<>();
        response.setData(finalResult);
        return response;
    }

    @Override
    public ResponseVO<Map<String, Integer>> deleteChatMessage(User user, String userOid) {
        LambdaUpdateWrapper<Message> update = new LambdaUpdateWrapper<>();
        update.set(Message:: getDelFlag, 1);
        update.eq(Message::getType, "CHAT");
        if(CUtil.isEmpty(userOid)) {
            update.and(w -> w.eq(Message::getSenderId, user.getOid()).or().eq(Message::getReceiveTarget, user.getOid()));
        } else {
            update.and(w ->
                    w.or(a -> a.eq(Message::getSenderId, user.getOid()).eq(Message::getReceiveTarget, userOid))
                    .or(a -> a.eq(Message::getSenderId, userOid).eq(Message::getReceiveTarget, user.getOid())));
        }
        this.remove(update);
        return new ResponseVO<>();
    }

    @Override
    public void removeChatPic(String oid, String target) {
        LambdaUpdateWrapper<Message> update = new LambdaUpdateWrapper<>();
        update.set(Message::getContent, "[图片已过期]");
        update.eq(Message::getType, "CHAT");
        update.eq(Message::getSenderId, oid);
        if(CUtil.isNotEmpty(target)) {
            update.eq(Message::getReceiveTarget, target);
        }
        update.le(Message::getCreateTime, DateUtil.subtract(3)+" 00:00:00");
        update.ne(Message::getContent,"[图片已过期]");
        this.update(update);
    }
}