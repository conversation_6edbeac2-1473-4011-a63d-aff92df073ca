package com.main.mapper.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.pojo.admin.Role;
import com.main.pojo.admin.Server;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 云资源的服务器信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
public interface ServerMapper extends BaseMapper<Server> {

    IPage<Server> getPageOfRecords(Page<Role> page, @Param("cloudOid") String cloudOid, @Param("params") JSONObject params);
}
