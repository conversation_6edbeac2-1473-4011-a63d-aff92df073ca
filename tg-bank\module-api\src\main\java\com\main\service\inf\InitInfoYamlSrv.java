package com.main.service.inf;

/**
 * 用于处理初始化参数服务，对应config/systemInfoParam.yml
 *
 * <AUTHOR>
 * 2021/11/15 14:28
 */
public interface InitInfoYamlSrv {
	boolean init();

	/**
	 * 是否可以初始化
	 *
	 * @return 成功or失败
	 */
	boolean canInit() ;

	/**
	 * 设置系统是否可以初始化
	 *
	 * @param bool true可以，false不可以，且链接/init失效
	 * @return 成功or失败
	 */
	boolean setCanInit(boolean bool);

	/**
	 * 校验初始化密码
	 *
	 * @param pwd 密码
	 * @return 成功or失败
	 */
	boolean validInitPwd(String pwd);

	/**
	 * 往文件写入参数信息
	 *
	 * @param key 键
	 * @param value 值
	 * @return 是否成功
	 */
	boolean setParam(String key, String value);

	/**
	 * 读取文件配置信息
	 *
	 * @param key 键
	 * @return 值
	 */
	String getParam(String key);
}
