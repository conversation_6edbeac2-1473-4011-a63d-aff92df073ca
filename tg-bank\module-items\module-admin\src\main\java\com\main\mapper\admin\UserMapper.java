package com.main.mapper.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.main.pojo.admin.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserMapper extends BaseMapper<User> {

    IPage<User> getPageOfRecords(Page<User> page, @Param("params")JSONObject params, @Param("userOid") String userOid);

    List<User> getOrgManagers(@Param("orgOid") String orgOid);

    List<User> getOrgOnlineUser(@Param("orgs") String orgs);

    IPage<User> getPageOfChatRecords(Page<User> page, @Param("params")JSONObject params, @Param("userOid") String userOid);
}
