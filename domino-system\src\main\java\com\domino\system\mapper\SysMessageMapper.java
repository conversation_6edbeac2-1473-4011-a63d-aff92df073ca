package com.domino.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.qh.domain.SysMessage;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Mapper
public interface SysMessageMapper extends BaseMapper<SysMessage> {
    
    /**
     * 分页查询消息列表
     *
     * @param page 分页对象
     * @param type 消息类型
     * @param keyword 搜索关键字
     * @param readStatus 阅读状态
     * @param userOid 用户OID（用于聊天消息查询）
     * @param user 当前登录用户
     * @return 消息列表
     */
    IPage<SysMessage> pageMessages(Page<SysMessage> page, 
                                   @Param("type") String type, 
                                   @Param("keyword") String keyword, 
                                   @Param("readStatus") Integer readStatus,
                                   @Param("userOid") String userOid, 
                                   @Param("user") SysUser user);

    /**
     * 获取未读消息数量统计
     *
     * @param user 当前登录用户
     * @return 未读消息统计
     */
    @MapKey("type")
    Map<String, Map<String, BigDecimal>> getUnreadCount(@Param("user") SysUser user);

    // ==================== 基础增删改查方法 ====================

    /**
     * 插入消息
     *
     * @param message 消息对象
     * @return 影响行数
     */
    int insertMessage(SysMessage message);

    /**
     * 批量插入消息
     *
     * @param messageList 消息列表
     * @return 影响行数
     */
    int batchInsertMessage(@Param("list") List<SysMessage> messageList);

    /**
     * 根据ID查询消息
     *
     * @param messageId 消息ID
     * @return 消息对象
     */
    SysMessage selectMessageById(String messageId);

    /**
     * 查询消息列表
     *
     * @param message 消息查询条件
     * @return 消息列表
     */
    List<SysMessage> selectMessageList(SysMessage message);

    /**
     * 更新消息
     *
     * @param message 消息对象
     * @return 影响行数
     */
    int updateMessage(SysMessage message);

    /**
     * 批量更新消息已读状态
     *
     * @param messageIds 消息ID数组
     * @param readStatus 已读状态
     * @return 影响行数
     */
    int batchUpdateReadStatus(@Param("messageIds") String[] messageIds, @Param("readStatus") Integer readStatus);

    /**
     * 更新聊天消息已读状态
     *
     * @param senderId 发送者ID
     * @param receiveTarget 接收者ID
     * @return 影响行数
     */
    int updateChatReadStatus(@Param("senderId") String senderId, @Param("receiveTarget") String receiveTarget);

    /**
     * 逻辑删除消息
     *
     * @param messageId 消息ID
     * @return 影响行数
     */
    int deleteMessageById(String messageId);

    /**
     * 批量逻辑删除消息
     *
     * @param messageIds 消息ID数组
     * @return 影响行数
     */
    int deleteMessageByIds(String[] messageIds);

    /**
     * 物理删除消息
     *
     * @param messageId 消息ID
     * @return 影响行数
     */
    int removeMessageById(String messageId);

    /**
     * 批量物理删除消息
     *
     * @param messageIds 消息ID数组
     * @return 影响行数
     */
    int removeMessageByIds(String[] messageIds);

    /**
     * 删除聊天记录
     *
     * @param senderId 发送者ID
     * @param receiveTarget 接收者ID
     * @return 影响行数
     */
    int deleteChatMessages(@Param("senderId") String senderId, @Param("receiveTarget") String receiveTarget);

    // ==================== 扩展查询方法 ====================

    /**
     * 根据类型查询消息数量
     *
     * @param type 消息类型
     * @return 消息数量
     */
    int countMessagesByType(String type);

    /**
     * 根据发送者查询消息列表
     *
     * @param senderId 发送者ID
     * @return 消息列表
     */
    List<SysMessage> selectMessagesBySender(Long senderId);

    /**
     * 根据接收者查询消息列表
     *
     * @param receiveTarget 接收者ID
     * @return 消息列表
     */
    List<SysMessage> selectMessagesByReceiver(String receiveTarget);

    /**
     * 查询聊天消息列表
     *
     * @param senderId 发送者ID
     * @param receiveTarget 接收者ID
     * @return 聊天消息列表
     */
    List<SysMessage> selectChatMessages(@Param("senderId") String senderId, @Param("receiveTarget") String receiveTarget);

    /**
     * 查询未读聊天消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int countUnreadChatMessages(@Param("userId") String userId);

    /**
     * 查询最近的聊天消息
     *
     * @param senderId 发送者ID
     * @param receiveTarget 接收者ID
     * @return 最近的聊天消息
     */
    SysMessage selectLatestChatMessage(@Param("senderId") String senderId, @Param("receiveTarget") String receiveTarget);

    /**
     * 清理过期的聊天图片
     *
     * @param days 过期天数
     * @return 影响行数
     */
    int cleanExpiredChatImages(@Param("days") int days);

    /**
     * 清理过期的聊天图片内容
     *
     * @param senderId 发送者ID
     * @param receiveTarget 接收者ID
     * @param expireTime 过期时间
     * @return 影响行数
     */
    int cleanExpiredChatPicContent(@Param("senderId") String senderId,
                                   @Param("receiveTarget") String receiveTarget,
                                   @Param("expireTime") Date expireTime);

    /**
     * 根据引用ID查询消息
     *
     * @param quoteOid 引用消息ID
     * @return 消息列表
     */
    List<SysMessage> selectMessagesByQuoteId(String quoteOid);
}
