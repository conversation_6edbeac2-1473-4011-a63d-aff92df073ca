package com.config.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.constant.DPConstant;
import com.main.pojo.admin.User;
import com.main.service.util.RedisSrv;
import com.main.util.CUtil;
import com.main.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

//@ServerEndpoint("/dvp-socket/{token}")
//@Component
@Deprecated
@Slf4j
public class WebSocketServer {

    private static final long sessionTimeout = 600000;

    /**
     * 当前在线连接数
     */
    private static AtomicInteger onlineCount = new AtomicInteger(0);
    //虽然@Component默认是单例模式的，但springboot还是会为每个websocket连接初始化一个bean，所以可以用一个静态set保存起来。
    //注：底下WebSocket是当前类名
    private static CopyOnWriteArraySet<WebSocketServer> webSockets = new CopyOnWriteArraySet<>();

    /**
     * 用来存放每个客户端对应的 WebSocketServer 对象
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收 id
     */
    private String id;

    @Resource
    private RedisSrv redisSrv;
    @Resource
    private StringRedisTemplate template;

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        session.setMaxIdleTimeout(sessionTimeout);
        this.session = session;
        try {
            String userJson = template.opsForValue().get(DPConstant.RK_DP_TOKEN + token);

            if (CUtil.isNotEmpty(userJson)) {
                User user = JSONObject.parseObject(userJson, User.class);
                this.id = user.getOid();
                webSockets.add(this);
                if (webSocketMap.containsKey(id)) {
                    webSocketMap.remove(id);
                    webSocketMap.put(id, this);
                } else {
                    webSocketMap.put(id, this);
                    addOnlineCount();
                }
                log.info("编号id:" + id + "连接,当前在线数为:" + getOnlineCount());

                sendMessage("连接成功！");

            } else {
                sendMessage("用户未登录！");
            }
        } catch (IOException e) {
            log.error("鉴权标识:" + token + ",网络异常!!!!!!");
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (webSocketMap != null && webSocketMap.keySet().size() > 0 && webSocketMap.containsKey(id)) {
            webSockets.remove(this);
            webSocketMap.remove(id);
            subOnlineCount();
        }
        log.info("编号id:" + id + "退出,当前在线数为:" + getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {

        log.info("编号id消息:" + id + ",报文:" + message);
    }

    /**
     * 发生错误时调用
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("编号id错误:" + this.id + ",原因:" + error.getMessage());
        error.printStackTrace();
    }


    /**
     * @description: 分布式  使用redis 去发布消息
     */
    public void sendMessage(@NotNull String key, String message) {
        String newMessge = null;
        try {
            newMessge = new String(message.getBytes(Constants.UTF8), Constants.UTF8);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        Map<String, String> map = new HashMap<>();
        map.put(Constants.REDIS_MESSAGE_KEY, key);
        map.put(Constants.REDIS_MESSAGE_VALUE, newMessge);
        template.convertAndSend(Constants.REDIS_CHANNEL, JSON.toJSONString(map));
    }

    /**
     * @description: 单机使用  外部接口通过指定的客户id向该客户推送消息。
     * @dateTime: 2021/6/16 17:49
     */
    public void sendMessageByWayBillId(@NotNull Long key, String message) {
        WebSocketServer webSocketServer = webSocketMap.get(key);
        if (!CUtil.isEmpty(webSocketServer)) {
            try {
                webSocketServer.sendMessage(message);
                log.info("编号id为：" + key + "发送消息：" + message);
            } catch (IOException e) {
                e.printStackTrace();
                log.error("编号id为：" + key + "发送消息失败");
            }
        }
        log.error("编号id号为：" + key + "未连接");
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    public static synchronized AtomicInteger getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount.getAndIncrement();
    }

    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount.getAndDecrement();
    }
}