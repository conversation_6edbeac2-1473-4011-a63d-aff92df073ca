package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.OrgMapper;
import com.main.pojo.admin.Dept;
import com.main.pojo.admin.Org;
import com.main.pojo.admin.User;
import com.main.pojo.admin.UserOrg;
import com.main.util.BeanUtils;
import com.main.util.CUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
public class OrgServiceImpl extends ServiceImpl<OrgMapper, Org> implements OrgService {

    @Resource
    private UserOrgService userOrgService;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private DeptService deptService;

    @Override
    public ResponseVO<Map<String, Object>> getOrgTreeData(String oid, User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Org> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(Org::getIsStop, 0);
        if (user.getIsAdmin() == 0) {
            queryWrapper.and(w -> w.inSql(Org::getOid, "select org_oid from d_user_org where user_oid = '" + user.getOid() + "'").or().eq(Org::getIsLeaf, 0));
        } else if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看机构信息！请联系管理员");
        }
        List<Org> orgList = this.list(queryWrapper);

        Map<String, List<Org>> parentChildrenMap = new HashMap<>();
        orgList.forEach(org -> {
            String parentId = org.getParentId();
            if (CUtil.isEmpty(parentId)) {
                parentId = "0";
            }
            List<Org> children = parentChildrenMap.get(parentId);
            if (children == null) children = new ArrayList<>();
            children.add(org);
            parentChildrenMap.put(parentId, children);
        });
        List<Org> topOrgList = parentChildrenMap.get("0");
        List<Map<String, Object>> treeData = new ArrayList<>();
        if (topOrgList != null && topOrgList.size() > 0) {
            handlerChildren(parentChildrenMap, treeData, topOrgList);
        }

        //获取当前用户已经选择的
        JSONArray dVal = new JSONArray();
        LambdaQueryWrapper<UserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userOrgLambdaQueryWrapper.eq(UserOrg::getUserOid, oid);
        List<UserOrg> userOrgList = userOrgService.list(userOrgLambdaQueryWrapper);
        if (userOrgList != null) {
            userOrgList.forEach(userOrg -> dVal.add(userOrg.getOrgOid()));
        }
        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        retDataMap.put("dVal", dVal);

        response.setData(retDataMap);
        return response;
    }

    @Override
    public ResponseVO<Map<String, Object>> getOrgDeptTreeData(String oid, User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Org> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Org::getIsStop, 0);
        queryWrapper.eq(Org::getIsLeaf, 1);
        //不是超级管理员就限制一下他能看他当前有的
        if (user.getIsAdmin() == 0) {
            queryWrapper.inSql(Org::getOid, "select org_oid from d_user_org where user_oid = '" + user.getOid() + "'");
        } else if (user.getIsAdmin() == -1) {
            throw new BusinessException("当前用户没有权限查看机构信息！请联系管理员");
        }
        List<Org> orgList = this.list(queryWrapper);

        LambdaQueryWrapper<Dept> queryDeptWrapper = new LambdaQueryWrapper<>();
        queryDeptWrapper.eq(Dept::getIsStop, 0);
        queryDeptWrapper.eq(Dept::getIsLeaf, 1);
//        if (user.getIsAdmin() != 1) {
//            queryDeptWrapper.inSql(Dept::getOid, "select dept_oid from d_user_org where user_oid = '" + user.getOid() + "'");
//        }
        List<Dept> deptList = deptService.list(queryDeptWrapper);
        Map<String, List<Map<String, Object>>> orgDeptMap = new HashMap<>();
        deptList.forEach(dept -> {
            String orgOid = dept.getOrgOid();
            List<Map<String, Object>> list = orgDeptMap.get(orgOid);
            if (list == null) {
                list = new ArrayList<>();
            }
            Map<String, Object> treeNode = new HashMap<>();
            treeNode = BeanUtils.autoBeanToMap(dept);
            ;
            treeNode.put("label", dept.getDeptName());
            treeNode.put("value", dept.getOrgOid() + "-" + dept.getOid());
            list.add(treeNode);
            orgDeptMap.put(orgOid, list);
        });

        List<Map<String, Object>> treeData = new ArrayList<>();
        for (Org org : orgList) {
            Map<String, Object> treeNode = new HashMap<>();
            treeNode = BeanUtils.autoBeanToMap(org);
            treeNode.put("label", org.getOrgName());
            treeNode.put("value", org.getOid());
            List<Map<String, Object>> childrenTreeData = orgDeptMap.get(org.getOid());
            treeNode.put("children", childrenTreeData);
            treeData.add(treeNode);
        }

        //获取当前用户已经选择的
        JSONArray dVal = new JSONArray();
        LambdaQueryWrapper<UserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userOrgLambdaQueryWrapper.eq(UserOrg::getUserOid, oid);
        List<UserOrg> userOrgList = userOrgService.list(userOrgLambdaQueryWrapper);
        if (userOrgList != null) {
            userOrgList.forEach(userOrg -> dVal.add(userOrg.getOrgOid() + "-" + userOrg.getDeptOid()));
        }
        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        retDataMap.put("dVal", dVal);

        response.setData(retDataMap);
        return response;
    }

    @Override
    public ResponseVO<String> saveOrg(JSONObject org) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Org orgInfo = org.toJavaObject(Org.class);
        if (CUtil.isEmpty(orgInfo.getOid())) {
            //ADD
            String oid = this.getOrgId(orgInfo.getParentId());
            orgInfo.setOid(oid);
            orgInfo.setIsLeaf(1);
            this.save(orgInfo);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(orgInfo.getParentId())) {
                this.updateLeaf(orgInfo.getParentId(), 0);
            }
        } else {
            //UPDATE
            this.updateById(orgInfo);
        }

        return responseVO;
    }

    @Override
    public ResponseVO<String> saveOrg(Org orgInfo) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        if (CUtil.isEmpty(orgInfo.getOid())) {
            //ADD
            String oid = this.getOrgId(orgInfo.getParentId());
            orgInfo.setOid(oid);
            orgInfo.setIsLeaf(1);
            this.save(orgInfo);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(orgInfo.getParentId())) {
                this.updateLeaf(orgInfo.getParentId(), 0);
            }
        } else {
            //UPDATE
            this.updateById(orgInfo);
        }

        return responseVO;
    }

    @Override
    public boolean updateLeaf(String oid, int isLeaf) {
        LambdaUpdateWrapper<Org> update = new LambdaUpdateWrapper<>();
        update.set(Org::getIsLeaf, isLeaf);
        update.eq(Org::getOid, oid);
        return this.update(update);
    }

    @Override
    public String getOrgId(String parentId) {
        String oid = orgMapper.getAutoGeneralID(parentId);
        return CUtil.handlerId(oid, parentId);
    }

    @Override
    public ResponseVO<String> deleteOrg(String oid, String parentId) {
        ResponseVO<String> response = new ResponseVO<>();
        //删除他和他的子节点
        LambdaQueryWrapper<Org> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(Org::getOid, oid);
        this.remove(queryWrapper);
        //判断他的老父亲是不是只有他这个儿子，如果是，就给他老父亲叶子标记为1
        if (CUtil.isNotEmpty(parentId)) {
            LambdaQueryWrapper<Org> queryParentWrapper = new LambdaQueryWrapper<>();
            queryParentWrapper.eq(Org::getParentId, CUtil.nullToStr(parentId));
            List<Org> orgs = this.list(queryParentWrapper);
            if (orgs == null || orgs.size() == 0) {
                this.updateLeaf(parentId, 1);
            }
        }
        return response;
    }

    private List<Map<String, Object>> getOrgTreeChildrenData(Map<String, List<Org>> parentChildrenMap, String oid) {
        List<Map<String, Object>> childrenTreeNode = new ArrayList<>();
        List<Org> children = parentChildrenMap.get(oid);
        handlerChildren(parentChildrenMap, childrenTreeNode, children);
        return childrenTreeNode;
    }

    private void handlerChildren(Map<String, List<Org>> parentChildrenMap, List<Map<String, Object>> childrenTreeNode, List<Org> children) {
        if (children != null && children.size() > 0) {
            children.forEach(org -> {
                Map<String, Object> treeNode = BeanUtils.autoBeanToMap(org);
                treeNode.put("label", org.getOrgName());
                treeNode.put("value", org.getOid());
                treeNode.put("disabled", org.getIsStop() != 0);

                if (org.getIsLeaf() == 0) {
                    List<Map<String, Object>> childrenTreeData = getOrgTreeChildrenData(parentChildrenMap, org.getOid());
                    treeNode.put("children", childrenTreeData);
                } else {
                    treeNode.put("children", null);
                }
                childrenTreeNode.add(treeNode);
            });
        }
    }
}
