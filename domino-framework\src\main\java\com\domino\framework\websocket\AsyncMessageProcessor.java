package com.domino.framework.websocket;

import com.domino.common.constant.ChatConstants;
import com.domino.common.core.domain.entity.SysUser;
import com.domino.common.core.domain.model.LoginUser;
import com.domino.common.qh.domain.SysMessage;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.StringUtils;
import com.domino.framework.web.service.TokenService;
import com.domino.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 异步消息处理器
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Component
public class AsyncMessageProcessor {

    @Autowired
    private QhWebSocketHandler webSocketHandler;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    /**
     * websocket 连接成功或失败的时候，同步用户的在线离线状态
     * @param state 状态 1 在线 0 离线 使
     * @param token token
     */
    @Async
    public void syncUserOnlineState(ISysUserService userService, String state, String token) {
        long a = System.currentTimeMillis();
        log.info("开始异步同步用户状态业务");
        //User user = redisSrv.getUserByToken(token);
        LoginUser user = SecurityUtils.getLoginUser();
        if(user != null) {
//            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(User::getOnline, state);
//            updateWrapper.eq(User::getOid, user.getOid());
//            userService.update(updateWrapper);
            //如果离线了，肯定要把他的token清除了
//			if(state.equals(DPConstant.USER_OFFLINE)) {
//				userService.logout(token);
//			}
        }
        long b = System.currentTimeMillis();
        log.info("结束异步同步用户状态业务，耗时:{} ms",b-a);
    }

    /**
     * 推送聊天消息
     * @param message 消息对象
     */
    @Async
    public void pushChatMessage(SysMessage message) {
        try {
            log.info("开始推送聊天消息，消息ID: {}", message.getMessageId());

            // 聊天消息发送给指定用户
            if (ChatConstants.RECEIVE_SCOPE_USER.equals(message.getReceiveScope())) {
                String targetUserId = message.getReceiveTarget();

                // 根据用户ID查找对应的token
                String token = tokenService.getTokenByUserId(targetUserId);
                if (StringUtils.isNotEmpty(token)) {
                    webSocketHandler.sendMessage(token, message);
                    log.info("聊天消息推送成功，目标用户: {}, token: {}", targetUserId, token);
                } else {
                    log.warn("用户 {} 未在线或token不存在，无法推送聊天消息", targetUserId);
                }
            }
        } catch (Exception e) {
            log.error("推送聊天消息失败，消息ID: {}, 错误: {}", message.getMessageId(), e.getMessage(), e);
        }
    }

    /**
     * 推送系统消息
     * @param message 消息对象
     */
    @Async
    public void pushSystemMessage(SysMessage message) {
        try {
            log.info("开始推送系统消息，消息ID: {}, 接收范围: {}", message.getMessageId(), message.getReceiveScope());

            switch (message.getReceiveScope()) {
                case ChatConstants.RECEIVE_SCOPE_ALL:
                    // 全部用户
                    webSocketHandler.sendMessage("ALL", message);
                    log.info("系统消息推送成功，范围: 全部用户");
                    break;

                case ChatConstants.RECEIVE_SCOPE_ORG:
                case ChatConstants.RECEIVE_SCOPE_CURRORG:
                    // 机构用户 - 根据机构ID获取用户列表进行推送
                    String orgIds = message.getReceiveTarget();
                    if (StringUtils.isNotEmpty(orgIds)) {
                        List<SysUser> orgUsers = userService.getOrgOnlineUser(orgIds);
                        log.info("找到机构 {} 的在线用户 {} 个", orgIds, orgUsers.size());

                        for (SysUser user : orgUsers) {
                            String userToken = tokenService.getTokenByUserId(user.getUserId().toString());
                            if (StringUtils.isNotEmpty(userToken)) {
                                webSocketHandler.sendMessage(userToken, message);
                                log.debug("系统消息推送给机构用户: {}, token: {}", user.getUserId(), userToken);
                            }
                        }
                        log.info("系统消息推送成功，范围: 机构用户，推送用户数: {}", orgUsers.size());
                    } else {
                        log.warn("机构ID为空，无法推送机构消息");
                    }
                    break;

                case ChatConstants.RECEIVE_SCOPE_USER:
                    // 指定用户
                    String targetUserId = message.getReceiveTarget();

                    // 根据用户ID查找对应的token
                    String token = tokenService.getTokenByUserId(targetUserId);
                    if (StringUtils.isNotEmpty(token)) {
                        webSocketHandler.sendMessage(token, message);
                        log.info("系统消息推送成功，目标用户: {}, token: {}", targetUserId, token);
                    } else {
                        log.warn("用户 {} 未在线或token不存在，无法推送系统消息", targetUserId);
                    }
                    break;

                default:
                    log.warn("未知的消息接收范围: {}", message.getReceiveScope());
                    break;
            }
        } catch (Exception e) {
            log.error("推送系统消息失败，消息ID: {}, 错误: {}", message.getMessageId(), e.getMessage(), e);
        }
    }
}
