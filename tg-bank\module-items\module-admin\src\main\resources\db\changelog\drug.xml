<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="2024-01-drug-1" author="cursor">
        <createTable tableName="his_drug_basic_info" remarks="药品基本信息表">
            <column name="company_id" type="varchar(32)" remarks="诊所ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warehouse_id" type="varchar(32)" remarks="库房ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="oid" type="varchar(32)" remarks="药品ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(100)" remarks="药品名称">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="varchar(50)" remarks="药品编码">
                <constraints nullable="false"/>
            </column>
            <column name="common_name" type="varchar(100)" remarks="通用名"/>
            <column name="trade_name" type="varchar(100)" remarks="商品名"/>
            <column name="zjm" type="varchar(50)" remarks="助记码"/>
            <column name="type" type="varchar(50)" remarks="药品类型"/>
            <column name="category" type="varchar(50)" remarks="药品分类"/>
            <column name="pharmacology_category" type="varchar(50)" remarks="药理分类"/>
            <column name="specification" type="varchar(100)" remarks="规格"/>
            <column name="unit" type="varchar(20)" remarks="单位"/>
            <column name="manufacturer" type="varchar(200)" remarks="生产厂家"/>
            <column name="approval_number" type="varchar(100)" remarks="批准文号"/>
            <column name="barcode" type="varchar(100)" remarks="条形码"/>
            <column name="product_code" type="varchar(50)" remarks="产品编码"/>
            <column name="dosage_form" type="varchar(50)" remarks="剂型"/>
            <column name="dosage" type="decimal(10,2)" remarks="剂量"/>
            <column name="dosage_unit" type="varchar(20)" remarks="剂量单位"/>
            <column name="min_package_quantity" type="int" remarks="最小包装数量"/>
            <column name="min_package_unit" type="varchar(20)" remarks="最小包装单位"/>
            <column name="package_unit" type="varchar(20)" remarks="包装单位"/>
            <column name="insurance_type" type="varchar(50)" remarks="医保类型"/>
            <column name="insurance_no" type="varchar(50)" remarks="医保编号"/>
            <column name="insurance_code" type="varchar(50)" remarks="医保代码"/>
            <column name="settlement_priority" type="varchar(20)" remarks="结算优先级"/>
        </createTable>

        <createTable tableName="his_drug_extended_info" remarks="药品扩展信息表">
            <column name="company_id" type="varchar(32)" remarks="诊所ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warehouse_id" type="varchar(32)" remarks="库房ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="oid" type="varchar(32)" remarks="药品ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="prescription_type" type="varchar(50)" remarks="处方类型"/>
            <column name="special_drug" type="varchar(200)" remarks="特殊药品标识"/>
            <column name="trace_code" type="varchar(100)" remarks="追溯码"/>
            <column name="license_holder" type="varchar(200)" remarks="上市许可持有人"/>
            <column name="is_basic_drug" type="varchar(10)" remarks="是否基药"/>
            <column name="drug_tags" type="varchar(500)" remarks="药品标签"/>
            <column name="storage_condition" type="varchar(200)" remarks="储存条件"/>
            <column name="maintenance_type" type="varchar(50)" remarks="养护类型"/>
            <column name="shelf_life" type="int" remarks="保质期"/>
            <column name="validity_period" type="int" remarks="有效期"/>
            <column name="validity_unit" type="varchar(20)" remarks="有效期单位"/>
            <column name="approval_valid_date" type="varchar(100)" remarks="批文有效期"/>
            <column name="location_no" type="varchar(50)" remarks="货位号"/>
            <column name="remark" type="varchar(500)" remarks="备注"/>
            <column name="antibacterial_type" type="varchar(50)" remarks="抗菌药物类型"/>
            <column name="antibacterial_level" type="varchar(20)" remarks="抗菌药物等级"/>
            <column name="antibacterial_categories" type="varchar(200)" remarks="抗菌药物类别"/>
            <column name="antibiotic_dosage" type="decimal(10,2)" remarks="抗生素用量"/>
            <column name="antibiotic_unit" type="varchar(20)" remarks="抗生素单位"/>
        </createTable>

        <createTable tableName="his_drug_pricing_info" remarks="药品定价信息表">
            <column name="company_id" type="varchar(32)" remarks="诊所ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warehouse_id" type="varchar(32)" remarks="库房ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="oid" type="varchar(32)" remarks="药品ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="purchase_price" type="decimal(10,2)" remarks="进货价"/>
            <column name="retail_price" type="decimal(10,2)" remarks="零售价"/>
            <column name="allow_split" type="boolean" remarks="允许拆零"/>
            <column name="split_retail_price" type="decimal(10,2)" remarks="拆零价"/>
            <column name="pricing_mode" type="varchar(50)" remarks="定价方式"/>
            <column name="purchase_tax_rate" type="decimal(5,2)" remarks="进项税率"/>
            <column name="sale_tax_rate" type="decimal(5,2)" remarks="销项税率"/>
        </createTable>

        <createTable tableName="his_drug_inventory_info" remarks="药品库存信息表">
            <column name="company_id" type="varchar(32)" remarks="诊所ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warehouse_id" type="varchar(32)" remarks="库房ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="oid" type="varchar(32)" remarks="药品ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="current_stock" type="decimal(10,2)" remarks="当前库存"/>
            <column name="available_stock" type="decimal(10,2)" remarks="可用库存"/>
            <column name="stock_value" type="decimal(10,2)" remarks="库存金额"/>
            <column name="daily_consumption" type="decimal(10,2)" remarks="日均消耗"/>
            <column name="turnover_days" type="int" remarks="周转天数"/>
            <column name="profit_rate" type="varchar(20)" remarks="毛利率"/>
            <column name="expiry_date" type="varchar(20)" remarks="有效期至"/>
        </createTable>

        <createIndex tableName="his_drug_basic_info" indexName="idx_drug_basic_code">
            <column name="code"/>
        </createIndex>
        <createIndex tableName="his_drug_basic_info" indexName="idx_drug_basic_name">
            <column name="name"/>
        </createIndex>
        <createIndex tableName="his_drug_basic_info" indexName="idx_drug_basic_zjm">
            <column name="zjm"/>
        </createIndex>
    </changeSet>
    <changeSet id="2025-01-drug-1" author="cfq">
        <addColumn tableName="his_drug_basic_info">
            <column name="is_valuable_equipment" type="boolean" defaultValue="false" remarks="是否贵重器械"/>
            <column name="origin_type" type="varchar(20)" defaultValue="" remarks="产地类型（进口/国产）"/>
        </addColumn>
        <addColumn tableName="his_drug_extended_info">
            <column name="equipment_class" type="varchar(40)" remarks="医疗器械分类"/>
        </addColumn>
        <addColumn tableName="his_drug_pricing_info">
            <column name="retail_percent" type="decimal(10,3)" remarks="定价加成比例"/>
        </addColumn>
    </changeSet>
    <changeSet id="2025-01-drug-2" author="cfq">
        <addColumn tableName="his_drug_basic_info">
            <column name="enable" type="int" defaultValue="1" remarks="状态 0 停用 1 "/>
        </addColumn>
    </changeSet>
</databaseChangeLog>