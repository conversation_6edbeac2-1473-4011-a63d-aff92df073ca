package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.handler.Api;
import com.main.pojo.admin.Service;
import com.main.pojo.admin.User;
import com.main.service.admin.ServiceService;
import org.junit.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务器部署的服务信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@RestController
@RequestMapping("/service")
public class ServiceHandler {
    @Resource
    private ServiceService service;
    @MyAuthority(auth="/server/cloud/index")
    @RequestMapping("/list")
    @ResponseBody
    public ResponseVO<Map<String, Object>> list(@RequestBody JSONObject params, @Login User user) {
        Assert.assertNotNull("未传递服务器OID！", params.getString("serverOid"));
        List<Service> serviceList = service.getList(params, user);
        return Api.packageList(serviceList);
    }
    @LogAnnotation(option = "保存服务器信息")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/save")
    public ResponseVO<String> save(@RequestBody JSONObject server, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行此操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return service.mySave(server, user.getOid());
    }

    @LogAnnotation(option = "删除服务器信息")
    @MyAuthority(auth="/server/cloud/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        Assert.assertEquals("只有超级管理员才能执行删除操作！", Long.parseLong(user.getIsAdmin().toString()), 1L);
        return service.myDel(params.getString("oid"));
    }
}
