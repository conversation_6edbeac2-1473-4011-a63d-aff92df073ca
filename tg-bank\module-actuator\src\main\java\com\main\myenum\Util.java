package com.main.myenum;

import java.util.concurrent.TimeUnit;

public class Util {
    public static String formatElapsedSecs(long secs) {
        long days = TimeUnit.SECONDS.toDays(secs);
        long eTime = secs - TimeUnit.DAYS.toSeconds(days);
        long hr = TimeUnit.SECONDS.toHours(eTime);
        eTime -= TimeUnit.HOURS.toSeconds(hr);
        long min = TimeUnit.SECONDS.toMinutes(eTime);
        eTime -= TimeUnit.MINUTES.toSeconds(min);
        return String.format("%d天%02d小时%02d分钟%02d秒", days, hr, min, eTime);
    }
}
