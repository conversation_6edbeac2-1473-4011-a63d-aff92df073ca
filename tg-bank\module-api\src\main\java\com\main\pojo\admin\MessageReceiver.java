package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 消息接收实体类
 * <AUTHOR>
 */
@Data
@TableName("d_message_receiver")
public class MessageReceiver {
    /**
     * 主键ID
     */
    @TableId
    private String oid;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 接收人ID
     */
    private String receiverId;
    
    /**
     * 阅读时间
     */
    private Date readTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 