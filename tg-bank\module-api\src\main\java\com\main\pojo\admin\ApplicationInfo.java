package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * application information record
 *
 * <AUTHOR>
 */
@Data
public class ApplicationInfo implements Serializable {

	private static final long serialVersionUID = -3659005483062971583L;

    @NotNull
    @TableId
	private String appId;

    @NotNull
	private String appName;

    @NotNull
	private String appSecret;

    private String termId;

	private String createTime;

	private String createUserId;

	private String updateTime;

	private String appType;

	//机构ID 关联PM_COMPANY表
	private String companyId;
	//应用状态：0启用 1禁用
	private String status;

	private String extra;

	private String notifyUrl;

}
