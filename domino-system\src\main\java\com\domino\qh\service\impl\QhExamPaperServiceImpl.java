package com.domino.qh.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.domino.common.constant.QhConstants;
import com.domino.common.core.domain.entity.SysDictData;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.domain.*;
import com.domino.common.qh.dto.*;
import com.domino.common.utils.DateUtils;
import com.domino.common.utils.DictUtils;
import com.domino.common.utils.SecurityUtils;
import com.domino.common.utils.spring.SpringUtils;
import com.domino.common.utils.uuid.IdUtils;
import com.domino.qh.mapper.QhExamPaperMapper;
import com.domino.qh.mapper.QhKnowledgeQuestionMapper;
import com.domino.qh.mapper.QhKnowledgeTreeMapper;
import com.domino.qh.mapper.QhQuestionBankMapper;
import com.domino.qh.service.IQhExamPaperService;
import com.domino.qh.service.IQhKnowledgeTreeService;
import com.domino.qh.service.IQhQuestionBankService;
import com.domino.qh.service.QuestionElasticSearchService;
import com.domino.system.mapper.SysUserMapper;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Multiset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class QhExamPaperServiceImpl implements IQhExamPaperService {

    @Resource
    private QhExamPaperMapper examPaperMapper;
    @Resource
    private QhQuestionBankMapper questionBankMapper;
    @Autowired
    private IQhKnowledgeTreeService iQhKnowledgeTreeService;
    @Resource
    private QhKnowledgeTreeMapper qhKnowledgeTreeMapper;
    @Resource
    private QhKnowledgeQuestionMapper knowledgeQuestionMapper;
    @Autowired
    private IQhQuestionBankService questionBankService;
    @Autowired
    private QuestionElasticSearchService questionElasticSearchService;

    private final ThreadLocal<Random> random = ThreadLocal.withInitial(Random::new);
    private final static String SYS_QH_QUESTIONS_TYPE = "sys_qh_questions_type";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<QhQuestionBank> generatePaper(QhGeneratePaperDTO paperDTO) {
        validateGeneratePaperDTO(paperDTO);
        checkPaperNameUnique(paperDTO.getCpaperName());
        // 获取候选题目池
        List<QhQuestionBank> candidates = getCandidateQuestions(paperDTO);
        List<QhQuestionBank> optimized = this.optimize(paperDTO, candidates);
        if (CollectionUtils.isEmpty(optimized)) {
            throw new QhException("未找到符合条件的题目！");
        }
        // 创建试卷
        creationExam(paperDTO, optimized);
//        List<QhQuestionBank> questionBankList = new ArrayList<>();
//        // 1. 处理题库 -> 知识点
//        Map<String, List<QhKnowledgeTree>> treeMap = new HashMap<>();
//        if (!CollectionUtils.isEmpty(paperDTO.getLibraryIds())) {
//            for (String libraryId : paperDTO.getLibraryIds()) {
//                QhKnowledgeTree query = new QhKnowledgeTree();
//                query.setAncestors(libraryId);
//                List<QhKnowledgeTree> knowledgeTreeList = iQhKnowledgeTreeService.selectKnowledgeTreeList(query);
//                if (CollectionUtils.isNotEmpty(knowledgeTreeList)) {
//                    List<String> gradeIds = knowledgeTreeList.stream().filter(item -> libraryId.equals(item.getParentId()))
//                            .map(QhKnowledgeTree::getId).collect(Collectors.toList());
//                    for (QhKnowledgeTree item : knowledgeTreeList) {
//                        String ancestors = item.getAncestors();
//                        if (ancestors == null || ancestors.isEmpty()) continue;
//                        String[] ancestorArr = ancestors.split(",");
//                        for (String gradeId : gradeIds) {
//                            if (Arrays.asList(ancestorArr).contains(gradeId)) {
//                                treeMap.computeIfAbsent(gradeId, k -> new ArrayList<>()).add(item);
//                            }
//                        }
//                    }
//                }
//            }
//
//        }
//        // 处理每个题型配置
//        for (QhQuestionDTO questionDTO : paperDTO.getQuestionDTOList()) {
//            QhQuestionBank bank = new QhQuestionBank();
//            bank.setDifficulty(questionDTO.getDifficulty());
//            bank.setQuestionType(questionDTO.getQuestionType());
//            bank.setGradeId(questionDTO.getGradeId());
//            bank.setScore(questionDTO.getScore());
//            bank.setYear(questionDTO.getYear());
//            bank.setRegion(questionDTO.getRegion());
//            // 处理知识点（年级,章节,直接选择的知识点）
//            List<String> knowledgeIds = new ArrayList<>();
//            if (!treeMap.isEmpty() && StrUtil.isNotBlank(questionDTO.getGradeId())) {
//                List<QhKnowledgeTree> knowledgeTrees = treeMap.get(questionDTO.getGradeId());
//                knowledgeIds.addAll(knowledgeTrees.stream().filter(Objects::nonNull).filter(it -> StrUtil.equals("5", it.getNodeType())).map(QhKnowledgeTree::getId).collect(Collectors.toList()));
//            }
//            if (!CollectionUtils.isEmpty(questionDTO.getChapters())) {
//                List<QhKnowledgeTree> knowledgeTreeList = new ArrayList<>();
//                for (String chapter : questionDTO.getChapters()) {
//                    QhKnowledgeTree query = new QhKnowledgeTree();
//                    query.setAncestors(chapter);
//                    knowledgeTreeList.addAll(iQhKnowledgeTreeService.selectKnowledgeTreeList(query));
//                }
//                knowledgeIds.addAll(knowledgeTreeList.stream().filter(Objects::nonNull).filter(it -> StrUtil.equals("5", it.getNodeType())).map(QhKnowledgeTree::getId).collect(Collectors.toList()));
//            }
//            if (!CollectionUtils.isEmpty(questionDTO.getKnowledgePoints())) {
//                knowledgeIds.addAll(questionDTO.getKnowledgePoints());
//            }
//            if (CollectionUtils.isNotEmpty(knowledgeIds)) {
//                bank.setKnowledgeTreeIds(knowledgeIds.stream().distinct().collect(Collectors.toList()));
//                List<QhQuestionBank> bankList = questionBankMapper.selectBankList(bank);
//                if (CollectionUtils.isNotEmpty(bankList)) {
//                    // 随机选择题目
//                    questionBankList.addAll(validQuestions(bankList, questionDTO.getRequiredNumber()));
//                }
//            } else {
//                List<QhQuestionBank> bankList = questionBankMapper.selectBankList(bank);
//                if (CollectionUtils.isNotEmpty(bankList)) {
//                    // 随机选择题目
//                    questionBankList.addAll(validQuestions(bankList, questionDTO.getRequiredNumber()));
//                }
//            }
//        }
//        if (CollectionUtils.isEmpty(questionBankList)) {
//            throw new QhException("未查到符合的题目，请重新选择！");
//        }
//        creationExam(paperDTO, questionBankList);
        return optimized;
    }

    // 获取候选题目（合并单配置和多配置）
    private List<QhQuestionBank> getCandidateQuestions(QhGeneratePaperDTO paperDTO) {
        List<QhQuestionBank> candidates = new ArrayList<>();
        // 处理单配置
        paperDTO.getQuestionDTOList().forEach(dto ->
                candidates.addAll(questionBankMapper.selectByComplexCondition(buildQuery(dto)))
        );
        // 处理多配置
        paperDTO.getQuestionDuoDTOList().forEach(duoDTO ->
                candidates.addAll(questionBankMapper.selectByComplexCondition(buildQuery(duoDTO)))
        );
        return candidates.stream().distinct().collect(Collectors.toList());
    }

    // 构建查询条件
    private QueryCondition buildQuery(Object config) {
        QueryCondition condition = new QueryCondition();
        if (config instanceof QhQuestionDTO) {
            QhQuestionDTO dto = (QhQuestionDTO) config;
            condition.setQuestionType(dto.getQuestionType());
            condition.setGrades(Collections.singletonList(dto.getGradeId()));
            condition.setYears(Collections.singletonList(dto.getYear()));
            condition.setTypes(Collections.singletonList(dto.getQuestionType()));
            // 其他字段...
        } else if (config instanceof QhQuestionDuoDTO) {
            QhQuestionDuoDTO duoDTO = (QhQuestionDuoDTO) config;
            condition.setGrades(duoDTO.getGradeIds());
            condition.setYears(duoDTO.getYears());
            condition.setTypes(duoDTO.getPaperTypes());
            // 其他字段...
        }
        return condition;
    }

    // 校验试卷名称唯一性
    private void checkPaperNameUnique(String name) {
        QhExamPaper query = new QhExamPaper();
        query.setPaperName(name);
        if (examPaperMapper.selectQhExamPaperList(query).size() > 0) {
            throw new QhException("试卷名称已存在！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<QhQuestionBank> pingxing(String id, String flag) {
        if (StrUtil.isEmpty(id)) {
            throw new QhException("请求试卷参数为空，请检查！");
        }
        QhExamPaper paper = new QhExamPaper();
        paper.setId(id);
        paper.setFlag(flag);
        //flag 为ZJ
        List<QhExamPaper> paperList = examPaperMapper.selectQhExamPaperList(paper);
        if (CollectionUtils.isEmpty(paperList)) {
            throw new QhException("试卷不存在，请检查！");
        }
        //获取题目信息
        List<String> knowledgeList = new ArrayList<>();
        List<QhQuestionBank> questionBankList = new ArrayList<>();
        if (StrUtil.equals("ZJ", flag)) {
            questionBankList = SpringUtils.getAopProxy(this).selectQhExamPaperById(id);
        } else {
            QhQuestionBank bank = new QhQuestionBank();
            bank.setSourcePaper(id);
            questionBankList = questionBankMapper.selectBankList(bank);
            fillBoardData(questionBankList);
        }

        if (CollectionUtils.isEmpty(questionBankList)) {
            throw new QhException("题目不存在，请检查！");
        }
        // 特征分析
        Map<String, TypeFeature> typeFeatures = analyzePaperFeatures(questionBankList);
        List<String> sourcePaperList = questionBankList.stream().map(QhQuestionBank::getSourcePaper).distinct().collect(Collectors.toList());
        List<QhExamPaper> qhExamPaperList = examPaperMapper.selectBatchQhExamPaperList(sourcePaperList);
        //获取题目的信息组装地区、年份、年级
        analyzePaperFeatures1(typeFeatures, qhExamPaperList);
        // 并行处理各题型
        List<QhQuestionBank> questionList = generateQuestionsByType(typeFeatures);
        if (CollectionUtils.isEmpty(questionList)) {
            throw new QhException("未查到符合的题目，请重新选择！");
        }
        QhGeneratePaperDTO paperDTO = new QhGeneratePaperDTO();
        List<QhQuestionDTO> questionDTOList = new ArrayList<>();
        questionList.forEach(it -> {
            QhQuestionDTO questionDTO = new QhQuestionDTO();
            BeanUtils.copyProperties(it, questionDTO);
            questionDTOList.add(questionDTO);
        });
        paperDTO.setCpaperName(DateUtils.getTime() + "-平行组卷");
        paperDTO.setPaperStyle("3");
        paperDTO.setQuestionDTOList(questionDTOList);
        creationExam(paperDTO, questionList);
        return questionList;
    }

    //参数校验
    private void validateGeneratePaperDTO(QhGeneratePaperDTO dto) {
        if (StrUtil.isBlank(dto.getCpaperName())) {
            throw new QhException("试卷名称不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getQuestionDuoDTOList())) {
            throw new QhException("至少需要配置一种题型");
        }

        dto.getQuestionDuoDTOList().forEach(q -> {
            if (StrUtil.isBlank(q.getQuestionType())) {
                throw new QhException("题型不能为空");
            }
            if (q.getRequiredNumber() == null || q.getRequiredNumber() <= 0) {
                throw new QhException("题目数量必须大于0");
            }
        });
    }

    private int creationExam(QhGeneratePaperDTO paperDTO, List<QhQuestionBank> questionBankList) {

        QhExamPaper examPaper = createExamPaper(paperDTO);
        int insertPaper = examPaperMapper.insertQhExamPaper(examPaper);
        if (insertPaper < 0) {
            throw new QhException("插入试卷表失败，请检查！");
        }
        saveExamPaperQuestion(examPaper.getId(), questionBankList, null);
        return insertPaper;
    }


    private List<QhQuestionBank> validQuestions(List<QhQuestionBank> candidates, int required) {
        if (candidates.size() < required) {
            throw new QhException("可用题目不足，需要：" + required + "，实际：" + candidates.size());
        }
        Collections.shuffle(candidates);
        return candidates.subList(0, required);
    }

    private String sumScores(List<String> scoreList) {
        BigDecimal total = BigDecimal.ZERO;
        for (String score : scoreList) {
            total = total.add(new BigDecimal(score));
        }
        return total.stripTrailingZeros().toPlainString();
    }


    private QhExamPaper createExamPaper(QhGeneratePaperDTO paperDTO) {
        //List<SysDictData> dictDatas = DictUtils.getDictCache(SYS_QH_PAPER_TYPE);
        List<String> scoreList = paperDTO.getQuestionDTOList().stream().map(QhQuestionDTO::getScore).collect(Collectors.toList());
        List<String> idList = paperDTO.getQuestionDTOList().stream().map(QhQuestionDTO::getId).collect(Collectors.toList());
        List<String> strings = iQhKnowledgeTreeService.queryLyByIds(idList);
        String score = sumScores(scoreList);
        String id = IdUtils.fastSimpleUUID();
        QhExamPaper paper = new QhExamPaper();
        paper.setId(id);
        paper.setPaperName(paperDTO.getCpaperName());
        paper.setPaperType(paperDTO.getCpaperType()); //先默认写死
        if (CollectionUtils.isNotEmpty(strings)) {
            paper.setSourcePaper(String.join( ",", strings));
        } else {
            paper.setSourcePaper("获取不到题库来源！");
        }
        paper.setRegion(paperDTO.getCregion());
        paper.setPaperStyle(paperDTO.getPaperStyle());
        paper.setPyear(paperDTO.getCyear());
        paper.setNum(String.valueOf(paperDTO.getQuestionDTOList().size()));
        paper.setScore(score);
        paper.setDelFlag("0");
        paper.setGdelFlag("0");
        paper.setRegion(paperDTO.getCregion());
        paper.setFolder(paperDTO.getMulu());
        paper.setCreateBy(SecurityUtils.getUsername());  // 后续改
        paper.setUpdateBy(SecurityUtils.getUsername());  // 后续改
        paper.setCreateTime(new Date());
        paper.setUpdateTime(new Date());
        return paper;
    }

    private void saveExamPaperQuestion(String paperId, List<QhQuestionBank> questionBankList, String score) {
        // 保存关联对象
        List<QhExamPaperQuestion> relations = questionBankList.stream().map(it -> {
            QhExamPaperQuestion paperQuestion = new QhExamPaperQuestion();
            paperQuestion.setId(IdUtils.fastSimpleUUID());
            paperQuestion.setPaperId(paperId);
            paperQuestion.setQuestionBankId(it.getId());
            // 若有分值存储需求，可扩展关联表字段
            return paperQuestion;
        }).collect(Collectors.toList());
        for (QhQuestionBank qhQuestionBank : questionBankList) {
            questionBankMapper.updateQuestionBank(qhQuestionBank);
        }
        // 批量插入
        examPaperMapper.batchInsert(relations);
    }

    /**
     * 查询试卷题目
     *
     * @param id 试卷主键
     * @return 试卷
     */
    @Override
    public List<QhQuestionBank> selectQhExamPaperById(String id) {
        List<String> bankIds = examPaperMapper.selectBankIds(id);
        if (CollectionUtils.isEmpty(bankIds)) {
            return Collections.emptyList();
        }
        List<QhQuestionBank> list = questionBankMapper.selectQuestionBankBatchByIds(bankIds);
        // 填充节点数据
        fillBoardData(list);
        return list;
    }

    @Override
    public List<QhQuestionBank> selectQhExamPaperById(String id, String flag) {
        List<QhQuestionBank> list = new ArrayList<>();
        if (StrUtil.equals("ZJ", flag)) {
            List<String> bankIds = examPaperMapper.selectBankIds(id);
            if (CollectionUtils.isEmpty(bankIds)) {
                return Collections.emptyList();
            }
            list = questionBankMapper.selectQuestionBankBatchByIds(bankIds);
            // 填充节点数据
            fillBoardData(list);
        } else {
            QhQuestionBank bank = new QhQuestionBank();
            bank.setSourcePaper(id);
            list = questionBankMapper.selectBankList(bank);
            fillBoardData(list);
        }
        return list;
    }

    private void fillBoardData(List<QhQuestionBank> questionBankList) {
        if (CollectionUtils.isEmpty(questionBankList)) {
            return;
        }
        // 填充节点信息
        List<String> ids = questionBankList.stream().map(QhQuestionBank::getId).collect(Collectors.toList());
        List<QhKnowledgeQuestion> questionList = knowledgeQuestionMapper.batchSelectBankByKids(null, ids);
        if (CollectionUtils.isEmpty(questionList)) {
            return;
        }
        Map<String, List<QhKnowledgeQuestion>> questionMap = questionList.stream().collect(Collectors.groupingBy(QhKnowledgeQuestion::getQuestionBankId));
        questionBankList.forEach(qhQuestionBank -> {
            // 汇总节点id
            List<String> knowledgeTreeIds = questionMap.get(qhQuestionBank.getId()).stream().map(QhKnowledgeQuestion::getKnowledgeTreeId).collect(Collectors.toList());
            // 根据节点id查询具体节点
            List<QhKnowledgeTree> qhKnowledgeTrees = qhKnowledgeTreeMapper.selectKnowledgeByIds(knowledgeTreeIds);
            // 设置节点信息
            qhQuestionBank.setKnowledgeTreeList(qhKnowledgeTrees);
        });
    }

    @Override
    public QhExamPaper queryQhExamPaperById(String id) {
        return examPaperMapper.selectQhExamPaperById(id);
    }

    @Override
    public Integer dashboard(String beginTime, String endTime) {
        return examPaperMapper.selectCount(beginTime, endTime);
    }

    /**
     * 查询试卷分类列表
     *
     * @param qhExamPaper 试卷分类
     * @return 试卷分类
     */
    @Override
    public List<QhExamPaper> selectQhExamPaperList(QhExamPaper qhExamPaper) {
        return examPaperMapper.selectQhExamPaperList(qhExamPaper);
    }

    /**
     * 新增试卷
     *
     * @param paperDTO 试卷创建
     * @return 结果
     */
    @Override
    public int insertQhExamPaper(QhGeneratePaperDTO paperDTO) {
        if (ObjectUtil.isNull(paperDTO)) {
            throw new QhException("试卷信息为空，请检查！");
        }
        QhExamPaper paper = new QhExamPaper();
        paper.setPaperName(paperDTO.getCpaperName());
        List<QhExamPaper> paperList = examPaperMapper.selectQhExamPaperList(paper);
        if (CollectionUtils.isNotEmpty(paperList)) {
            throw new QhException("试卷名称已存在，请检查！");
        }
        if (CollectionUtils.isEmpty(paperDTO.getQuestionDTOList())) {
            throw new QhException("无题目，请检查！");
        }
        List<QhQuestionDTO> questionDTOList = paperDTO.getQuestionDTOList();
        List<QhQuestionBank> questionBankList = new ArrayList<>(questionDTOList.size());
        for (QhQuestionDTO source : questionDTOList) {
            QhQuestionBank target = new QhQuestionBank();
            BeanUtils.copyProperties(source, target);
            questionBankList.add(target);
        }
        return creationExam(paperDTO, questionBankList);
    }

    /**
     * 修改试卷分类
     *
     * @param qhExamPaper 试卷分类
     * @return 结果
     */
    @Override
    public int updateQhExamPaper(QhExamPaper qhExamPaper) {
        qhExamPaper.setUpdateTime(DateUtils.getNowDate());
        return examPaperMapper.updateQhExamPaper(qhExamPaper);
    }

    /**
     * 批量删除试卷
     *
     * @param ids 需要删除的试卷主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteQhExamPaperByIds(String[] ids, String flag) {
        //examPaperMapper.deleteQhExamPaperQuestionByIds(ids)
        if (StringUtils.equals("ZJ", flag)) {
            return examPaperMapper.deleteQhGExamPaperByIds(ids);
        } else if (StringUtils.equals("SJ", flag)) {
            // SJ标志：试卷删除，需要同时删除相关题目和ES数据
            int result = examPaperMapper.deleteQhExamPaperByIds(ids);

            // 查询并删除试卷相关的题目
            for (String paperId : ids) {
                try {
                    // 查询试卷相关的题目
                    QhQuestionBank bank = new QhQuestionBank();
                    bank.setSourcePaper(paperId);
                    List<QhQuestionBank> relatedQuestions = questionBankMapper.selectBankList(bank);
                    if (CollectionUtils.isNotEmpty(relatedQuestions)) {
                        // 提取题目ID列表
                        List<String> questionIds = relatedQuestions.stream()
                                .map(QhQuestionBank::getId)
                                .collect(Collectors.toList());
                        // 逻辑删除题目（数据库）
                        questionBankService.deleteQuestionBankByIds(questionIds);
                        // 逻辑删除ES中的题目
                        try {
                            questionElasticSearchService.logicalDeleteQuestionsByIds(questionIds);
                        } catch (IOException ioException) {
                            log.error("逻辑删除ES中的题目失败，题目ID列表：{}，错误信息：{}", questionIds, ioException.getMessage());
                            throw new QhException("逻辑删除ES中的题目失败: " + ioException.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("删除试卷 {} 相关题目失败：{}", paperId, e.getMessage(), e);
                    throw new QhException("删除试卷相关题目失败: " + e.getMessage());
                }
            }
            return result;
        } else {
            return examPaperMapper.deleteQhExamPaperByIds(ids);
        }
    }

    /**
     * 删除试卷信息, 标记是组卷还是试卷删除
     *
     * @param id 试卷主键
     * @return 结果
     */
    @Override
    public int deleteQhExamPaperById(String id, String flag) {
        //examPaperMapper.deleteQhExamPaperQuestionById(id)
        if (StringUtils.equals("ZJ", flag)) {
            return examPaperMapper.deleteQhGExamPaperById(id);
        } else {
            return examPaperMapper.deleteQhExamPaperById(id);
        }
    }

    private List<QhQuestionBank> generateQuestionsByType(Map<String, TypeFeature> typeFeatures) {
        // 串行处理各题型
        return typeFeatures.entrySet().stream()
                .flatMap(entry -> {
                    String questionType = entry.getKey();
                    TypeFeature feature = entry.getValue();
                    try {
                        QueryCondition condition = buildQueryCondition(questionType, feature);
                        List<QhQuestionBank> candidates = questionBankMapper.selectByCondition(condition)
                                .stream().filter(Objects::nonNull).filter(it -> !feature.getOriginalIds().contains(it.getId())).collect(Collectors.toList());
                        if (candidates.size() < feature.getCount() * 0.5) {
                            condition = relaxCondition(condition);
                            candidates = questionBankMapper.selectByCondition(condition)
                                    .stream().filter(it -> !feature.getOriginalIds().contains(it.getId())).collect(Collectors.toList());
                        }
                        if (candidates.size() < feature.getCount()) {
                            throw new QhException("可用题目不足，需要：" + feature.getCount() + "，实际：" + candidates.size() + ", 请检查！");
                        }
                        List<QhQuestionBank> selected = selectOptimalQuestions(candidates, feature);
                        return selected.stream();
                    } catch (Exception e) {
                        log.error("题型{}组卷失败: {}", questionType, e.getMessage());
                        List<SysDictData> dictDatas = DictUtils.getDictCache(SYS_QH_QUESTIONS_TYPE);
                        Map<String, String> questionsMap = dictDatas.stream().filter(Objects::nonNull).collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
                        throw new QhException("题型: " + questionsMap.get(questionType) + " ,平行组卷失败," + e.getMessage());
                    }
                }).collect(Collectors.toList());
    }

    private QueryCondition relaxCondition(QueryCondition original) {
        QueryCondition relaxed = new QueryCondition();
        BeanUtils.copyProperties(original, relaxed);
        // 放宽难度范围
        relaxed.setMinDifficulty(Math.max(0, relaxed.getMinDifficulty() - 0.5));
        relaxed.setMaxDifficulty(Math.min(4, relaxed.getMaxDifficulty() + 0.5));
        // 移除地区限制
        relaxed.setRegions(null);
        // 放宽分数范围
        relaxed.setMinScore(relaxed.getMinScore() * 0.8);
        relaxed.setMaxScore(relaxed.getMaxScore() * 1.2);
        return relaxed;
    }

    // 特征分析核心方法
    private Map<String, TypeFeature> analyzePaperFeatures(List<QhQuestionBank> questions) {
        return questions.stream()
                .collect(Collectors.groupingBy(
                        QhQuestionBank::getQuestionType,
                        Collectors.collectingAndThen(Collectors.toList(), this::buildTypeFeature)));
    }

    private void analyzePaperFeatures1(Map<String, TypeFeature> typeFeatureMap, List<QhExamPaper> qhExamPaperList) {
        if (CollectionUtils.isEmpty(qhExamPaperList)) {
            return;
        }
        List<String> regionList = qhExamPaperList.stream().map(QhExamPaper::getRegion).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> yearList = qhExamPaperList.stream().map(QhExamPaper::getPyear).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> grageList = qhExamPaperList.stream().map(QhExamPaper::getGrade).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        for (Map.Entry<String, TypeFeature> entry : typeFeatureMap.entrySet()) {
            TypeFeature feature = entry.getValue();
            Multiset<String> yearSet = HashMultiset.create();
            if (CollectionUtils.isNotEmpty(yearList)) {
                yearSet.addAll(yearList);
                feature.setMainYears(processMainFeatures(yearSet, feature.getCount()));
            }

            Multiset<String> gradeSet = HashMultiset.create();

            if (CollectionUtils.isNotEmpty(grageList)) {
                gradeSet.addAll(grageList);
                feature.setMainGrades(processMainFeatures(gradeSet, feature.getCount()));
            }

            Multiset<String> regionSet = HashMultiset.create();
            if (CollectionUtils.isNotEmpty(regionList)) {
                regionSet.addAll(regionList);
                feature.setMainRegions(processMainFeatures(regionSet, feature.getCount()));
            }
        }
    }

    private TypeFeature buildTypeFeature(List<QhQuestionBank> questions) {
        TypeFeature feature = new TypeFeature();
        feature.setQuestionType(questions.get(0).getQuestionType());
        feature.setCount(questions.size());
        feature.setOriginalIds(questions.stream().map(QhQuestionBank::getId).collect(Collectors.toSet()));
        // 处理分数（String转Double）
        feature.setTotalScore(questions.stream().mapToDouble(it -> parseScore(it.getScore())).sum());
        // 处理难度（String转数值）
        List<Double> difficulties = questions.stream().map(it -> Double.parseDouble(it.getDifficulty())).collect(Collectors.toList());
        feature.setAvgDifficulty(calculateAverage(difficulties));
        feature.setDifficultyStd(calculateStdDev(difficulties, feature.getAvgDifficulty()));
        List<String> knowledgeIds = new ArrayList<>();
        for (QhQuestionBank question : questions) {
            if (CollectionUtils.isEmpty(question.getKnowledgeTreeList())) continue;
            List<QhKnowledgeTree> knowledgeTreeList = question.getKnowledgeTreeList();
            List<String> collect = knowledgeTreeList.stream().map(QhKnowledgeTree::getId).distinct().collect(Collectors.toList());
            knowledgeIds.addAll(collect);
        }
        feature.setKnowledgeList(knowledgeIds);
        return feature;
    }

    // 动态构建查询条件
    private QueryCondition buildQueryCondition(String questionType, TypeFeature feature) {
        QueryCondition condition = new QueryCondition();
        condition.setQuestionType(questionType);
        condition.setAvgDifficulty(feature.getAvgDifficulty());

        // 难度范围
        double difficultyRange = calculateDifficultyRange(feature);
        condition.setMinDifficulty(feature.getAvgDifficulty() - difficultyRange);
        condition.setMaxDifficulty(feature.getAvgDifficulty() + difficultyRange);

        // 处理月份
        if (CollectionUtils.isNotEmpty(feature.getMainYears())) {
            condition.setYears(feature.getMainYears().stream().map(Multiset.Entry::getElement)
                    .limit(getLimitCount(feature.getMainYears(), feature.getCount())).collect(Collectors.toList()));
        }

        // 处理年级
        if (CollectionUtils.isNotEmpty(feature.getMainGrades())) {
            condition.setGrades(feature.getMainGrades().stream().map(Multiset.Entry::getElement)
                    .limit(getLimitCount(feature.getMainGrades(), feature.getCount())).collect(Collectors.toList()));
        }

        // 处理地区
        if (CollectionUtils.isNotEmpty(feature.getMainRegions())) {
            condition.setRegions(feature.getMainRegions().stream().map(Multiset.Entry::getElement)
                    .limit(getLimitCount(feature.getMainRegions(), feature.getCount())).collect(Collectors.toList()));
        }

        // 分数范围
        double avgScore = feature.getTotalScore() / feature.getCount();
        condition.setMinScore(avgScore * 0.8);
        condition.setMaxScore(avgScore * 1.2);
        condition.setKnowledgeTreeIds(feature.getKnowledgeList());
        return condition;
    }

    // 难度平均值
    private double calculateAverage(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        return values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }

    private double calculateStdDev(List<Double> values, double mean) {
        if (values == null || values.size() < 2) {
            return 0.0; // 标准差需要至少两个数据点
        }
        double variance = values.stream().mapToDouble(d -> Math.pow(d - mean, 2)).sum() / (values.size() - 1); // 样本方差用n-1
        return Math.sqrt(variance);
    }

    // 特征处理方法 阈值设为15%，即只有占比超过15%的特征项才会被保留
    private List<Multiset.Entry<String>> processMainFeatures(Multiset<String> featureSet, int totalCount) {
        double THRESHOLD_RATIO = 0.15;
        return featureSet.entrySet().stream()
                .filter(e -> (double)e.getCount() / totalCount > THRESHOLD_RATIO)
                .sorted((e1, e2) -> e2.getCount() - e1.getCount()).collect(Collectors.toList());
    }

    private double calculateDifficultyRange(TypeFeature feature) {
        // 当题目数量少时（<5）使用固定范围
        if (feature.getCount() < 5) {
            return 1.0;
        }
        // 标准差 + 缓冲值（0.5-1.0动态调整）
        double buffer = Math.min(1.0, Math.max(0.5, feature.getDifficultyStd() * 0.3));
        return feature.getDifficultyStd() + buffer;
    }

    private double calculateDifficultyScore(QhQuestionBank bank, TypeFeature feature) {
        double qDifficulty = Double.parseDouble(bank.getDifficulty());
        //feature.getDifficultyStd() + 0.5 防止分母为空
        // 高斯函数计算匹配度，标准差越大容忍范围越大
        double exponent = -Math.pow(qDifficulty - feature.getAvgDifficulty(), 2) / (2 * Math.pow(feature.getDifficultyStd() + 0.5, 2));
        return Math.exp(exponent);
    }

    private double calculateMonthScore(QhQuestionBank bank, TypeFeature feature) {
        if (StringUtils.isBlank(bank.getYear())) return 0.0;
        String qMonth = bank.getYear();
        return feature.getMainYears().stream().anyMatch(e -> e.getElement().equals(qMonth)) ? 1.0 : 0.0;
    }

    //此方法年级待定。后续修改，因为年级不在题目表中
    private double calculateGradeScore(QhQuestionBank bank, TypeFeature feature) {
        if (feature.getMainGrades() == null || feature.getMainGrades().isEmpty()) return 0.0;
        // 查找匹配年级并计算权重
        return feature.getMainGrades().stream().filter(e -> e.getElement().equals(bank.getGradeId())).findFirst()
                .map(e -> {
                    // 根据占比计算分数（出现次数/总题数）
                    double ratio = (double)e.getCount() / feature.getCount();
                    // 使用S型函数增强主要特征
                    return 1.0 / (1 + Math.exp(-10*(ratio - 0.15)));
                }).orElse(0.0);
    }

    private double calculateScoreMatch(QhQuestionBank bank, TypeFeature feature) {
        double expectedScore = feature.getTotalScore() / feature.getCount();
        double actualScore = parseScore(bank.getScore());
        if (expectedScore == 0) return 0.0;
        // 相对误差计算（限制在0-1之间）
        double relativeError = Math.abs(actualScore - expectedScore) / expectedScore;
        return 1.0 - Math.min(1.0, relativeError);
    }

    // 组合评分方法，权重配置目前写死 总和需保持为1.0
    private double combineScores(double difficultyScore, double regionScore, double gradeScore, double scoreMatch, double yearScore) {
        Map<String, Double> weights = ImmutableMap.of(
                "difficulty", 0.25,"region", 0.2, "grade", 0.2, "score", 0.2, "year", 0.15);

        return difficultyScore * weights.get("difficulty") + regionScore * weights.get("region")
                + gradeScore * weights.get("grade") + scoreMatch * weights.get("score") + yearScore * weights.get("year");
    }

    private double calculateMatchScore(QhQuestionBank bank, TypeFeature feature) {
        // 计算各维度分数时不再设置到question对象
        double diffScore = calculateDifficultyScore(bank, feature);
        double yearScore = calculateMonthScore(bank, feature);
        double regionScore = calculateRegionScore(bank, feature);
        double gradeScore = calculateGradeScore(bank, feature);
        double scoreMatch = calculateScoreMatch(bank, feature);
        return combineScores(diffScore, regionScore, gradeScore, scoreMatch, yearScore);
    }



    private List<QhQuestionBank> selectOptimalQuestions(List<QhQuestionBank> candidates, TypeFeature feature) {
        // 计算并排序匹配结果
        List<QuestionMatchResult> matchResults = candidates.stream()
                .map(it -> new QuestionMatchResult(it, calculateMatchScore(it, feature)))
                .sorted((a,b) -> Double.compare(b.getMatchScore(), a.getMatchScore())).collect(Collectors.toList());
        // 执行选择
        return selectByScore(matchResults, feature);
    }



    private List<QhQuestionBank> selectByScore(List<QuestionMatchResult> matchResults, TypeFeature feature) {
        List<QhQuestionBank> result = new ArrayList<>();
        double targetTotal = feature.getTotalScore();
        int targetCount = feature.getCount();
        double currentTotal = 0.0;
        // 第一步：按匹配度降序选择，允许总分比原试卷多15%
        for (QuestionMatchResult match : matchResults) {
            QhQuestionBank bank = match.getQuestion();
            if (result.size() >= targetCount) break;
            double questionScore = parseScore(bank.getScore());
            if (currentTotal + questionScore <= targetTotal * 1.15) {
                result.add(bank);
                currentTotal += questionScore;
            }
        }

        // 第二步：分数偏差补偿
        if (Math.abs(currentTotal - targetTotal) > targetTotal * 0.15) {
            adjustWithMatchScore(result, matchResults, targetTotal, currentTotal);
        }
        return result.stream().limit(targetCount).collect(Collectors.toList());
    }


    private void adjustWithMatchScore(List<QhQuestionBank> result, List<QuestionMatchResult> allMatches, double target, double current) {
        double deviation = target - current;
        // 构建未选中的候选集
        List<QuestionMatchResult> remaining = allMatches.stream()
                .filter(match -> !result.contains(match.getQuestion())).collect(Collectors.toList());
        // 如果没有候选题目，无法调整
        if (remaining.isEmpty()) return;
        // 寻找最优补偿题目 分数偏差小且匹配度高
        Optional<QuestionMatchResult> bestMatch = remaining.stream()
                .min(Comparator.comparingDouble(match -> {
                    double scoreDiff = Math.abs(parseScore(match.getQuestion().getScore()) - deviation);
                    return scoreDiff * 0.7 - match.getMatchScore() * 0.3; // 权重可调
                }));

        bestMatch.ifPresent(match -> {
            if (!result.isEmpty()) result.remove(result.size() - 1);
            result.add(match.getQuestion());
        });
    }

    private int getLimitCount(List<Multiset.Entry<String>> mainFeatures, int totalCount) {
        if (mainFeatures.isEmpty()) return 0;
        // 最大特征占比
        double maxRatio = (double)mainFeatures.get(0).getCount() / totalCount;
        // 主导特征明显 高于60%、30-60%、小于30%
        if (maxRatio > 0.6) {
            return Math.min(2, mainFeatures.size());
        } else if (maxRatio > 0.3) {
            return Math.min(3, mainFeatures.size());
        } else {
            return Math.min(5, mainFeatures.size());
        }
    }

    // 地区评分计算
    private double calculateRegionScore(QhQuestionBank bank, TypeFeature feature) {
        if (StringUtils.isBlank(bank.getRegion())) return 0;
        String normalizedRegion = normalizeRegion(bank.getRegion());
        return feature.getMainRegions().stream().anyMatch(e -> e.getElement().equals(normalizedRegion)) ? 1.0 : 0.0;
    }

    // 其他工具方法
//    private double convertDifficulty(String difficulty) {
//        List<SysDictData> dictDatas = DictUtils.getDictCache(SYS_QH_PAPER_TYPE);
//        Assert.isTrue(CollectionUtils.isNotEmpty(dictDatas), SYS_QH_PAPER_TYPE + "字典未维护，请检查！");
//        Map<String, Double> difficultyMapping = dictDatas.stream().filter(Objects::nonNull).collect(Collectors.toMap(SysDictData::getDictLabel, it -> Double.parseDouble(it.getDictValue()), (k1, k2) -> k1));
//        return difficultyMapping.getOrDefault(StringUtils.trimToEmpty(difficulty), 2.0);  //2正常类型
//    }

    private double parseScore(String score) {
        try {
            return Double.parseDouble(StringUtils.trimToEmpty(score));
        } catch (NumberFormatException e) {
            log.warn("无效的分数: {}", score);
            return 0.0;
        }
    }

    private String normalizeRegion(String region) {
        return StringUtils.trimToEmpty(region).replaceAll("地区$", "").toLowerCase();
    }

    @Data
    static class QuestionMatchResult {
        private QhQuestionBank question;
        private Double matchScore;

        public QuestionMatchResult(QhQuestionBank question, Double matchScore) {
            this.question = question;
            this.matchScore = matchScore;
        }
    }

    // 特征类和查询条件类
    @Data
    static class TypeFeature {
        private String questionType;
        private int count;
        private double totalScore;
        private double avgDifficulty;  //难度平均值
        private double difficultyStd;  //难度标准差
        private Set<String> originalIds;
        private List<String> knowledgeList;
        private List<Multiset.Entry<String>> mainYears;
        private List<Multiset.Entry<String>> mainGrades;
        private List<Multiset.Entry<String>> mainRegions;
    }

    public List<QhQuestionBank> optimize(QhGeneratePaperDTO config, List<QhQuestionBank> candidateQuestions) {
        List<PaperChromosome> population = initializePopulation(config);
        PaperChromosome bestChromosome = null;

        // 2. 迭代优化
        for (int gen = 0; gen < config.getMaxGeneration(); gen++) {
            // 计算适应度并选择
            population.forEach(it -> calculateFitness(it, config));
            population = select(population);
            // 交叉与变异
            population = crossover(population, config);
            mutate(population, config, candidateQuestions);
            // 记录当前最优
            bestChromosome = getBestChromosome(population);
        }

        return bestChromosome != null ? bestChromosome.getQuestions() : Collections.emptyList();
    }

    // 初始化种群
    private List<PaperChromosome> initializePopulation(QhGeneratePaperDTO config) {
        return IntStream.range(0, config.getPopulationSize()).mapToObj(i -> generateRandomChromosome(config))
                .collect(Collectors.toList());
    }

    // 生成随机染色体
    private PaperChromosome generateRandomChromosome(QhGeneratePaperDTO config) {
        List<QhQuestionBank> chromosome = new ArrayList<>();

        // 处理单配置DTO
        config.getQuestionDTOList().forEach(dto -> {
            List<QhQuestionBank> questions = pickQuestionsByDTO(dto);
            if (questions.size() < dto.getRequiredNumber()) {
                throw new QhException("题库中题型[" + dto.getQuestionType() + "]题目不足，无法生成试卷");
            }
            // 随机选取所需数量的题目
            Collections.shuffle(questions);
            chromosome.addAll(questions.subList(0, dto.getRequiredNumber()));
        });

        // 处理多配置DuoDTO
        config.getQuestionDuoDTOList().forEach(duoDTO -> {
            List<QhQuestionBank> questions = pickQuestionsByDuoDTO(duoDTO);
            if (questions.size() < duoDTO.getRequiredNumber()) {
                throw new QhException("题库中题型[" + duoDTO.getQuestionType() + "]题目不足，无法生成试卷");
            }
            Collections.shuffle(questions);
            chromosome.addAll(questions.subList(0, duoDTO.getRequiredNumber()));
        });

        return new PaperChromosome(chromosome, 0, 0, 0, 0);
    }

    private void calculateFitness(PaperChromosome chromosome, QhGeneratePaperDTO config) {
        // 1. 难度得分（原有逻辑）
        double expectedDiff = calculateExpectedDifficulty(config);
        double actualDiff = chromosome.getQuestions().stream()
                .mapToDouble(q -> Double.parseDouble(q.getDifficulty()))
                .average().orElse(0);
        chromosome.setDifficultyScore(1 / (1 + Math.abs(expectedDiff - actualDiff)));

        // 2. 知识点覆盖率得分（原有逻辑）
        Set<String> requiredKnowledge = getAllRequiredKnowledgePoints(config);
        Set<String> covered = chromosome.getQuestions().stream()
                .flatMap(q -> q.getKnowledgeTreeList().stream().map(QhKnowledgeTree::getId))
                .collect(Collectors.toSet());
        chromosome.setCoverageScore((double) covered.size() / requiredKnowledge.size());

        // 3. 新增：题型数量匹配得分
        double typeMatchScore = calculateTypeMatchScore(chromosome.getQuestions(), config);
        chromosome.setTypeMatchScore(typeMatchScore);

        // 综合适应度（调整权重）
        chromosome.setFitness(
                config.getDifficultyWeight() * chromosome.getDifficultyScore() +
                        config.getCoverageWeight() * chromosome.getCoverageScore() +
                        config.getTypeMatchWeight() * chromosome.getTypeMatchScore()
        );
    }


    // 计算题型匹配得分（0-1，完全匹配为1）
    private double calculateTypeMatchScore(List<QhQuestionBank> questions, QhGeneratePaperDTO config) {
        // 统计实际各题型数量
        Map<String, Long> actualTypeCounts = questions.stream()
                .collect(Collectors.groupingBy(QhQuestionBank::getQuestionType, Collectors.counting()));

        // 合并单配置和多配置的题型需求
        Map<String, Integer> requiredTypeCounts = new HashMap<>();
        config.getQuestionDTOList().forEach(dto ->
                requiredTypeCounts.put(dto.getQuestionType(), dto.getRequiredNumber())
        );
        config.getQuestionDuoDTOList().forEach(duoDTO ->
                requiredTypeCounts.put(duoDTO.getQuestionType(), duoDTO.getRequiredNumber())
        );

        // 计算匹配度
        double score = 0;
        for (Map.Entry<String, Integer> entry : requiredTypeCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();
            long actual = actualTypeCounts.getOrDefault(type, 0L);
            score += (actual >= required) ? 1.0 : (double) actual / required;
        }
        return score / requiredTypeCounts.size(); // 平均匹配度
    }

    public static List<PaperChromosome> selectByRoulette(List<PaperChromosome> population) {
        double totalFitness = population.stream().mapToDouble(PaperChromosome::getFitness).sum();
        List<PaperChromosome> selected = new ArrayList<>();
        for (int i = 0; i < population.size(); i++) {
            double rand = Math.random() * totalFitness;
            double sum = 0;
            for (PaperChromosome chrom : population) {
                sum += chrom.getFitness();
                if (sum >= rand) {
                    selected.add(chrom);
                    break;
                }
            }
        }
        return selected;
    }

    public static double calculateExpectedDifficulty(QhGeneratePaperDTO paperDTO) {
        // 根据配置计算平均期望难度
        return paperDTO.getQuestionDTOList().stream()
                .mapToDouble(dto -> Double.parseDouble(dto.getDifficulty()))
                .average()
                .orElse(5.0); // 默认中等难度
    }

    private void mutate(List<PaperChromosome> population, QhGeneratePaperDTO config, List<QhQuestionBank> candidateQuestions) {
        population.forEach(chromosome -> {
            if (random.get().nextDouble() < config.getMutationRate()) {
                int idx = random.get().nextInt(chromosome.getQuestions().size());
                QhQuestionBank oldQuestion = chromosome.getQuestions().get(idx);
                // 仅从相同题型的候选题目中选取
                List<QhQuestionBank> sameTypePool = candidateQuestions.stream()
                        .filter(q -> q.getQuestionType().equals(oldQuestion.getQuestionType()))
                        .collect(Collectors.toList());
                if (!sameTypePool.isEmpty()) {
                    QhQuestionBank newQuestion = sameTypePool.get(random.get().nextInt(sameTypePool.size()));
                    chromosome.getQuestions().set(idx, newQuestion);
                }
            }
        });
    }

    // 交叉操作完整实现（两点交叉）
    private List<PaperChromosome> crossover(List<PaperChromosome> population, QhGeneratePaperDTO config) {
        List<PaperChromosome> newPopulation = new ArrayList<>();
        for (int i = 0; i < population.size(); i += 2) {
            // 边界检查：避免越界
            if (i + 1 >= population.size()) break;

            PaperChromosome parent1 = population.get(i);
            PaperChromosome parent2 = population.get(i + 1);

            // 生成交叉点（确保 parent 非空）
            int size = parent1.getQuestions().size();
            int point1 = random.get().nextInt(size);
            int point2 = random.get().nextInt(size);
            if (point1 > point2) {
                int temp = point1;
                point1 = point2;
                point2 = temp;
            }

            // 生成子代染色体
            List<QhQuestionBank> child1Questions = new ArrayList<>();
            child1Questions.addAll(parent1.getQuestions().subList(0, point1));
            child1Questions.addAll(parent2.getQuestions().subList(point1, point2));
            child1Questions.addAll(parent1.getQuestions().subList(point2, size));

            List<QhQuestionBank> child2Questions = new ArrayList<>();
            child2Questions.addAll(parent2.getQuestions().subList(0, point1));
            child2Questions.addAll(parent1.getQuestions().subList(point1, point2));
            child2Questions.addAll(parent2.getQuestions().subList(point2, size));

            // 创建子代对象并计算适应度
            PaperChromosome child1 = new PaperChromosome(child1Questions, 0, 0, 0, 0);
            PaperChromosome child2 = new PaperChromosome(child2Questions, 0, 0, 0, 0);
            calculateFitness(child1, config);
            calculateFitness(child2, config);

            newPopulation.add(child1);
            newPopulation.add(child2);
        }
        return newPopulation;
    }
    // 从候选题目中随机选取一个题目
    private QhQuestionBank getRandomQuestion(List<QhQuestionBank> candidateQuestions) {
        if (CollectionUtils.isEmpty(candidateQuestions)) {
            throw new QhException("候选题目列表为空，无法进行变异操作");
        }
        return candidateQuestions.get(random.get().nextInt(candidateQuestions.size()));
    }

    // 获取所有配置中要求的知识点ID集合（去重）
    private Set<String> getAllRequiredKnowledgePoints(QhGeneratePaperDTO paperDTO) {
        Set<String> knowledgePoints = new HashSet<>();

        // 处理单配置DTO的知识点
        paperDTO.getQuestionDTOList().forEach(dto -> {
            if (!CollectionUtils.isEmpty(dto.getKnowledgePoints())) {
                knowledgePoints.addAll(dto.getKnowledgePoints());
            }
            if (!CollectionUtils.isEmpty(dto.getChapters())) {
                knowledgePoints.addAll(qhKnowledgeTreeMapper.getKnowledgeIdsByChapterIds(dto.getChapters()));
            }
        });

        paperDTO.getQuestionDuoDTOList().forEach(duoDTO -> {
            if (!CollectionUtils.isEmpty(duoDTO.getKnowledgePoints())) {
                knowledgePoints.addAll(duoDTO.getKnowledgePoints());
            }
            if (!CollectionUtils.isEmpty(duoDTO.getChapters())) {
                knowledgePoints.addAll(
                        qhKnowledgeTreeMapper.getKnowledgeIdsByChapterIds(duoDTO.getChapters())
                );
            }
        });

        return knowledgePoints;
    }

    // 根据单配置DTO筛选题目
    private List<QhQuestionBank> pickQuestionsByDTO(QhQuestionDTO dto) {
        QhQuestionBank query = new QhQuestionBank();
        query.setGradeId(dto.getGradeId());
        query.setYear(dto.getYear());
        query.setQuestionType(dto.getQuestionType());
        query.setDifficulty(dto.getDifficulty());
        query.setKnowledgeTreeIds(dto.getKnowledgePoints());
        return questionBankMapper.selectBankList(query);
    }

    // 根据多配置DuoDTO筛选题目
    private List<QhQuestionBank> pickQuestionsByDuoDTO(QhQuestionDuoDTO duoDTO) {
        QueryCondition query = new QueryCondition();
        query.setGrades(duoDTO.getGradeIds());
        query.setYears(duoDTO.getYears());
        query.setQuestionType(duoDTO.getQuestionType());
        query.setDifficulty(duoDTO.getDifficulty());
        query.setKnowledgeTreeIds(duoDTO.getKnowledgePoints());
        return questionBankMapper.selectByComplexCondition(query);
    }

    // 获取种群中适应度最高的染色体
    private PaperChromosome getBestChromosome(List<PaperChromosome> population) {
        if (CollectionUtils.isEmpty(population)) {
            throw new QhException("种群为空，无法选择最优染色体");
        }
        return population.stream()
                .max(Comparator.comparingDouble(PaperChromosome::getFitness)).orElseThrow(() -> new QhException("未找到有效染色体"));
    }

    private List<PaperChromosome> select(List<PaperChromosome> population) {
        List<PaperChromosome> selected = new ArrayList<>();
        double totalFitness = population.stream().mapToDouble(PaperChromosome::getFitness).sum();
        if (totalFitness <= 0) {
            return population;
        }

        // 轮盘赌选择
        for (int i = 0; i < population.size(); i++) {
            double rand = random.get().nextDouble() * totalFitness;
            double currentSum = 0;
            for (PaperChromosome chrom : population) {
                currentSum += chrom.getFitness();
                if (currentSum >= rand) {
                    selected.add(chrom);
                    break;
                }
            }
        }
        return selected;
    }
}
