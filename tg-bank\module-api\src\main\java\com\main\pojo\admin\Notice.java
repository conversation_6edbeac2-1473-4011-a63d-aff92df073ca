package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_notice")
public class Notice implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId
    private String oid;

    private String title;

    private String content;

    private String sentTime;

    private String fromWho;

    private String fromWhoName;

    private String type;


}
