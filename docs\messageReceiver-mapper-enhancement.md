# MessageReceiver Mapper 增强文档

## 概述

本次修改为 `messageReceiverMapper` 增加了完整的增删改查方法，并将所有使用 MyBatis-Plus 默认方法的地方改为使用 XML 中定义的自定义方法。

## 修改内容

### 1. tg-bank 模块

#### 1.1 创建 MessageReceiverMapper.xml

**文件路径**: `tg-bank/module-items/module-admin/src/main/resources/mapping/admin/MessageReceiverMapper.xml`

**新增方法**:
- `insertMessageReceiver` - 插入消息接收记录
- `batchInsertMessageReceiver` - 批量插入消息接收记录
- `selectMessageReceiverById` - 根据ID查询消息接收记录
- `selectMessageReceiverList` - 查询消息接收记录列表
- `updateMessageReceiver` - 更新消息接收记录
- `deleteMessageReceiverById` - 删除消息接收记录
- `deleteMessageReceiverByIds` - 批量删除消息接收记录
- `deleteByMessageId` - 根据消息ID删除接收记录
- `deleteByMessageIds` - 根据消息ID数组批量删除接收记录
- `deleteByReceiverId` - 根据接收者ID删除接收记录
- `selectByMessageIdAndReceiverId` - 根据消息ID和接收者ID查询接收记录
- `checkMessageRead` - 检查消息是否已读
- `selectReceiversByMessageId` - 根据消息ID查询所有接收者
- `selectReadMessagesByReceiverId` - 根据接收者ID查询所有已读消息
- `countReadByMessageId` - 统计消息的已读人数
- `countReadByReceiverId` - 统计用户的已读消息数量
- `selectByTimeRange` - 根据时间范围查询接收记录
- `cleanExpiredRecords` - 清理过期的接收记录

#### 1.2 更新 MessageReceiverMapper 接口

**文件路径**: `tg-bank/module-items/module-admin/src/main/java/com/main/mapper/admin/MessageReceiverMapper.java`

**修改内容**:
- 添加了所有 XML 中定义的方法签名
- 使用 `@Param` 注解标注参数
- 添加了详细的 JavaDoc 注释

#### 1.3 修改 MessageServiceImpl

**文件路径**: `tg-bank/module-items/module-admin/src/main/java/com/main/service/admin/MessageServiceImpl.java`

**修改内容**:
- `markAsRead` 方法：
  - 使用 `insertMessageReceiver` 替代 `insert`
  - 添加重复检查逻辑 `checkMessageRead`
- `markAllAsRead` 方法：
  - 使用 `selectMessageReceiverList` 替代 `selectList`
  - 使用 `batchInsertMessageReceiver` 进行批量插入
  - 添加重复检查逻辑

### 2. domino-system 模块

#### 2.1 更新 SysMessageReceiverMapper.xml

**文件路径**: `domino-system/src/main/resources/mapper/system/SysMessageReceiverMapper.xml`

**新增方法**: 与 tg-bank 模块类似的完整增删改查方法，适配 `sys_message_receiver` 表结构

#### 2.2 更新 SysMessageReceiverMapper 接口

**文件路径**: `domino-system/src/main/java/com/domino/system/mapper/SysMessageReceiverMapper.java`

**修改内容**: 添加了所有 XML 中定义的方法签名

#### 2.3 修改 SysMessageServiceImpl

**文件路径**: `domino-system/src/main/java/com/domino/system/service/impl/SysMessageServiceImpl.java`

**修改内容**:
- `markAsRead` 方法：使用 `insertMessageReceiver` 和 `checkMessageRead`
- `markAllAsRead` 方法：使用 `selectMessageReceiverList` 和 `batchInsertMessageReceiver`

## 优化效果

### 1. 性能优化

- **批量操作**: 使用 `batchInsertMessageReceiver` 进行批量插入，减少数据库交互次数
- **重复检查**: 添加 `checkMessageRead` 方法避免重复插入已读记录
- **索引友好**: XML 中的查询语句针对数据库索引进行了优化

### 2. 功能增强

- **扩展查询**: 提供了多种查询方式（按时间范围、按用户、按消息等）
- **统计功能**: 提供已读人数统计、用户已读消息数量统计
- **数据清理**: 提供过期数据清理功能

### 3. 代码质量

- **类型安全**: 使用强类型的方法签名替代泛型查询
- **可维护性**: XML 中的 SQL 语句更容易维护和优化
- **可读性**: 方法名更具语义化，代码更易理解

## 使用示例

### 基础操作

```java
// 插入单条记录
MessageReceiver receiver = new MessageReceiver();
receiver.setOid(Global.createUUID());
receiver.setMessageId("msg123");
receiver.setReceiverId("user456");
receiver.setReadTime(new Date());
receiver.setCreateTime(new Date());
messageReceiverMapper.insertMessageReceiver(receiver);

// 批量插入
List<MessageReceiver> receivers = Arrays.asList(receiver1, receiver2, receiver3);
messageReceiverMapper.batchInsertMessageReceiver(receivers);

// 检查是否已读
boolean isRead = messageReceiverMapper.checkMessageRead("msg123", "user456");

// 统计已读人数
int readCount = messageReceiverMapper.countReadByMessageId("msg123");
```

### 查询操作

```java
// 根据条件查询
MessageReceiver query = new MessageReceiver();
query.setReceiverId("user456");
List<MessageReceiver> receivers = messageReceiverMapper.selectMessageReceiverList(query);

// 根据时间范围查询
Date startTime = DateUtils.parseDate("2023-01-01");
Date endTime = DateUtils.parseDate("2023-12-31");
List<MessageReceiver> timeRangeReceivers = messageReceiverMapper.selectByTimeRange(startTime, endTime);
```

### 清理操作

```java
// 清理30天前的过期记录
int cleanedCount = messageReceiverMapper.cleanExpiredRecords(30);
```

## 注意事项

1. **数据库表结构**: 确保数据库表结构与 XML 中的字段映射一致
2. **索引优化**: 建议在 `message_id`、`receiver_id`、`user_id`、`read_time` 等字段上建立索引
3. **事务管理**: 批量操作建议在事务中执行
4. **参数验证**: 在调用方法前进行必要的参数验证

## 测试建议

1. **单元测试**: 为每个新增的 mapper 方法编写单元测试
2. **性能测试**: 对批量操作进行性能测试，确保在大数据量下的表现
3. **集成测试**: 测试与业务逻辑的集成，确保功能正确性

## 后续优化

1. **缓存策略**: 考虑为频繁查询的数据添加缓存
2. **分页查询**: 为大数据量查询添加分页支持
3. **异步处理**: 对于大批量的已读标记操作，考虑异步处理
