package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 菜单资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_res")
public class Res implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String oid;

    private String name;

    private String resUrl;

    private String parentId;

    private int isLeaf;

    private Integer sort;

    private String extra;

    private Integer isBtn = 0;

    private String frontName;

    private String icon;

    private int isStop;

    @TableField(exist = false)
    private List<Res> children;


}
