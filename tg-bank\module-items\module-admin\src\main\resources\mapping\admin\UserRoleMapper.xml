<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.admin.UserRoleMapper">

    <select id="getUserRoleList" resultType="com.main.pojo.admin.Role">
        select b.* from d_user_role a left join d_role b on a.role_oid = b.oid and b.is_stop=0
        where a.user_oid = #{oid} and b.oid is not null
    </select>
</mapper>
