package com.common;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

/**
 * 权限验证
 * 默认都验证，不验证的话请配置 ignore
 * <AUTHOR>
 * 2020/5/18 17:43
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MyAuthority {
	boolean ignore() default false;

	/**
	 * 对应resource表的auth字段
	 * @return
	 */
	String auth() default "";
	String[] auths() default {};
}
