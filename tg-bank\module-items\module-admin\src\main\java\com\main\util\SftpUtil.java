package com.main.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
 
/**
 * sftp工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SftpUtil {
    /**
     * 不存在此文件
     */
    private static final String NO_SUCH_FILE = "no such file";
 
    /**
     * 初始化客户端
     *
     * @return
     * @throws JSchException
     */
    public SftpObject initSftpClient(String hostname, String username, int port, String password) throws Exception {
        // 创建JSch对象
        JSch jsch = new JSch();
        try {
            // 根据用户名、主机ip、端口号获取一个Session对象
            Session session = jsch.getSession(username, hostname, port);
            // 设置密码
            session.setPassword(password);
 
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            // 为Session对象设置properties
            session.setConfig(config);
            // 设置超时
            session.setTimeout(1000 * 10);
            // 通过Session建立连接
            session.connect();
            // 打开SFTP通道
            Channel channel = session.openChannel("sftp");
            // 建立SFTP通道的连接
            channel.connect();
            ChannelSftp sftp = (ChannelSftp) channel;
            return new SftpObject(session, sftp);
        } catch (Exception e) {
            log.error("连接sftp异常", e);
        }
        return null;
    }
 
    /**
     * 上传文件
     *
     * @param channelSftp channelSftp
     * @param pathname    ftp服务保存地址
     * @param fileName    上传到ftp的文件名
     * @param inputStream 输入文件流
     * @return
     */
    public Map<String, String> uploadFile(ChannelSftp channelSftp, String pathname, String fileName, InputStream inputStream) {
        try {
            createDir(pathname, channelSftp);
            channelSftp.put(inputStream, fileName);
            Map<String, String> map = new HashMap<String, String>(16);
            map.put("fileName", fileName);
            map.put("ftpUrl", pathname + File.separator + fileName);
            log.info("sftp成功上传文件，文件路径：{}", map.get("ftpUrl"));
            return map;
        } catch (Exception e) {
            log.error("上传文件异常", e);
            closeSftp(channelSftp);
        }
 
        return null;
    }
 
    /**
     * sftp创建一个文件目录
     */
    public void createDir(String createPath, ChannelSftp sftp) {
        synchronized (NO_SUCH_FILE) {
            try {
                if (isDirExist(createPath, sftp)) {
                    sftp.cd(createPath);
                    return;
                }
                String[] pathArry = createPath.split("/");
                StringBuffer filePath = new StringBuffer("/");
                for (String path : pathArry) {
                    if ("".equals(path)) {
                        continue;
                    }
                    filePath.append(path + "/");
                    if (isDirExist(filePath.toString(), sftp)) {
                        sftp.cd(filePath.toString());
                    } else {
                        // 建立目录
                        sftp.mkdir(filePath.toString());
                        // 进入并设置为当前目录
                        sftp.cd(filePath.toString());
                    }
                }
                sftp.cd(createPath);
            } catch (SftpException e) {
                log.error("创建文件夹异常", e);
            }
        }
    }
 
    /**
     * sftp判断目录是否存在
     */
    public boolean isDirExist(String directory, ChannelSftp sftp) {
        boolean isExist = false;
        try {
            SftpATTRS sftpAttrs = sftp.lstat(directory);
            isExist = true;
            return sftpAttrs.isDir();
        } catch (Exception e) {
            if (NO_SUCH_FILE.equals(e.getMessage().toLowerCase())) {
                isExist = false;
            }
        }
        return isExist;
    }
 
    /**
     * sftp判断文件是否存在
     */
    public boolean isFileExist(String directory, ChannelSftp sftp) {
        boolean isExist = false;
        try {
            SftpATTRS sftpAttrs = sftp.lstat(directory);
            isExist = true;
            return isExist;
        } catch (Exception e) {
            if (NO_SUCH_FILE.equals(e.getMessage().toLowerCase())) {
                isExist = false;
            }
        }
        return isExist;
    }
 
    /**
     * 下载文件
     *
     * @param sftp         sftp
     * @param directory    下载目录(服务器文件所在的目录)
     * @param downloadFile 下载的文件
     * @param saveFile     存在本地的路径
     */
    public File download(ChannelSftp sftp, String directory, String downloadFile, String saveFile) {
        FileOutputStream fileOutputStream = null;
        try {
            sftp.cd(directory);
            File file = new File(saveFile);
            fileOutputStream = new FileOutputStream(file);
            sftp.get(downloadFile, fileOutputStream);
            fileOutputStream.close();
            return file;
        } catch (Exception e) {
            log.error("sftp下载文件异常", e);
            return null;
        } finally {
            if (null != fileOutputStream) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.error(e.toString(), e);
                }
            }
        }
    }
 
    /**
     * 下载文件
     *
     * @param sftp             sftp
     * @param downloadFilePath 下载的文件完整目录
     * @param sos         ServletOutputStream
     */
    public void download(ChannelSftp sftp, String downloadFilePath, ServletOutputStream sos) {
        try {
            int i = downloadFilePath.lastIndexOf('/');
            if (i == -1) {
                return ;
            }
 
            sftp.cd(downloadFilePath.substring(0, i));
            sftp.get(downloadFilePath.substring(i + 1), sos);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取文件列表
     *
     * @param dir 目录
     * @return 文件列表
     * @throws Exception 异常
     */
    public ArrayList<String> listFiles(ChannelSftp sftp, String dir, boolean isOnlyDirectory) throws Exception {
        ArrayList<String> files = new ArrayList<>();
        sftp.cd(dir);
        Vector<String> lss = sftp.ls("*");
 
        for (int i = 0; i < lss.size(); i++) {
            Object obj = lss.elementAt(i);
            if (obj instanceof ChannelSftp.LsEntry) {
                ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) obj;
                //如果不是只要求目录的话，文件也返回
                if (!isOnlyDirectory && !entry.getAttrs().isDir()) {
                    files.add(entry.getFilename());
                }
                //目录要过滤掉 . ..两个
                if (entry.getAttrs().isDir()) {
                    if (!".".equals(entry.getFilename()) && !"..".equals(entry.getFilename())) {
                        files.add(entry.getFilename());
                    }
                }
            }
        }
 
        return files;
    }

    public ArrayList<com.main.pojo.admin.File> listFiles(ChannelSftp sftp, String dir) throws Exception {
        ArrayList<com.main.pojo.admin.File> files = new ArrayList<>();
        sftp.cd(dir);
        Vector<String> lss = sftp.ls("*");

        for (int i = 0; i < lss.size(); i++) {
            Object obj = lss.elementAt(i);
            if (obj instanceof ChannelSftp.LsEntry) {
                ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) obj;
                com.main.pojo.admin.File file = new com.main.pojo.admin.File();
                long size = entry.getAttrs().getSize();
                String[] temp = entry.getLongname().split(" ");
                file.setOwner(temp[5]+"/"+temp[10]);
                Date date = new Date((long)entry.getAttrs().getMTime() * 1000L);
                file.setUptTime(DateUtil.format(date, "yyyy-MM-dd HH:mm"));
                file.setAuth(entry.getAttrs().getPermissionsString());
                //如果不是只要求目录的话，文件也返回
                if (!entry.getAttrs().isDir()) {
                    file.setName(entry.getFilename());
                    file.setType("1");
                    file.setSize(formatFileSize(size));
                    files.add(file);
                }
                //目录要过滤掉 . ..两个
                if (entry.getAttrs().isDir()) {

                    if (!".".equals(entry.getFilename()) && !"..".equals(entry.getFilename())) {
                        file.setName(entry.getFilename());
                        file.setType("0");
                        file.setSize("");
                        files.add(file);
                    }
                }

            }
        }

        return files;
    }
    public static String formatFileSize(long size) {
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.floor(Math.log(size) / Math.log(1024)));
        BigDecimal bigDecimal = new BigDecimal(size / Math.pow(1024, digitGroups));
        return bigDecimal.setScale(2, RoundingMode.HALF_UP) + " " + units[digitGroups];
    }
    /**
     * @param sftp             sftp连接
     * @param downloadFilePath 下载的文件完整路径
     * @return
     */
    public InputStream get(ChannelSftp sftp, String downloadFilePath) throws SftpException {
        int i = downloadFilePath.lastIndexOf('/');
        if (i == -1) {
            return null;
        }
 
        sftp.cd(downloadFilePath.substring(0, i));
        return sftp.get(downloadFilePath.substring(i + 1));
    }
 
    /**
     * 删除文件
     *
     * @param sftp     sftp
     * @param fileName 文件名
     * @throws Exception 异常
     */
    public void delete(ChannelSftp sftp, String fileName) throws Exception {
        sftp.rm(fileName);
    }
 
    /**
     * 关闭sftp
     *
     * @param sftpObject
     */
    public void closeSftp(SftpObject sftpObject) {
        if (sftpObject == null) {
            return;
        }
        ChannelSftp channelSftp = sftpObject.getChannelSftp();
        Session session = sftpObject.getSession();
        if (channelSftp != null) {
            if (channelSftp.isConnected()) {
                channelSftp.disconnect();
            }
        }
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }
 
    /**
     * 关闭sftp连接
     *
     * @param channel channel
     */
    public void closeSftp(ChannelSftp channel) {
        if (channel != null) {
            channel.disconnect();
            Session session = null;
            try {
                session = channel.getSession();
                if (session != null) {
                    session.disconnect();
                }
            } catch (JSchException e) {
                log.error("关闭sftp异常", e);
            }
        }
    }
 
    public static void main(String[] args) {
        SftpUtil sftpUtil = new SftpUtil();
        try {
            SftpObject sftpObject = sftpUtil.initSftpClient("121.36.52.230", "root", 6607, "Ecansoft@2023");
            List<String> fileList = sftpUtil.listFiles(sftpObject.getChannelSftp(), "/", true);
            fileList.forEach(data -> {
                log.info(data);
            });
            sftpUtil.closeSftp(sftpObject);
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}