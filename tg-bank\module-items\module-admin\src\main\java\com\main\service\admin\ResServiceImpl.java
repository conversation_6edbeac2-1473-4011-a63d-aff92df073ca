package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.mapper.admin.ResMapper;
import com.main.pojo.admin.Res;
import com.main.pojo.admin.User;
import com.main.util.BeanUtils;
import com.main.util.CUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 菜单资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
public class ResServiceImpl extends ServiceImpl<ResMapper, Res> implements ResService {
    @Resource
    private ResMapper resMapper;

    @Override
    public ResponseVO<Map<String, Object>> getTreeData(String oid, String enable, User user) {
        ResponseVO<Map<String, Object>> response = new ResponseVO<>();
        LambdaQueryWrapper<Res> queryWrapper = new LambdaQueryWrapper<>();
        if (user.getIsAdmin() == 0) {
            if ("1".equals(enable)) {
                queryWrapper.eq(Res::getIsStop, 0);
            }
            queryWrapper.and(w -> w.inSql(Res::getOid, "select b.res_oid from d_user_role a left join d_role_res b on a.role_oid=b.role_oid\n" +
                    "where a.user_oid = '" + user.getOid() + "'").or().eq(Res::getIsLeaf, 0));
        } else if (user.getIsAdmin() == -1) {
            throw new BusinessException("普通用户没有查询资源权限！");
        }
        queryWrapper.orderByAsc(Res::getSort);
        List<Res> list = this.list(queryWrapper);

        Map<String, List<Res>> parentChildrenMap = new HashMap<>();
        Map<String, String> idNameMaps = new HashMap<>();
        list.forEach(obj -> {
            String parentId = obj.getParentId();
            if (CUtil.isEmpty(parentId)) {
                parentId = "0";
            }
            List<Res> children = parentChildrenMap.get(parentId);
            if (children == null) children = new ArrayList<>();
            children.add(obj);
            parentChildrenMap.put(parentId, children);
            idNameMaps.put(obj.getOid(), obj.getName());
        });
        List<Res> topObjList = parentChildrenMap.get("0");
        List<Map<String, Object>> treeData = new ArrayList<>();
        if (topObjList != null && topObjList.size() > 0) {
            handlerChildren(parentChildrenMap, treeData, topObjList, idNameMaps);
        }

        //获取当前用户已经选择的
        JSONArray dVal = new JSONArray();
        Map<String, Object> retDataMap = new HashMap<>();
        retDataMap.put("data", treeData);
        retDataMap.put("dVal", dVal);

        response.setData(retDataMap);
        return response;
    }

    private List<Map<String, Object>> getTreeChildrenData(Map<String, List<Res>> parentChildrenMap, String
            oid, Map<String, String> idNameMaps) {
        List<Map<String, Object>> childrenTreeNode = new ArrayList<>();
        List<Res> children = parentChildrenMap.get(oid);
        handlerChildren(parentChildrenMap, childrenTreeNode, children, idNameMaps);
        return childrenTreeNode;
    }

    private void handlerChildren
            (Map<String, List<Res>> parentChildrenMap, List<Map<String, Object>> childrenTreeNode, List<Res> children, Map<String, String> idNameMaps) {
        if (children != null && children.size() > 0) {
            children.forEach(obj -> {
                Map<String, Object> treeNode = BeanUtils.autoBeanToMap(obj);
                treeNode.put("label", obj.getName());
                treeNode.put("value", obj.getOid());
                treeNode.put("parentName", idNameMaps.get(obj.getParentId()));
                if (CUtil.isEmpty(treeNode.get("parentName"))) {
                    treeNode.put("parentName", "一级菜单");
                }

                if (obj.getIsLeaf() == 0) {
                    List<Map<String, Object>> childrenTreeData = getTreeChildrenData(parentChildrenMap, obj.getOid(), idNameMaps);
                    treeNode.put("children", childrenTreeData);
                } else {
                    treeNode.put("children", null);
                }
                childrenTreeNode.add(treeNode);
            });
        }
    }

    @Override
    public ResponseVO<String> save(JSONObject obj) {
        ResponseVO<String> responseVO = new ResponseVO<>();
        Res bean = obj.toJavaObject(Res.class);
        if (CUtil.isEmpty(bean.getOid())) {
            //ADD
            String oid = this.getId(bean.getParentId());
            bean.setOid(oid);
            bean.setIsLeaf(1);
            this.save(bean);
            //修改父亲为非明细节点
            if (CUtil.isNotEmpty(bean.getParentId())) {
                this.updateLeaf(bean.getParentId(), 0);
            }
        } else {
            //UPDATE
            this.updateById(bean);
        }

        return responseVO;
    }


    @Override
    public boolean updateLeaf(String oid, int isLeaf) {
        LambdaUpdateWrapper<Res> update = new LambdaUpdateWrapper<>();
        update.set(Res::getIsLeaf, isLeaf);
        update.eq(Res::getOid, oid);
        return this.update(update);
    }

    @Override
    public String getId(String parentId) {
        String oid = resMapper.getAutoGeneralID(parentId);
        return CUtil.handlerId(oid, parentId);
    }

    @Override
    public ResponseVO<String> delete(String oid, String parentId) {
        ResponseVO<String> response = new ResponseVO<>();
        //删除他和他的子节点
        LambdaQueryWrapper<Res> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(Res::getOid, oid);
        this.remove(queryWrapper);
        //判断他的老父亲是不是只有他这个儿子，如果是，就给他老父亲叶子标记为1
        if (CUtil.isNotEmpty(parentId)) {
            LambdaQueryWrapper<Res> queryParentWrapper = new LambdaQueryWrapper<>();
            queryParentWrapper.eq(Res::getParentId, CUtil.nullToStr(parentId));
            List<Res> childrens = this.list(queryParentWrapper);
            if (childrens == null || childrens.size() == 0) {
                this.updateLeaf(parentId, 1);
            }
        }
        return response;
    }
}
