package com.main.service.his;

import com.main.bean.vo.ResponseVO;
import com.main.pojo.his.dto.PatientDTO;
import com.main.pojo.admin.User;
import java.util.Map;

public interface PatientService {
    ResponseVO<String> createPatient(PatientDTO patientDTO, User user);
    ResponseVO<String> updatePatient(String id, PatientDTO patientDTO, User user);
    ResponseVO<String> deletePatient(String id, User user);
    ResponseVO<Map<String, Object>> pagePatients(Integer pageNum, Integer pageSize, String keyWord, String typeKey, User user);
    ResponseVO<PatientDTO> getById(String id, User user);
    void updateVIP(String patientId, int vip);
}