package com.main.bean.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * View Object of Request Param
 *
 * <AUTHOR>
 * @date 2019/5/16 15:05
 */
@Data
public class RequestVO<T> implements Serializable {
	private static final long serialVersionUID = -7602478003003767252L;

	private String appId; //应用ID
	private String appSecret;//应用密钥
	private String method;//访问方法
	private String platform;//访问平台
	private String signType;//签名类型
	private String sign;//签名串
	private String encryptType;//加密类型
	private String timestamp;//时间戳
	private String version;//访问方法版本

	private String bizContent; //加密字符串
	private T bizObj;//解密对象


	public String getVersion() {
		//获取支付配置相关信息
		String[] serviceId = this.getMethod().split("/");
		if (serviceId.length != 0) {
			return serviceId[serviceId.length - 1];
		} else {
			return version;
		}
	}


}
