package com.main.util;

import com.config.websocket.WebSocketHandler;
import com.constant.DPConstant;
import com.jcraft.jsch.*;
import com.main.pojo.admin.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 远程执行 shell
 */

@Component
@Slf4j
public class ExecuteShellUtil {

    public WebSocketHandler webSocketServer;
    private static final int TIME_OUT = 10 * 60 * 1000; //设置超时为10分钟

    /**
     * 未调用初始化方法 错误提示信息
     */
    private static final String DONOT_INIT_ERROR_MSG = "please invoke init(...) first!";

    private Session session;

    private Channel channel;

    private ChannelExec channelExec;

    private ExecuteShellUtil() {
    }

    /**
     * 获取ExecuteShellUtil类实例对象
     *
     * @return 实例
     * @date 2019/4/29 16:58
     */
    public static ExecuteShellUtil getInstance() {
        return new ExecuteShellUtil();
    }

    public static ExecuteShellUtil getInstance(WebSocketHandler webSocketServer) {
        ExecuteShellUtil executeShellUtil = new ExecuteShellUtil();
        executeShellUtil.webSocketServer = webSocketServer;
        return executeShellUtil;
    }

    /**
     * 初始化
     *
     * @throws JSchException JSch异常
     */
    public void init(SshHost sshInfo) throws JSchException {
        JSch jsch = new JSch();
        session = jsch.getSession(sshInfo.getUser(), sshInfo.ip, sshInfo.getPort());
        session.setPassword(sshInfo.getPwd());
        Properties sshConfig = new Properties();
        sshConfig.put("StrictHostKeyChecking", "no");
        session.setConfig(sshConfig);
        session.connect(60 * 1000);
        session.setTimeout(TIME_OUT);
        log.info("Session connected!");
        // 打开执行shell指令的通道
        channel = session.openChannel("exec");
        channelExec = (ChannelExec) channel;
    }

    /**
     * 执行一条命令
     */
    public String execCmd(String command) throws Exception {
        if (session == null || channel == null || channelExec == null) {
            throw new Exception(DONOT_INIT_ERROR_MSG);
        }
        log.info("execCmd command - > {}", command);
        channelExec.setCommand(command);
        channel.setInputStream(null);
        channelExec.setErrStream(System.err);
        channel.connect();
        StringBuilder sb = new StringBuilder(16);
        try (InputStream in = channelExec.getInputStream();
             InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8);
             BufferedReader reader = new BufferedReader(isr)) {
            String buffer;
            while ((buffer = reader.readLine()) != null) {
                System.out.println(buffer);
                sb.append("\n").append(buffer);
            }
            // 释放资源
            close();
            log.info("execCmd result - > {}", sb);
            return sb.toString();
        }
    }

    /**
     * 执行一条命令
     */
    public void execCmdAndSendWebSocket(String taskId, String command) throws Exception {
        if (session == null || channel == null || channelExec == null) {
            throw new Exception(DONOT_INIT_ERROR_MSG);
        }
        log.info("execCmd command - > {}", command);
        channelExec.setCommand(command);
        channel.setInputStream(null);
        channelExec.setErrStream(System.err);
        channel.connect();

        try (InputStream in = channelExec.getInputStream();
             InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8);
             BufferedReader reader = new BufferedReader(isr)) {
            String buffer;
            while ((buffer = reader.readLine()) != null) {
                // 发送websocket信息
                sendMessage(taskId, buffer);
            }
            // 释放资源
            close();
        }
    }


    /**
     * 释放资源
     *
     * @date 2019/3/15 12:47
     */
    public void close() {
        if (channelExec != null && channelExec.isConnected()) {
            channelExec.disconnect();
        }
        if (channel != null && channel.isConnected()) {
            channel.disconnect();
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }


    /**
     * 连接远程机器执行 shell
     *
     * @param cmd
     * @return
     * @throws Exception
     */
    public static String executeShell(SshHost sshHost, String cmd) throws Exception {
        ExecuteShellUtil instance = ExecuteShellUtil.getInstance();
        instance.init(sshHost);
        return instance.execCmd(cmd);
    }

    public static void executeShellAndSendWebSocket(String taskId, WebSocketHandler webSocketServer,SshHost sshHost, String cmd) throws Exception {
        ExecuteShellUtil instance = ExecuteShellUtil.getInstance(webSocketServer);
        instance.init(sshHost);
        instance.execCmdAndSendWebSocket(taskId, cmd);
    }

    public void sendMessage(String token, String msg) {
        if (webSocketServer != null) {
            webSocketServer.sendMessage(token, Message.getOne(DPConstant.WS_MSG_TYPE_SHELL,msg + "\r\n"));
        } else {
            log.warn("客户端已退出");
        }
    }


//    public static void main(String[] args) throws Exception {
//        ExecuteShellUtil instance = ExecuteShellUtil.getInstance();
//        instance.init("*************", 22, "root", "mima");
//        String result = instance.execCmd("cd /home/<USER>/ybs; ls");
//        instance.close();
//        System.out.println(result);
//    }

}


