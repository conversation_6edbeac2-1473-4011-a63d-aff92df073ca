package com.main.handler.admin;

import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 404 跳转到自定义界面
 */
@Controller
public class RedirectUnknownUrls implements ErrorController {
    @GetMapping("/error")
    public String redirectNonExistentUrlsToErrorHtml(Model model, String errorMessage)  {
        model.addAttribute("error",errorMessage);
        return "/error/404";
    }
    @GetMapping("/main/error")
    public String mainRedirectNonExistentUrlsToErrorHtml(Model model, String errorMessage)  {
        model.addAttribute("error",errorMessage);
        return "/error/404";
    }
}
