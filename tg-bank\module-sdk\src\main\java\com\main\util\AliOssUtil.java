//package com.main.util;
//
//import com.aliyun.oss.*;
//import com.aliyun.oss.common.auth.CredentialsProvider;
//import com.aliyun.oss.common.auth.DefaultCredentialProvider;
//import com.aliyun.oss.common.comm.SignVersion;
//import com.aliyun.oss.model.*;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.*;
//
///**
//* @description: 阿里云OSS工具类
//* <AUTHOR>
//* @date 2024/12/31 11:03
//* @version 1.0
//*/
//public class AliOssUtil {
//
//    private static Logger logger = LoggerFactory.getLogger(AliOssUtil.class);
//
//    private static final String accessKeyId = "1D2qh57726GwwDGy";
//    private static final String accessKeySecret = "4KaTOKmaZ9E7kzViUXeWxxLrUUfkKG";
//    private static final String bucketName = "mzt-exam-appoint";
//    private static final String endpoint = "*************:7002"; https://file.minzhentong.com.cn/oss;
//
//
//    private static void createBucket() throws Exception {
//        //根据入参代码直接初始化   （测试用  生产改为环境变量模式）
//        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
//
//        // 创建OSSClient实例。
//        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
//        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
//        // 创建OSSClient实例。
//        OSS ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);
//
//        try {
//            // 创建CreateBucketRequest对象。
//            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
//
//            // 如果创建存储空间的同时需要指定存储类型、存储空间的读写权限、数据容灾类型, 请参考如下代码。
//            // 此处以设置存储空间的存储类型为标准存储为例介绍。
//            //createBucketRequest.setStorageClass(StorageClass.Standard);
//            // 数据容灾类型默认为本地冗余存储，即DataRedundancyType.LRS。如果需要设置数据容灾类型为同城冗余存储，请设置为DataRedundancyType.ZRS。
//            //createBucketRequest.setDataRedundancyType(DataRedundancyType.ZRS);
//            // 设置存储空间读写权限为公共读，默认为私有。
//            createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
//
//            // 在支持资源组的地域创建Bucket时，您可以为Bucket配置资源组。
//            //createBucketRequest.setResourceGroupId(rsId);
//
//            // 创建存储空间。
//            ossClient.createBucket(createBucketRequest);
//        } catch (OSSException oe) {
//            logger.info("阿里云OSS存储桶创建异常：{}", oe.getMessage());
//        } catch (ClientException ce) {
//            logger.info("阿里云OSS存储桶创建异常：{}", ce.getMessage());
//        } finally {
//            if (ossClient != null) {
//                ossClient.shutdown();
//            }
//        }
//    }
//
//
//    public static String upload(String filePath, String fileName) throws Exception{
//
//        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
//        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
//        String objectName = fileName;
//
//        // 创建OSSClient实例。
//        OSS ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);
//
//        try {
//            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(convertFileToByteArray(filePath)));
//
//            StringBuilder fileUrl = new StringBuilder();
//            fileUrl
//                    //.append(endpoint)
//                    .append("/")
//                    .append(bucketName)
//                    .append("/")
//                    .append(objectName);
//            return fileUrl.toString();
//        } catch (OSSException oe) {
//            logger.info("阿里云OSS文件上传异常：{}", oe.getMessage());
//        } catch (ClientException ce) {
//            logger.info("阿里云OSS文件上传异常：{}", ce.getMessage());
//        } finally {
//            if (ossClient != null) {
//                ossClient.shutdown();
//            }
//        }
//        return null;
//    }
//
//    public static byte[] convertFileToByteArray(String filePath) {
//        File file = new File(filePath);
//        byte[] byteArray = null;
//        try (FileInputStream fis = new FileInputStream(file)) {
//            // 获取文件大小
//            int length = (int) file.length();
//            byteArray = new byte[length];
//
//            // 读取文件内容到字节数组
//            int bytesRead = fis.read(byteArray);
//            if (bytesRead != length) {
//                throw new IOException("文件解析异常");
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return byteArray;
//    }
//
//
//
//    public void delete(String objectName) throws Exception {
//
//        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
//        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
//
//        // 创建OSSClient实例。
//        OSS ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);
//
//        try {
//            // 删除文件。
//            ossClient.deleteObject(bucketName, objectName);
//        } catch (OSSException oe) {
//            logger.info("阿里云OSS文件删除异常：{}", oe.getMessage());
//        } catch (ClientException ce) {
//            logger.info("阿里云OSS文件删除异常：{}", ce.getMessage());
//        } finally {
//            if (ossClient != null) {
//                ossClient.shutdown();
//            }
//        }
//    }

//import java.io.BufferedInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.HttpURLConnection;
//import java.net.URL; /**
// * 从指定的网络文件URL读取内容并转换为byte数组。
// * @param fileUrl 网络文件的URL
// * @return 文件的byte数组
// * @throws java.io.IOException 如果读取过程中发生IO异常
// */
//public static byte[] readFileToByteArray(String fileUrl) throws IOException {
//    // 创建URL对象
//    URL url = new URL(fileUrl);
//
//    // 打开连接
//    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//    connection.setRequestMethod("GET");
//
//    // 检查响应码
//    int responseCode = connection.getResponseCode();
//    if (responseCode != HttpURLConnection.HTTP_OK) {
//        throw new IOException("文件读取异常，返回代码：" + responseCode);
//    }
//
//    try (InputStream inputStream = new BufferedInputStream(connection.getInputStream());
//         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
//
//        // 读取数据并写入ByteArrayOutputStream
//        byte[] buffer = new byte[4096];
//        int bytesRead;
//        while ((bytesRead = inputStream.read(buffer)) != -1) {
//            byteArrayOutputStream.write(buffer, 0, bytesRead);
//        }
//
//        // 将ByteArrayOutputStream转换为byte数组
//        return byteArrayOutputStream.toByteArray();
//    }
//}
//
//}