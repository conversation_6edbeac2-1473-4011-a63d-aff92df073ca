-- 聊天功能相关表结构

-- 消息表
CREATE TABLE `sys_message` (
  `message_id` varchar(64) NOT NULL COMMENT '主键ID',
  `title` varchar(255) DEFAULT NULL COMMENT '消息标题',
  `content` text COMMENT '消息内容',
  `type` varchar(20) NOT NULL COMMENT '消息类型(SYSTEM-系统消息,PERSONAL-公告通知,CHAT-聊天消息)',
  `sender_id` varchar(64) NOT NULL COMMENT '发送人ID',
  `sender_name` varchar(100) DEFAULT NULL COMMENT '发送人姓名',
  `org_id` varchar(64) DEFAULT NULL COMMENT '发送人所属机构ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  `receive_scope` varchar(20) DEFAULT NULL COMMENT '接收范围(ALL-全部人员, ORG-机构, CURRORG-当前登录机构,USER-指定用户)',
  `receive_target` varchar(500) DEFAULT NULL COMMENT '接收对象ID(机构ID/用户ID列表)',
  `sender_chat_sex` tinyint(1) DEFAULT NULL COMMENT '发送者性别',
  `sender_chat_phone` varchar(20) DEFAULT NULL COMMENT '发送者手机号',
  `receive_target_chat_sex` tinyint(1) DEFAULT NULL COMMENT '接收者性别',
  `receive_target_chat_phone` varchar(20) DEFAULT NULL COMMENT '接收者手机号',
  `is_image` tinyint(1) DEFAULT '0' COMMENT '是否是图片(0-否,1-是)',
  `quote_oid` varchar(64) DEFAULT NULL COMMENT '引用消息ID',
  `quote_content` text COMMENT '引用内容',
  `quote_timestamp` datetime DEFAULT NULL COMMENT '引用时间',
  `chat_read_status` tinyint(1) DEFAULT '0' COMMENT '聊天内容是否已读(0-未读,1-已读)',
  PRIMARY KEY (`message_id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_receive_target` (`receive_target`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';

-- 消息接收记录表
CREATE TABLE `sys_message_receiver` (
  `receiver_id` varchar(64) NOT NULL COMMENT '主键ID',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `user_id` varchar(64) NOT NULL COMMENT '接收人ID',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`receiver_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息接收记录表';

-- 为sys_user表添加聊天相关字段
ALTER TABLE `sys_user` ADD COLUMN `online` varchar(1) DEFAULT '0' COMMENT '用户是否在线(0-离线,1-在线)';

-- 创建索引
CREATE INDEX `idx_sys_user_online` ON `sys_user` (`online`);

-- 插入测试数据（可选）
-- INSERT INTO `sys_message` (`message_id`, `title`, `content`, `type`, `sender_id`, `sender_name`, `org_id`, `receive_scope`, `receive_target`) 
-- VALUES ('test001', '系统通知', '这是一条系统通知消息', 'SYSTEM', '1', '系统管理员', '100', 'ALL', NULL);
