package com.main.handler.admin;

import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.bean.vo.ResponseVO;
import com.main.handler.Api;
import com.main.pojo.admin.File;
import com.main.pojo.admin.User;
import com.main.service.admin.FileService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/file")
@Slf4j
public class FileHandler {

    @Resource
    private FileService fileService;

    @MyAuthority(auth = "/oth/fileList")
    @PostMapping("/tree")
    public ResponseVO<Map<String, Object>> getTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        String serverOid = String.valueOf(params.get("serverOid"));
        Assert.assertNotNull("服务器OID不能为空！", serverOid);
        return fileService.getTreeData(user, serverOid, String.valueOf(params.get("path")));
    }

    @MyAuthority(auth = "/oth/fileList")
    @RequestMapping("/list")
    @ResponseBody
    public ResponseVO<Map<String, Object>> list(@RequestBody JSONObject params, @Login User user) {
        Assert.assertNotNull("服务器OID不能为空！", params.getString("serverOid"));
        List<File> serviceList = fileService.getFileList(params, user);
        return Api.packageList(serviceList);
    }

    @MyAuthority(auth = "/oth/fileList")
    @GetMapping(value = "/download")
    public void batchDownLoad(
            HttpServletResponse response,
            @RequestParam(value = "filePath") String filePath,
            @RequestParam(value = "serverOid") String serverOid,
            @Login User user) {
        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        if (fileName.length() > 0) {
            fileService.downloadFile(response, filePath, fileName, serverOid, user);
        }
    }

    @MyAuthority(auth = "/oth/fileList")
    @PostMapping(value = "/upload")
    @ResponseBody
    public ResponseVO<String> fileUpload(@RequestParam("file") MultipartFile file,
                                         @RequestParam(value = "fileUrl") String fileUrl,
                                         @RequestParam(value = "serverOid") String serverOid,
                                         @Login User user) {
        return fileService.uploadFile(file, fileUrl, serverOid, user);
    }

    @MyAuthority(auth = "/oth/fileList")
    @PostMapping(value = "/del")
    @ResponseBody
    public ResponseVO<String> fileDel(@RequestBody JSONObject params, @Login User user) {
        return fileService.delFile(params.getString("filePath"), params.getString("serverOid"), user);
    }

}
