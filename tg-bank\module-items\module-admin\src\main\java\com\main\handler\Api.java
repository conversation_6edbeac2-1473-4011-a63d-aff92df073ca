package com.main.handler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.main.myenum.EncryptType;
import com.main.myenum.SignType;
import com.main.bean.vo.RequestVO;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.main.pojo.admin.ApplicationInfo;
import com.main.service.inf.ApplicationService;
import com.main.service.inf.ReflectionService;
import com.main.util.*;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API handler
 *
 * <AUTHOR>
 * 2019/5/16 14:25
 */
@Controller
@RequestMapping("/api")
public class Api {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@DubboReference(version = "1.0.0",group = "module-admin")
	private ReflectionService itemReflectionServiceImpl;
	@Resource
	private ApplicationService applicationService;

	public static ResponseVO<Map<String,Object>> packageList(List<?> obj) {
		ResponseVO<Map<String,Object>> response = new ResponseVO<>();
		Map<String, Object> data = new HashMap<>();
		if(obj != null ) {
			data.put("list", obj);
		}else {
			data.put("list", new ArrayList<>());
		}
		response.setData(data);
		return response;
	}
    public static ResponseVO<Map<String,Object>> packageTable(IPage obj) {
		ResponseVO<Map<String,Object>> response = new ResponseVO<>();
		Map<String, Object> data = new HashMap<String, Object>();
		if(obj != null ) {
			data.put("total", obj.getTotal());
			data.put("list", obj.getRecords());
		}else {
			data.put("total", 0);
			data.put("list", new ArrayList<>());
		}
		response.setData(data);
		return response;
    }

    /**
	 * interface of all request about pay
	 *
	 * @param request  request
	 * @param response response
	 */
	@PostMapping("/gateway.do")
	public void pay(HttpServletRequest request, HttpServletResponse response) {
		long start = System.currentTimeMillis();
		InputStream inputStream = null;
		InputStreamReader inputStreamReader = null;
		ResponseVO<Object> responseVO = new ResponseVO<>();
		RequestVO<Object> requestVO = new RequestVO<>();
		try {
			inputStream = request.getInputStream();
			inputStreamReader = new InputStreamReader(inputStream, Global.ENCODING);
			String requestMessage = StreamUtil.readInputStream(inputStreamReader);
			logger.info("==|----------------------请求处理阶段-------------------------------|==");
			logger.info("AIO API 统一入口请求开始，请求报文为：{}", requestMessage);
			requestVO = validRequestMssage(requestVO, requestMessage);

			//reflection call
			if (!CUtil.isEmpty(requestVO.getMethod())) {
				// 完成代理
				logger.info("开始调用反射服务 反射服务，入参：{}", JSON.toJSONString(requestVO));
				responseVO = itemReflectionServiceImpl.doHandler(requestVO);
				logger.info("结束调用反射服务 反射服务 返回报文：{}", JSON.toJSONString(responseVO));

			} else {
				// 方法为空需要返回错误
				responseVO.setCode(HandlerType.REQ_METHOD_EMPTY.getRetCode());
				responseVO.setMsg(HandlerType.REQ_METHOD_EMPTY.getRetMsg());
			}

		} catch (Exception e) {
			if (e instanceof BusinessException) {
				responseVO.setCode(((BusinessException) e).getErrorCode());
				responseVO.setMsg(((BusinessException) e).getErrorMessage());
			}else {
				responseVO.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
				responseVO.setMsg(HandlerType.SYSTEM_ERROR.getRetMsg() + ":" + e.getMessage());
			}
		} finally {
			//返回消息
			try {
				if (inputStreamReader != null) {
					inputStreamReader.close();
				}
				if (inputStream != null) {
					inputStream.close();
				}
				logger.info("==|----------------------返回结果报文阶段-------------------------------|==");
				response.setContentType("application/json");
				PrintWriter out = response.getWriter();
				// 创建响应报文
				responseVO.setSignType(requestVO.getSignType());
				responseVO.setEncryptType(requestVO.getEncryptType());
				responseVO.setTimestamp(DateUtil.getCurrentDateTime());
				String sign = Signature.createSign(responseVO, requestVO.getAppId(), requestVO.getAppSecret());
				responseVO.setSign(sign);
				String bizContent = JSONObject.toJSONString(responseVO.getBizObj());

				long end = System.currentTimeMillis();
				long callTimeConsume = end - start;
				if(!requestVO.getEncryptType().equals(EncryptType.Plain.toString())) {
					// 加密报文
					logger.info("返回报文加密前：{}", JSONObject.toJSONString(responseVO));
					String encryptData = SecurityUtil.encrypt(bizContent, requestVO.getEncryptType(), requestVO.getAppSecret(), requestVO.getAppId());
					responseVO.setBizContent(encryptData);
					// 清空明文
					if (CUtil.isNotEmpty(requestVO.getEncryptType())) {
						responseVO.setBizObj(null);
					}
				}
				String responseMsg = JSON.toJSONString(responseVO);
				logger.info("接口耗时:{}毫秒,返回报文加密后：{}", callTimeConsume, CUtil.formateJSON(responseMsg));
				out.write(responseMsg);
				out.close();
			} catch (Exception e1) {
				logger.error("构造响应消息出现异常，异常信息：" + e1.getMessage(), e1);
			}
		}
	}

	/**
	 * CHECK POST TEXT PARAM
	 *
	 * @param requestVO      object
	 * @param requestMessage param string
	 */
	@SuppressWarnings("unchecked")
	private RequestVO<Object> validRequestMssage(RequestVO requestVO, String requestMessage) throws BusinessException {
		//valiad post text is null
		if (CUtil.isEmpty(requestMessage)) {
			throw new BusinessException(HandlerType.REQ_POST_TEXT_NULL.getRetCode(), HandlerType.REQ_POST_TEXT_NULL.getRetMsg());
		}
		//valiad post is illegal
		requestVO = JSON.parseObject(requestMessage, RequestVO.class);
		if (requestVO == null) {
			throw new BusinessException(HandlerType.REQ_POST_TEXT_ILLEGAL.getRetCode(), HandlerType.REQ_POST_TEXT_ILLEGAL.getRetMsg());
		}
		//valiad timestamp
		if (CUtil.isEmpty(requestVO.getTimestamp())) {
			throw new BusinessException(HandlerType.REQ_TIMESTAMP_EMPTY.getRetCode(), HandlerType.REQ_TIMESTAMP_EMPTY.getRetMsg());
		}
		// get appId、appSecret and check
		String appId = requestVO.getAppId();
		if (CUtil.isEmpty(appId)) {
			throw new BusinessException(HandlerType.REQ_APP_ID_EMPTY.getRetCode(), HandlerType.REQ_APP_ID_EMPTY.getRetMsg());
		}
		// GET APPINFORMATION FROM SRVPAY SERVICE
		ApplicationInfo app = applicationService.getApplication(appId);
		if (app == null) {
			throw new BusinessException(HandlerType.REQ_APP_ID_ILLEGAL.getRetCode(), HandlerType.REQ_APP_ID_ILLEGAL.getRetMsg());
		}
		String appSecret = app.getAppSecret();
		// RESET APPSECRET
		requestVO.setAppSecret(appSecret);

		try {
			String encryptData = requestVO.getBizContent();
			String decryptData;
			if (!(EncryptType.Plain.toString().equals(requestVO.getEncryptType()))) {
				logger.info("请求密文:{}", encryptData);
				decryptData = SecurityUtil.decrypt(encryptData, requestVO.getEncryptType(), appSecret, appId);
				logger.info("请求明文:{}", decryptData);
			} else {
				decryptData = encryptData;
				logger.info("----报文不需要解密 -----");
			}
			requestVO.setBizObj(JSON.parseObject(decryptData, Object.class));

		} catch (Exception e) {
			logger.error("解密失败:", e);
			throw new BusinessException(HandlerType.REQ_DECRYPT_FAIL.getRetCode(), HandlerType.REQ_DECRYPT_FAIL.getRetMsg() + ":" + e.getMessage());
		}

		if(!requestVO.getSignType().equals(SignType.Plain.toString())) {

			String signOld = requestVO.getSign();//

			//去除不要参与签名的参数
			requestVO.setSign(null);
			requestVO.setBizContent(null);
			requestVO.setAppSecret(null);

			String signNew = Signature.createSign(requestVO, appSecret);
			boolean isVerify = signOld.equals(signNew);
			logger.info("验证签名结果：{}，报文的签名值：{}，服务端的签名值：{}", isVerify, signOld, signNew);
			if (!isVerify) {
				throw new BusinessException(HandlerType.REQ_CHECK_SIGN_FAIL.getRetCode(), HandlerType.REQ_CHECK_SIGN_FAIL.getRetMsg());
			}
			requestVO.setSign(signNew);
		}
		requestVO.setAppSecret(appSecret);
		return requestVO;
	}
}
