package com.main.bean.vo;

import com.main.bean.dto.HandlerStateDTO;
import com.main.myenum.HandlerType;
import lombok.Data;

import java.io.Serializable;

/**
 * View Object of Response
 *
 * <AUTHOR>
 */
@Data
public class ResponseVO<T> extends HandlerStateDTO<T> implements Serializable {
	private static final long serialVersionUID = -7602478003003767252L;

	private String signType;
	private String encryptType;
	private String timestamp;
	private String sign;
	//加解密明文对象
	private T bizObj;
	//加解密密文字符串
	private String bizContent;

	public ResponseVO() {
		//创建的时候默认是成功的
		super();
		this.setTimestamp(String.valueOf(System.currentTimeMillis()));
	}
	public ResponseVO(RequestVO requestVO) {
		this.setSign(requestVO.getSign());
		this.setEncryptType(requestVO.getEncryptType());
		this.setRetInfo(HandlerType.SUCCESS);
		this.setTimestamp(String.valueOf(System.currentTimeMillis()));
	}

	public ResponseVO(T obj) {
		//创建的时候默认是成功的
		super();
		this.setTimestamp(String.valueOf(System.currentTimeMillis()));
		this.setData(obj);
	}
}
