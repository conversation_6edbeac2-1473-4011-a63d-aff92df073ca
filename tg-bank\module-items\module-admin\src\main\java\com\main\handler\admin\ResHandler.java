package com.main.handler.admin;


import com.alibaba.fastjson.JSONObject;
import com.common.Login;
import com.common.MyAuthority;
import com.main.aop.LogAnnotation;
import com.main.bean.vo.ResponseVO;
import com.main.exception.BusinessException;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.Res;
import com.main.pojo.admin.User;
import com.main.service.admin.ResService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 菜单资源表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@RestController
@RequestMapping("/menu")
@Slf4j
public class ResHandler {

    @Resource
    private ResService resService;


    @PostMapping("/tree")
    public ResponseVO<Map<String,Object>> getTreeData(@RequestBody Map<String, Object> params, @Login User user) {
        Assert.assertNotNull("OID不能为空！",params.get("userOid"));
        return resService.getTreeData(String.valueOf(params.get("userOid")),String.valueOf(params.get("enable")), user);
    }

    @LogAnnotation(option = "保存菜单")
    //@MyAuthority(auth="/admin/menu/index")
    @PostMapping("/save")
    public ResponseVO<Boolean> save(@RequestBody List<Res> user) {
//        if(user.getIsAdmin() != 1) {
//            throw new BusinessException(HandlerType.SRV_AUTH_ERROR);
//        }
        boolean b = resService.saveBatch(user);
        return new ResponseVO<>(b);
    }

    @LogAnnotation(option = "删除菜单")
    @MyAuthority(auth="/admin/menu/index")
    @PostMapping("/del")
    public ResponseVO<String> delete(@RequestBody JSONObject params, @Login User user) {
        if(user.getIsAdmin() != 1) {
            throw new BusinessException(HandlerType.SRV_AUTH_ERROR);
        }
        return resService.delete(params.getString("menuOid"), params.getString("parentId"));
    }

}
