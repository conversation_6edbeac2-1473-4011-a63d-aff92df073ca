package com.main.service.his;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.Login;
import com.common.ZjmUtil;
import com.main.bean.vo.ResponseVO;
import com.main.handler.Api;
import com.main.mapper.his.DrugExtendedInfoMapper;
import com.main.mapper.his.DrugInfoMapper;
import com.main.mapper.his.DrugInventoryInfoMapper;
import com.main.mapper.his.DrugPricingInfoMapper;
import com.main.myenum.HandlerType;
import com.main.pojo.admin.User;
import com.main.pojo.his.DrugBasicInfo;
import com.main.pojo.his.DrugExtendedInfo;
import com.main.pojo.his.DrugInventoryInfo;
import com.main.pojo.his.DrugPricingInfo;
import com.main.pojo.his.vo.DrugInfoVO;
import com.main.service.util.RedissLockUtil;
import com.main.util.CUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 药品信息服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrugInfoServiceImpl extends ServiceImpl<DrugInfoMapper, DrugBasicInfo> implements DrugInfoService {

    /**
     * 药品扩展信息Mapper
     */
    private final DrugExtendedInfoMapper drugExtendedInfoMapper;

    /**
     * 药品定价信息Mapper
     */
    private final DrugPricingInfoMapper drugPricingInfoMapper;

    /**
     * 药品库存信息Mapper
     */
    private final DrugInventoryInfoMapper drugInventoryInfoMapper;

    /**
     * 获取药品库存锁的key
     * 格式：drug:inventory:lock:{诊所ID}:{库房ID}:{药品ID}
     */
    private String getDrugInventoryLockKey(String companyId, String warehouseId, String oid) {
        return String.format("drug:inventory:lock:%s:%s:%s", companyId, warehouseId, oid);
    }

    @Override
    public ResponseVO<Map<String, Object>> pageList(@Login User user, Integer pageNum, Integer pageSize, String keyword, String warehouseId, JSONObject param) {
        // 创建分页对象
        Page<DrugInfoVO> page = new Page<>(pageNum, pageSize);
        // 获取当前登录用户的诊所ID
        String companyId = user.getCurrentLoginOrg().getOid();
        // 调用Mapper执行分页查询
        page = baseMapper.selectDrugInfoPage(page, keyword, companyId, warehouseId, param);
        return Api.packageTable(page);
    }

    @Override
    public ResponseVO<DrugInfoVO> getById(String companyId, String warehouseId, String oid) {
        // 调用Mapper查询药品详细信息
        DrugInfoVO drugInfoVO = baseMapper.selectDrugInfoById(companyId, warehouseId, oid);
        return new ResponseVO<>(drugInfoVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> saveOrUpdateBasicInfo(@Login User user, DrugBasicInfo drugBasicInfo, String warehouseId) {
        // 设置诊所ID和库房ID
        drugBasicInfo.setCompanyId(user.getCurrentLoginOrg().getOid());
        drugBasicInfo.setWarehouseId(warehouseId);
        // 保存药品基本信息
        saveOrUpdate(drugBasicInfo);
        return new ResponseVO<>(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> saveOrUpdateExtendedInfo(@Login User user, DrugExtendedInfo drugExtendedInfo, String warehouseId) {
        // 设置诊所ID和库房ID
        drugExtendedInfo.setCompanyId(user.getCurrentLoginOrg().getOid());
        drugExtendedInfo.setWarehouseId(warehouseId);
        // 保存药品扩展信息
        drugExtendedInfoMapper.insert(drugExtendedInfo);
        return new ResponseVO<>(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> saveOrUpdatePricingInfo(@Login User user, DrugPricingInfo drugPricingInfo, String warehouseId) {
        // 设置诊所ID和库房ID
        drugPricingInfo.setCompanyId(user.getCurrentLoginOrg().getOid());
        drugPricingInfo.setWarehouseId(warehouseId);
        // 保存药品定价信息
        drugPricingInfoMapper.insert(drugPricingInfo);
        return new ResponseVO<>(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> saveOrUpdateInventoryInfo(@Login User user, DrugInventoryInfo drugInventoryInfo, String warehouseId) {
        // 设置诊所ID和库房ID
        drugInventoryInfo.setCompanyId(user.getCurrentLoginOrg().getOid());
        drugInventoryInfo.setWarehouseId(warehouseId);

        // 获取分布式锁
        String lockKey = getDrugInventoryLockKey(
                drugInventoryInfo.getCompanyId(),
                drugInventoryInfo.getWarehouseId(),
                drugInventoryInfo.getOid()
        );

        ResponseVO<Boolean> response = new ResponseVO<>();
        try {
            // 尝试获取锁，最多等待3秒
            if (!RedissLockUtil.tryLock(lockKey, 3)) {
                response.setRetInfo(HandlerType.SYSTEM_ERROR);
                response.setMsg("获取库存锁失败！");
                return response;
            }

            // 执行库存更新操作
            drugInventoryInfoMapper.insert(drugInventoryInfo);
            return new ResponseVO<>(true);
        } catch (Exception e) {
            log.error("更新药品库存信息失败", e);
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("更新药品库存信息失败");
            return response;
        } finally {
            // 释放锁
            RedissLockUtil.unlock(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> delete(String companyId, String warehouseId, String oid) {
        // 获取分布式锁
        String lockKey = getDrugInventoryLockKey(companyId, warehouseId, oid);

        ResponseVO<Boolean> response = new ResponseVO<>();
        try {
            // 尝试获取锁，最多等待3秒
            if (!RedissLockUtil.tryLock(lockKey, 3)) {
                response.setRetInfo(HandlerType.SYSTEM_ERROR);
                response.setMsg("获取库存锁失败！");
                return response;
            }

            // 执行删除操作，删除药品的所有相关信息
            removeById(oid);
            drugExtendedInfoMapper.deleteById(oid);
            drugPricingInfoMapper.deleteById(oid);
            drugInventoryInfoMapper.deleteById(oid);
            return new ResponseVO<>(true);
        } catch (Exception e) {
            log.error("删除药品信息失败", e);
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("删除药品信息失败");
            return response;
        } finally {
            // 释放锁
            RedissLockUtil.unlock(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> saveDrugInfo(@Login User user, DrugInfoVO drugInfo) {
        ResponseVO<Boolean> response = new ResponseVO<>();
        String companyId = user.getCurrentLoginOrg().getOid();
        String warehouseId = drugInfo.getWarehouseId();
        String oid = drugInfo.getOid();
        boolean updateFlag = false;
        if(CUtil.isEmpty(oid)) {
            oid = companyId+"_"+warehouseId+"_"+drugInfo.getProductCode();
            drugInfo.setEnable(1);
        } else {
            updateFlag = true;
        }
        // 固定设置值
        drugInfo.setZjm(ZjmUtil.generateZJM(drugInfo.getName()));

        // 获取分布式锁
        String lockKey = getDrugInventoryLockKey(companyId, warehouseId, oid);

        try {
            // 尝试获取锁，最多等待3秒
            if (!RedissLockUtil.tryLock(lockKey, 3)) {
                response.setRetInfo(HandlerType.SYSTEM_ERROR);
                response.setMsg("获取库存锁失败！");
                return response;
            }

            // 1. 保存基本信息
            DrugBasicInfo basicInfo = new DrugBasicInfo();
            basicInfo.setCompanyId(companyId);
            basicInfo.setWarehouseId(warehouseId);
            basicInfo.setOid(oid);
            // 复制基本信息字段
            BeanUtils.copyProperties(drugInfo, basicInfo, "companyId", "warehouseId", "oid");
            if(CUtil.isEmpty(basicInfo.getSpecification())) {
                basicInfo.calcSpecification();
            }
            saveOrUpdate(basicInfo);

            // 2. 保存扩展信息
            DrugExtendedInfo extendedInfo = new DrugExtendedInfo();
            extendedInfo.setCompanyId(companyId);
            extendedInfo.setWarehouseId(warehouseId);
            extendedInfo.setOid(oid);
            // 复制扩展信息字段
            BeanUtils.copyProperties(drugInfo, extendedInfo, "companyId", "warehouseId", "oid");
            if(updateFlag) {
                drugExtendedInfoMapper.updateById(extendedInfo);
            } else {
                drugExtendedInfoMapper.insert(extendedInfo);
            }

            // 3. 保存定价信息
            DrugPricingInfo pricingInfo = new DrugPricingInfo();
            pricingInfo.setCompanyId(companyId);
            pricingInfo.setWarehouseId(warehouseId);
            pricingInfo.setOid(oid);
            // 复制定价信息字段
            BeanUtils.copyProperties(drugInfo, pricingInfo, "companyId", "warehouseId", "oid");
            if(updateFlag) {
                drugPricingInfoMapper.updateById(pricingInfo);
            } else {
                drugPricingInfoMapper.insert(pricingInfo);
            }

            // 4. 保存库存信息
            DrugInventoryInfo inventoryInfo = new DrugInventoryInfo();
            inventoryInfo.setCompanyId(companyId);
            inventoryInfo.setWarehouseId(warehouseId);
            inventoryInfo.setOid(oid);
            // 复制库存信息字段
            BeanUtils.copyProperties(drugInfo, inventoryInfo, "companyId", "warehouseId", "oid");
            if(updateFlag) {
                drugInventoryInfoMapper.updateById(inventoryInfo);
            } else {
                drugInventoryInfoMapper.insert(inventoryInfo);
            }

            return new ResponseVO<>(true);
        } catch (Exception e) {
            log.error("保存药品信息失败", e);
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("保存药品信息失败");
            // 发生异常时会自动回滚事务
            return response;
        } finally {
            // 释放锁
            RedissLockUtil.unlock(lockKey);
        }
    }

    @Override
    public ResponseVO<String> changeDrugStatus(String drugOid, int enable) {
        LambdaUpdateWrapper<DrugBasicInfo> update = new LambdaUpdateWrapper<>();
        update.set(DrugBasicInfo::getEnable, enable);
        update.eq(DrugBasicInfo::getOid, drugOid);
        this.update(update);
        return new ResponseVO<>();
    }
} 