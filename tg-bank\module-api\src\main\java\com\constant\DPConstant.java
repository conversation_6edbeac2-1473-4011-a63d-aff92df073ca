package com.constant;

/**
 * <AUTHOR>
 * 2021/9/15 08:39
 */
public class DPConstant {

    //用户状态信息  1正常 2锁定 0停用
    public static final String STATUS_NORMAL = "1";
    public static final String STATUS_LOCK = "2";
    public static final String STATUS_OFF = "0";

    //用户性别  0女 1男 2未知
    public static final int SEX_MAN = 1;
    public static final int SEX_WOMEN = 0;
    public static final int SEX_UNKNOW = 2;

    //用户类型
    public static final int USERTYPE_FRONT = 0;//0 前台用户
    public static final int USERTYPE_BACK = 1;//1 后台用户


    //适用是否启用， 0否 1是
    public static final String ENABLE = "1";
    public static final String DISABLE = "0";

    //实名信息
    public static final int AUTHLEVEL_NOAUTH = 0;//未实名
    public static final int AUTHLEVEL_AUTH = 1;//实名
    public static final int ID_TYPE_IDCARD = 0;//身份类型 身份证


    //参数标识 0 临时参数 有效期不超过1天的参数  1 系统参数 2 业务参数
    public static final String PFLAG_TEMP = "0";
    public static final String PFLAG_SYS = "1";
    public static final String PFLAG_BNS = "2";

    //登录渠道
    public static final String WX_PUB = "WX_PUB";
    public static final String H5 = "H5";


    //RedisKey 管理
    public static final String RK_RETRY_TIMES = "dp:login:retry-times:";//+requestId 密码或验证码连续输入错误次数
    public static final String RK_SMS_CODE = "dp:sms:code:";//+phoneNo 短信验证码 默认10分钟有效
    public static final String RK_SMS_HADSENT = "dp:sms:sendInOneMinutes:";//+phoneNo 一分钟发送一次标识 有效期1分钟
    public static final String RK_SMS_HADSENT_REQ = "dp:sms:sendInOneMinutes:req:";//+requestId一分钟发送一次标识 有效期1分钟
    public static final String RK_SMS_SENDNUMSONEDAY = "dp:sms:sendNumsInOneDay:";//+phoneNo 一天发送了多少条短信，默认允许10条
    public static final String RK_SMS_SENDNUMSONEDAY_REQ = "dp:sms:sendNumsInOneDay:req:";//+requestId 一天发送了多少条短信，默认允许10条
    public static final String RK_DP_TOKEN = "dp:user:token:";//+token token获取用户信息
    public static final String RK_DP_ID_TOKEN = "dp:user:oid:";//+oid oid获取token

    public static final String RK_DP_TICKET = "dp:user:ticket:";//+ticket ticket 用于oauth2换取token
    public static final String RK_DP_CLIENT_ID = "dp:user:clientId:";//+clientId 获取clientId相关信息 存requestId走通旧登录
    public static final String RK_DP_TICKET_TEMP = "dp:SSO:TOKEN_TICKET_TEMP:";//+token 避免token 频繁生成ticket
    public static final String RK_DP_LOGOUT = "dp:SSO:LOGOUT:";//记录第三方退出登录成功后的回调地址
    public static final String RK_TEMP = "dp:TEMP:";//临时数据

    public static final String DEFAULT_PASSWORD = "123456";

    //用户是否在线
    public static final String USER_ONLINE = "1";
    public static final String USER_OFFLINE = "0";

    //websocket消息类型
    //对应界面上的邮箱通知
    public static final String WS_MSG_TYPE_MESSAGE = "SYSTEM";
    public static final String WS_MSG_TYPE_PERSONAL = "PERSONAL";
    public static final String WS_MSG_TYPE_CHAT = "CHAT";
    //右上角弹窗提示
    public static final String WS_MSG_TYPE_NOTIFICATION = "notification";
    //shell脚本
    public static final String WS_MSG_TYPE_SHELL = "shell";


}
