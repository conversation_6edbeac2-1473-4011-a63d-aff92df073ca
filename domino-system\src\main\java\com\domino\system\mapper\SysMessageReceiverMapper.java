package com.domino.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.domino.common.qh.domain.SysMessageReceiver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 消息接收记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Mapper
public interface SysMessageReceiverMapper extends BaseMapper<SysMessageReceiver> {

    // ==================== 基础增删改查方法 ====================

    /**
     * 插入消息接收记录
     *
     * @param messageReceiver 消息接收记录对象
     * @return 影响行数
     */
    int insertMessageReceiver(SysMessageReceiver messageReceiver);

    /**
     * 批量插入消息接收记录
     *
     * @param messageReceiverList 消息接收记录列表
     * @return 影响行数
     */
    int batchInsertMessageReceiver(@Param("list") List<SysMessageReceiver> messageReceiverList);

    /**
     * 根据ID查询消息接收记录
     *
     * @param receiverId 主键ID
     * @return 消息接收记录对象
     */
    SysMessageReceiver selectMessageReceiverById(String receiverId);

    /**
     * 查询消息接收记录列表
     *
     * @param messageReceiver 消息接收记录查询条件
     * @return 消息接收记录列表
     */
    List<SysMessageReceiver> selectMessageReceiverList(SysMessageReceiver messageReceiver);

    /**
     * 更新消息接收记录
     *
     * @param messageReceiver 消息接收记录对象
     * @return 影响行数
     */
    int updateMessageReceiver(SysMessageReceiver messageReceiver);

    /**
     * 删除消息接收记录
     *
     * @param receiverId 主键ID
     * @return 影响行数
     */
    int deleteMessageReceiverById(String receiverId);

    /**
     * 批量删除消息接收记录
     *
     * @param receiverIds 主键ID数组
     * @return 影响行数
     */
    int deleteMessageReceiverByIds(String[] receiverIds);

    // ==================== 扩展方法 ====================

    /**
     * 根据消息ID删除接收记录
     *
     * @param messageId 消息ID
     * @return 影响行数
     */
    int deleteByMessageId(String messageId);

    /**
     * 根据消息ID数组批量删除接收记录
     *
     * @param messageIds 消息ID数组
     * @return 影响行数
     */
    int deleteByMessageIds(String[] messageIds);

    /**
     * 根据用户ID删除接收记录
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(String userId);

    /**
     * 根据消息ID和用户ID查询接收记录
     *
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息接收记录对象
     */
    SysMessageReceiver selectByMessageIdAndUserId(@Param("messageId") String messageId, @Param("userId") String userId);

    /**
     * 检查消息是否已读
     *
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 是否已读
     */
    boolean checkMessageRead(@Param("messageId") String messageId, @Param("userId") String userId);

    /**
     * 根据消息ID查询所有接收者
     *
     * @param messageId 消息ID
     * @return 接收记录列表
     */
    List<SysMessageReceiver> selectReceiversByMessageId(String messageId);

    /**
     * 根据用户ID查询所有已读消息
     *
     * @param userId 用户ID
     * @return 接收记录列表
     */
    List<SysMessageReceiver> selectReadMessagesByUserId(String userId);

    /**
     * 统计消息的已读人数
     *
     * @param messageId 消息ID
     * @return 已读人数
     */
    int countReadByMessageId(String messageId);

    /**
     * 统计用户的已读消息数量
     *
     * @param userId 用户ID
     * @return 已读消息数量
     */
    int countReadByUserId(String userId);

    /**
     * 根据时间范围查询接收记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 接收记录列表
     */
    List<SysMessageReceiver> selectByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 清理过期的接收记录
     *
     * @param days 过期天数
     * @return 影响行数
     */
    int cleanExpiredRecords(@Param("days") int days);
}
