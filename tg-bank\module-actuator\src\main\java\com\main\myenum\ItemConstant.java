package com.main.myenum;

/**
 * 全局常量 样例
 * <AUTHOR>
 * 2021/11/17 08:39
 */
public class ItemConstant {

	//用户状态信息  0正常 1锁定 2删除 3注销
	public static final int STATUS_NORMAL = 0;
	public static final int STATUS_LOCK = 1;
	public static final int STATUS_DEL = 2;
	public static final int STATUS_OFF = 3;

	//用户性别  0女 1男 2未知
	public static final int SEX_MAN = 1;
	public static final int SEX_WOMEN = 0;
	public static final int SEX_UNKNOW = 2;

	//用户类型
	public static final int USERTYPE_FRONT = 0;//0 前台用户
	public static final int USERTYPE_BACK = 1;//1 后台用户


	//适用是否启用， 0否 1是
	public static final String ENABLE = "1";
	public static final String DISABLE = "0";

	//实名信息
	public static final int AUTHLEVEL_NOAUTH = 0;//未实名
	public static final int AUTHLEVEL_AUTH = 1;//实名
	public static final int ID_TYPE_IDCARD = 0;//身份类型 身份证


	//参数标识 0 临时参数 有效期不超过1天的参数  1 系统参数 2 业务参数
	public static final String PFLAG_TEMP = "0";
	public static final String PFLAG_SYS = "1";
	public static final String PFLAG_BNS = "2";

	//登录渠道
	public static final String WX_PUB = "WX_PUB";
	public static final String H5 = "H5";


	//成功
	public static final String OK = "ok";
	//失败
	public static final String FAIL = "fail";
}
