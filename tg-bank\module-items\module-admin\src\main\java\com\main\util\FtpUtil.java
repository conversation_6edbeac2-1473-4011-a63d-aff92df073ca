package com.main.util;
 
import com.enterprisedt.net.ftp.FTPClient;
import com.enterprisedt.net.ftp.FTPConnectMode;
import com.enterprisedt.net.ftp.FTPTransferType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
 
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
 
/**
 * ftp工具类
 *
 * <AUTHOR>
@Deprecated
@Slf4j
public class FtpUtil {
    /**
     * ftp客户端
     */
    private FTPClient ftp;
 
    /**
     * 初始化Ftp信息
     *
     * @param ftpServer   ftp服务器地址
     * @param ftpPort     Ftp端口号
     * @param ftpUsername ftp 用户名
     * @param ftpPassword ftp 密码
     */
    public FtpUtil(String ftpServer, int ftpPort, String ftpUsername,
                   String ftpPassword) {
        connect(ftpServer, ftpPort, ftpUsername, ftpPassword);
    }
 
    /**
     * 连接到ftp
     *
     * @param ftpServer   ftp服务器地址
     * @param ftpPort     Ftp端口号
     * @param ftpUsername ftp 用户名
     * @param ftpPassword ftp 密码
     */
    public void connect(String ftpServer, int ftpPort, String ftpUsername, String ftpPassword) {
        ftp = new FTPClient();
        try {
            ftp.setControlEncoding("UTF-8");
            ftp.setRemoteHost(ftpServer);
            ftp.setRemotePort(ftpPort);
            ftp.setTimeout(6000);
            ftp.setConnectMode(FTPConnectMode.ACTIVE);
            ftp.connect();
            ftp.login(ftpUsername, ftpPassword);
            ftp.setType(FTPTransferType.BINARY);
        } catch (Exception e) {
            log.error(e.toString(), e);
            ftp = null;
        }
    }
 
    /**
     * 更改ftp路径
     *
     * @param ftp     ftp客户端
     * @param dirName 目录
     * @return 是否成功
     */
    public boolean checkDirectory(FTPClient ftp, String dirName) {
        boolean flag;
        try {
            ftp.chdir(dirName);
            flag = true;
        } catch (Exception e) {
            log.error(e.toString(), e);
            flag = false;
        }
        return flag;
    }
 
    /**
     * 断开ftp链接
     */
    public void disconnect() {
        try {
            if (ftp.connected()) {
                ftp.quit();
            }
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
 
    /**
     * 读取ftp文件流
     *
     * @param filePath ftp文件路径
     * @return 文件流
     * @throws Exception 异常
     */
    public InputStream downloadFile(String filePath) throws Exception {
        InputStream inputStream = null;
        String fileName = "";
        filePath = StringUtils.removeStart(filePath, "/");
        int len = filePath.lastIndexOf("/");
        if (len == -1) {
            if (filePath.length() > 0) {
                fileName = filePath;
            } else {
                throw new Exception("没有输入文件路径");
            }
        } else {
            fileName = filePath.substring(len + 1);
 
            String type = filePath.substring(0, len);
            String[] typeArray = type.split("/");
            for (String s : typeArray) {
                ftp.chdir(s);
            }
        }
        byte[] data;
        try {
            data = ftp.get(fileName);
            inputStream = new ByteArrayInputStream(data);
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
        return inputStream;
    }
 
    /**
     * 上传文件到ftp
     *
     * @param file     文件对象
     * @param filePath 上传的路径
     * @throws Exception 异常
     */
    public void uploadFile(File file, String filePath) throws Exception {
        InputStream inStream = new FileInputStream(file);
        uploadFile(inStream, filePath);
    }
 
    /**
     * 上传文件到ftp
     *
     * @param inStream 上传的文件流
     * @param filePath 上传路径
     */
    public void uploadFile(InputStream inStream, String filePath)
            throws Exception {
        if (inStream == null) {
            return;
        }
        String fileName = "";
        filePath = StringUtils.removeStart(filePath, "/");
        int len = filePath.lastIndexOf("/");
        if (len == -1) {
            if (filePath.length() > 0) {
                fileName = filePath;
            } else {
                throw new Exception("没有输入文件路径");
            }
        } else {
            fileName = filePath.substring(len + 1);
            String type = filePath.substring(0, len);
            String[] typeArray = type.split("/");
            for (String s : typeArray) {
                if (!checkDirectory(ftp, s)) {
                    ftp.mkdir(s);
                }
            }
        }
 
        ftp.put(inStream, fileName);
    }
 
    /**
     * 删除ftp文件
     *
     * @param filePath 文件路径
     * @throws Exception 异常
     */
    public void deleteFile(String filePath) throws Exception {
        String fileName = "";
        filePath = StringUtils.removeStart(filePath, "/");
        int len = filePath.lastIndexOf("/");
        if (len == -1) {
            if (filePath.length() > 0) {
                fileName = filePath;
            } else {
                throw new Exception("没有输入文件路径");
            }
        } else {
            fileName = filePath.substring(len + 1);
 
            String type = filePath.substring(0, len);
            String[] typeArray = type.split("/");
            for (String s : typeArray) {
                if (checkDirectory(ftp, s)) {
                    ftp.chdir(s);
                }
            }
        }
        ftp.delete(fileName);
    }
 
    /**
     * 切换目录
     *
     * @param path 目录
     */
    public void changeDirectory(String path) {
        if (!StringUtils.isEmpty(path)) {
            try {
                ftp.chdir(path);
            } catch (Exception e) {
                log.error(e.toString(), e);
            }
        }
    }
 
    /**
     * 获取目录下文件列表
     *
     * @param directory 目录
     * @return 文件列表
     */
    public String[] list(String directory) {
        try {
            String[] list = ftp.dir(directory, false);
            return list;
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
        return null;
    }
 
    public static void main(String[] args) {
        // 从ftp下载文件
        FtpUtil ftp = new FtpUtil("121.36.52.230", 6607, "root", "Ecansoft@2023");
        String[] fileList = ftp.list("/home");
        for (String file : fileList) {
            log.info(file);
        }
    }
}