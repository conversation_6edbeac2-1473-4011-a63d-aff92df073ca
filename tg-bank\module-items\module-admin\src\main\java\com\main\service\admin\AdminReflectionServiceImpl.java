package com.main.service.admin;

import com.main.bean.vo.RequestVO;
import com.main.bean.vo.ResponseVO;
import com.main.myenum.HandlerType;
import com.main.service.inf.ReflectionService;
import com.main.util.CUtil;
import com.util.ParamValid;
import com.util.YamlUtil;
import com.alibaba.fastjson.JSON;
import com.util.reflect.ReflectConfig;
import com.util.reflect.ReflectContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Map;

/**
 * <AUTHOR>
 * 2019/9/16 10:33
 */
//TODO group 修改为自己的项目名
@DubboService(group = "module-admin",version = "1.0.0")
@Slf4j
public class AdminReflectionServiceImpl implements ReflectionService {

	@Override
	public ResponseVO<Object> doHandler(RequestVO requestVO) {
		//TODO
		long begin = System.currentTimeMillis();
		ResponseVO<Object> responseVO = new ResponseVO<>();
		log.info("==|-----------------统一反射调用开始-----------------|==");
		log.info("用户中心服务处理启动，入参：{}", JSON.toJSONString(requestVO));
		String method = requestVO.getMethod();
		try {
			//校验参数
			ParamValid.valiadParam(requestVO);
			Map<String, Object> map = YamlUtil.getTypePropertieMap(YamlUtil.RFL_BEAN_CONFIG, method);
			ReflectConfig config = new ReflectConfig();
			config.setBeanName(String.valueOf(map.get("bean")));
			config.setMethodName(String.valueOf(map.get("method")));
			config.setParam(requestVO);
			responseVO = (ResponseVO<Object>) ReflectContext.excutor(config);

		} catch (Exception e) {
			e.printStackTrace();
			Throwable cause = e.getCause();
			String errorMsg = null;
			if (cause != null) {
				errorMsg = cause.getMessage();
			}else {
				errorMsg = e.getMessage();
			}
			log.error("反射异常报错：{}",errorMsg);
			responseVO.setCode(HandlerType.SYSTEM_ERROR.getRetCode());
			responseVO.setMsg(HandlerType.SYSTEM_ERROR.getRetMsg() + errorMsg);
		}
		long end = System.currentTimeMillis();
		log.info("接口耗时:{}毫秒,用户中心服务处理结束，返回：{}",(end-begin), CUtil.formateJSON(JSON.toJSONString(responseVO)));
		log.info("==|-----------------用户中心统一反射调用结束-----------------|==");
		return responseVO;
	}
}
