package com.main.service.admin;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.Org;
import com.main.pojo.admin.UserOrg;

import java.util.List;

/**
 * <p>
 * 用户关联机构 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserOrgService extends IService<UserOrg> {

    /**
     * 我自己的保存机构方法
     *
     * @param userOid 用户主索引
     * @param orgList 机构信息
     * @return boolean
     */
    boolean mySave(String userOid, JSONArray orgList);

    boolean delete(String userOid);

    /**
     * 获取用户归属的机构列表
     *
     * @param oid 用户id
     * @return 机构列表
     */
    List<Org> getUserOrgList(String oid);

    /**
     * 校验机构是否过期
     * @param org 机构对象
     * @return 结果 true or false
     */
    boolean validateOrg(Org org);

    boolean setDefaultLoginOrg(String orgOid, String userOid);

}
