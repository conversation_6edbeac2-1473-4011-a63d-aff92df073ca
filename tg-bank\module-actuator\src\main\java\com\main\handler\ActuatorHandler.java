package com.main.handler;

import com.aizuda.common.toolkit.CollectionUtils;
import com.aizuda.monitor.DiskInfo;
import com.aizuda.monitor.MemoryInfo;
import com.aizuda.monitor.OshiMonitor;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.main.myenum.Util;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import oshi.SystemInfo;
import oshi.hardware.*;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;
import oshi.util.FormatUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@RequestMapping("/diy-actuator")
@RestController
public class ActuatorHandler {
    private SystemInfo si = new SystemInfo();
    @Resource
    private OshiMonitor oshiMonitor;

    @RequestMapping("/health")
    public String getHealth() {
        return "success";
    }

    @RequestMapping("/monitor")
    public Map<String, Object> monitor(String auth) {
        HardwareAbstractionLayer hal = si.getHardware();
        OperatingSystem os = si.getOperatingSystem();
        Map<String, Object> server = new HashMap<>(5);
        // 系统信息
        server.put("sysInfo", oshiMonitor.getSysInfo());
        // CPU 信息
//        server.put("cpuInfo", oshiMonitor.getCpuInfo());
        // 内存信息
        MemoryInfo mem = oshiMonitor.getMemoryInfo();
        String used = mem.getUsed().replace("GB", "").replace("MB", "");
        String free = mem.getFree().replace("GB", "").replace("MB", "");
        JSONObject memJson = new JSONObject();
        memJson.put("used", used);
        memJson.put("free", free);
        memJson.put("total", mem.getTotal());
        memJson.put("usePercent", mem.getUsePercent());
        if (mem.getUsePercent() * 100 > 80) {
            memJson.put("mem_zs", "up");
        } else {
            memJson.put("mem_zs", "down");
        }
        server.put("memoryInfo", memJson);

        // Jvm 虚拟机信息
        server.put("jvmInfo", oshiMonitor.getJvmInfo());
        // 磁盘信息
        List<DiskInfo> diskInfos = oshiMonitor.getDiskInfos();


        Map<String, Long> totalLabelSizeMap = new HashMap<>();
        Map<String, Long> usableLabelSizeMap = new HashMap<>();
        Map<String, String> addFlag = new HashMap<>();
        long totalSpace = 0;
        long usableSpace = 0;
        if (CollectionUtils.isNotEmpty(diskInfos)) {
            for (DiskInfo diskInfo : diskInfos) {
                if (StringUtils.isEmpty(addFlag.get(diskInfo.getSize()))) {
                    totalSpace += diskInfo.getTotalSpace();
                    usableSpace += diskInfo.getUsableSpace();
                    usableLabelSizeMap.put(diskInfo.getSize(), diskInfo.getUsableSpace());
                    totalLabelSizeMap.put(diskInfo.getSize(), diskInfo.getTotalSpace());
                    addFlag.put(diskInfo.getSize(), diskInfo.getLabel());
                }
            }
            double usedSize = (totalSpace - usableSpace);
            // 统计所有磁盘的使用率
            server.put("diskUsePercent", oshiMonitor.formatDouble(usedSize / totalSpace * 100));
            server.put("diskUsedSize", oshiMonitor.formatDouble(usedSize / 1024 / 1024 / 1024));
            server.put("diskUsableSize", oshiMonitor.formatDouble(usableSpace / 1024 / 1024 / 1024));
            server.put("diskTotalSize", oshiMonitor.formatDouble(totalSpace / 1024 / 1024 / 1024));
            if (oshiMonitor.formatDouble(usedSize / totalSpace * 100) > 80) {
                server.put("disk_zs", "up");
            } else {
                server.put("disk_zs", "down");
            }
            JSONArray diskDetails = new JSONArray();
            for (String label : addFlag.keySet()) {
                JSONObject labelDiskInfo = new JSONObject();
                labelDiskInfo.put("label", addFlag.get(label));
                labelDiskInfo.put("totalSize", oshiMonitor.formatDouble(totalLabelSizeMap.get(label) / 1024 / 1024));
                labelDiskInfo.put("usableSize", oshiMonitor.formatDouble(usableLabelSizeMap.get(label) / 1024 / 1024));
                diskDetails.add(labelDiskInfo);
            }
            server.put("diskDetail", diskDetails);
        }

        server.put("computerName", String.valueOf(os));
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochSecond(os.getSystemBootTime()), ZoneId.systemDefault());
        String tempTime = time.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss"));
        server.put("Booted", tempTime);
        server.put("Uptime", Util.formatElapsedSecs(os.getSystemUptime()));
        return server;
    }

    @Deprecated
    @RequestMapping("/baseInfo")
    public JSONObject getBaseInfo(String auth) {
        JSONObject json = new JSONObject();
        HardwareAbstractionLayer hal = si.getHardware();
        OperatingSystem os = si.getOperatingSystem();
        json.put("computerName", String.valueOf(os));
        json.put("Booted", Instant.ofEpochSecond(os.getSystemBootTime()));
        json.put("Uptime", FormatUtil.formatElapsedSecs(os.getSystemUptime()));
        json.put("cpu-core", si.getHardware().getProcessor().getProcessorIdentifier().getName());
        json.put("cpu-core-py", si.getHardware().getProcessor().getPhysicalProcessorCount());
        json.put("cpu-core-lj", si.getHardware().getProcessor().getLogicalProcessorCount());
        json.put("processor", hal.getProcessor().toString());
        json.put("p-mem", hal.getMemory().toString());
        VirtualMemory vm = hal.getMemory().getVirtualMemory();
        json.put("v-mem", vm.toString());
        GlobalMemory memory = si.getHardware().getMemory();
        long totalMemorySize = memory.getTotal();
        long availableMemorySize = memory.getAvailable();
        double memoryUsage = (totalMemorySize - availableMemorySize) * 100.0 / totalMemorySize;
        json.put("mem-used", memoryUsage);
        return json;
    }

    @RequestMapping("/processor")
    public JSONObject getProcessor(String auth) {
        JSONObject json = new JSONObject();
        OperatingSystem os = si.getOperatingSystem();
        HardwareAbstractionLayer hal = si.getHardware();
        CentralProcessor processor = hal.getProcessor();
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        long[][] prevProcTicks = processor.getProcessorCpuLoadTicks();
        List<NetworkIF> beforeList = hal.getNetworkIFs();
        Map<String, Long> beforeNetInfo = new HashMap<>();
        if(!beforeList.isEmpty()) {
            for (NetworkIF net : beforeList) {
                beforeNetInfo.put(net.getName()+"_REV", net.getBytesRecv());
                beforeNetInfo.put(net.getName()+"_SENT", net.getBytesSent());
            }
        }

        AtomicLong timeWait = new AtomicLong(2000L);
        // Wait a second...
        oshi.util.Util.sleep(timeWait.get());
        List<NetworkIF> list = hal.getNetworkIFs();
        JSONArray netList = new JSONArray();
        if (!list.isEmpty()) {
            for (NetworkIF net : list) {
                JSONObject netObj = new JSONObject();
                long nowByte = net.getBytesRecv();
                long nowByte_sent = net.getBytesSent();
                long agoByte = beforeNetInfo.get(net.getName()+"_REV");
                long agoByte_sent = beforeNetInfo.get(net.getName()+"_SENT");
                long ce4rec = nowByte-agoByte;
                long ce4sent = nowByte_sent-agoByte_sent;
                if(ce4rec < 1000) {
                    netObj.put("recNetSpeed",ce4rec/(timeWait.get()/1000) + " B/s"); //MB/S
                }else if(ce4rec < 1000000) {
                    netObj.put("recNetSpeed",ce4rec/1024/(timeWait.get()/1000) + " KB/s"); //MB/S
                }else if(ce4rec < 1000000000) {
                    netObj.put("recNetSpeed",ce4rec/1024/1024/(timeWait.get()/1000) + " MB/s"); //MB/S
                }else {
                    netObj.put("recNetSpeed",ce4rec/1024/1024/1024/(timeWait.get()/1000) + " GB/s"); //MB/S
                }
                if(ce4sent < 1000) {
                    netObj.put("sentNetSpeed",ce4sent/(timeWait.get()/1000) + " B/s"); //MB/S
                }else if(ce4sent < 1000000) {
                    netObj.put("sentNetSpeed",ce4sent/1024/(timeWait.get()/1000) + " KB/s"); //MB/S
                }else if(ce4sent < 1000000000) {
                    netObj.put("sentNetSpeed",ce4sent/1024/1024/(timeWait.get()/1000) + " MB/s"); //MB/S
                }else {
                    netObj.put("sentNetSpeed",ce4sent/1024/1024/1024/(timeWait.get()/1000) + " GB/s"); //MB/S
                }
                netObj.put("name", net.getName());
                netList.add(netObj);
            }
        }
        json.put("netSpeedInfos", netList);


        long[] ticks = processor.getSystemCpuLoadTicks();
        long user = ticks[CentralProcessor.TickType.USER.getIndex()] - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()] - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long sys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()] - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()] - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long iowait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()] - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()] - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softirq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()] - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()] - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];
        long totalCpu = user + nice + sys + idle + iowait + irq + softirq + steal;
        json.put("cpu", BigDecimal.valueOf(processor.getSystemCpuLoadBetweenTicks(prevTicks) * 100).setScale(2, RoundingMode.UP).doubleValue());
        double[] load = processor.getProcessorCpuLoadBetweenTicks(prevProcTicks);
        if (totalCpu != 0) {
            json.put("user", new BigDecimal(100d * user / totalCpu).setScale(2, RoundingMode.UP).doubleValue());
            json.put("system", new BigDecimal(100d * sys / totalCpu).setScale(2, RoundingMode.UP).doubleValue());
        } else {
            json.put("user", 0);
            json.put("system", 0);
        }
        Double[] sonCpu = new Double[load.length];
        Double[] sonMem = new Double[load.length];
        int i = 0;
        for (double avg : load) {
            sonCpu[i++] = new BigDecimal(avg * 100).setScale(2, RoundingMode.UP).doubleValue();
        }
        json.put("sonCpu", sonCpu);
        if (((Double) json.get("cpu")) > 80) {
            json.put("cpu_zs", "up");
        } else {
            json.put("cpu_zs", "down");
        }
        return json;
    }

    @RequestMapping("/processes/{sort}")
    public JSONArray getProcess(String auth, @PathVariable String sort) {
        JSONArray jsonArray = new JSONArray();
        OperatingSystem os = si.getOperatingSystem();
        HardwareAbstractionLayer hal = si.getHardware();
        List<OSProcess> procs = null;
        if ("cpu".equals(sort)) {
            procs = os.getProcesses(OperatingSystem.ProcessFiltering.ALL_PROCESSES, OperatingSystem.ProcessSorting.CPU_DESC, 10);
        } else {
            procs = os.getProcesses(OperatingSystem.ProcessFiltering.ALL_PROCESSES, OperatingSystem.ProcessSorting.RSS_DESC, 10);
        }
        for (int i = 0; i < procs.size(); i++) {
            JSONObject json = new JSONObject();
            OSProcess p = procs.get(i);
            json.put("pid", p.getProcessID());
            json.put("cpup", BigDecimal.valueOf(100d * (p.getKernelTime() + p.getUserTime()) / p.getUpTime() * 100).setScale(2, RoundingMode.UP).doubleValue());
            json.put("memp", BigDecimal.valueOf(100d * p.getResidentSetSize() / hal.getMemory().getTotal()).setScale(2, RoundingMode.UP).doubleValue());
            json.put("rss", FormatUtil.formatBytes(p.getResidentSetSize()));
            json.put("name", p.getName());
            json.put("path", p.getPath());
            jsonArray.add(json);
        }
        return jsonArray;
    }

}
