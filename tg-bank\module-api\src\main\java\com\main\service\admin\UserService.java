package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.User;
import com.main.vo.MenuVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserService extends IService<User> {

    IPage<User> getPage(int pageNum, int pageSize, JSONObject params, User user);

    /**
     * 聊天助手获取聊天用户列表
     * @param pageNum 默认1
     * @param pageSize 默认100
     * @param params 参数暂未启用
     * @param user 当前登录对象
     * @return 用户列表
     */
    IPage<User> getChatPage(int pageNum, int pageSize, JSONObject params, User user);

    ResponseVO<Map<String, Object>> changeStatus(String oid, String status);

    ResponseVO<String> mySave(JSONObject params, User loginUser);

    ResponseVO<String> delete(String userOid);

    ResponseVO<Object> doLoginWithPwd(Map<String, String> headers, String username, String password);

    ResponseVO<User> getUserInfo(User user);

    ResponseVO<User> logout(String token);

    ResponseVO<List<MenuVO>> getMenu(User user);

    ResponseVO<User> switchOrg(String orgOid, HttpServletRequest request, User user);

    /**
     * 个人中心获取当前用户登录记录和机构管理团队信息
     * @param user 登录用户
     * @return 登录记录与机构管理团队
     */
    ResponseVO<Object> getLoginRecordAndManager(User user);

    List<User> getOrgManagers(String orgOid);

    ResponseVO<Object> uptPwd(String oid, User userPage);

    /**
     * 获取机构组在线成员列表
     * @param orgs 机构编码
     * @return 成员列表
     */
    List<User> getOrgOnlineUser(String orgs);

    /**
     * 重置用户密码
     * @param user 登录用户
     * @param oid 需要重置密码用户
     * @return 成功或者失败
     */
    ResponseVO<Object> resetPwd(User user, String oid);

    /**
     * 注册用户
     * @param params 用户信息
     * @return 成功或者失败
     */
    ResponseVO<String> register(JSONObject params);
}
