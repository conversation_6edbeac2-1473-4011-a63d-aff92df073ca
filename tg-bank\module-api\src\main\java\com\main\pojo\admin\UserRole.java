package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_user_role")
public class UserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userOid;

    private String roleOid;

    public UserRole(String userOid, String roleOid) {
        this.userOid = userOid;
        this.roleOid = roleOid;
    }

}
