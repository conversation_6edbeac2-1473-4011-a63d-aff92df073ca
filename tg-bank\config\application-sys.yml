dubbo:
  registry:
    #TODO zk 地址
    address: zookeeper://*************:2181
druid:
  loginPassword: pm
  loginUsername: pm
spring:
  #TODO 自行修改信息
  redis:
    database: 3
    host: *************
    password: Hand#2021
    port: 6379
    timeout: 5000
#    lettuce:
#      pool:
#        max-active: 50
#        max-wait: 2000
#        max-idle: 50
#        min-idle: 5
#      cluster:
#        refresh:
#          adaptive: true
#          period: 20
    pool:
      max-active: 50
      max-wait: 2000
      max-idle: 50
      min-idle: 5
      time-between-eviction-runs: 1000
sys:
  module-admin:
    app-name: module-admin
    app-port: 8601
    dubbo-port: 28601
  module-report:
    app-name: module-report
    app-port: 8602
    dubbo-port: 28602
  login:
    valid: 0
  cros: 1
