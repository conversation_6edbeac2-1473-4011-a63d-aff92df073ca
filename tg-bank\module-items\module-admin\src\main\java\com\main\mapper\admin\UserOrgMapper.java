package com.main.mapper.admin;

import com.main.pojo.admin.Dept;
import com.main.pojo.admin.Org;
import com.main.pojo.admin.UserOrg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户关联机构 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface UserOrgMapper extends BaseMapper<UserOrg> {

    /**
     * 获取用户归属的机构信息
     * @param userOid
     * @return
     */
    List<Org> getUserOrgList(@Param("oid") String userOid);

    List<Dept> getUserDeptList(@Param("oid") String userOid);
}
