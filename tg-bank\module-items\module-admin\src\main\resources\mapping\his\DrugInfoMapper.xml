<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.main.mapper.his.DrugInfoMapper">
    
    <resultMap id="DrugInfoVOMap" type="com.main.pojo.his.vo.DrugInfoVO">
        <id column="company_id" property="companyId"/>
        <id column="warehouse_id" property="warehouseId"/>
        <id column="oid" property="oid"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="common_name" property="commonName"/>
        <result column="trade_name" property="tradeName"/>
        <result column="zjm" property="zjm"/>
        <result column="type" property="type"/>
        <result column="category" property="category"/>
        <result column="pharmacology_category" property="pharmacologyCategory"/>
        <result column="specification" property="specification"/>
        <result column="unit" property="unit"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="approval_number" property="approvalNumber"/>
        <result column="barcode" property="barcode"/>
        <result column="product_code" property="productCode"/>
        <result column="dosage_form" property="dosageForm"/>
        <result column="dosage" property="dosage"/>
        <result column="dosage_unit" property="dosageUnit"/>
        <result column="min_package_quantity" property="minPackageQuantity"/>
        <result column="min_package_unit" property="minPackageUnit"/>
        <result column="package_unit" property="packageUnit"/>
        <result column="insurance_type" property="insuranceType"/>
        <result column="insurance_no" property="insuranceNo"/>
        <result column="insurance_code" property="insuranceCode"/>
        <result column="settlement_priority" property="settlementPriority"/>
        
        <result column="prescription_type" property="prescriptionType"/>
        <result column="special_drug" property="specialDrug"/>
        <result column="trace_code" property="traceCode"/>
        <result column="license_holder" property="licenseHolder"/>
        <result column="is_basic_drug" property="isBasicDrug"/>
        <result column="drug_tags" property="drugTags"/>
        <result column="storage_condition" property="storageCondition"/>
        <result column="maintenance_type" property="maintenanceType"/>
        <result column="shelf_life" property="shelfLife"/>
        <result column="validity_period" property="validityPeriod"/>
        <result column="validity_unit" property="validityUnit"/>
        <result column="approval_valid_date" property="approvalValidDate"/>
        <result column="location_no" property="locationNo"/>
        <result column="remark" property="remark"/>
        <result column="antibacterial_type" property="antibacterialType"/>
        <result column="antibacterial_level" property="antibacterialLevel"/>
        <result column="antibacterial_categories" property="antibacterialCategories"/>
        <result column="antibiotic_dosage" property="antibioticDosage"/>
        <result column="antibiotic_unit" property="antibioticUnit"/>
        
        <result column="purchase_price" property="purchasePrice"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="allow_split" property="allowSplit"/>
        <result column="split_retail_price" property="splitRetailPrice"/>
        <result column="pricing_mode" property="pricingMode"/>
        <result column="purchase_tax_rate" property="purchaseTaxRate"/>
        <result column="sale_tax_rate" property="saleTaxRate"/>
        
        <result column="current_stock" property="currentStock"/>
        <result column="available_stock" property="availableStock"/>
        <result column="stock_value" property="stockValue"/>
        <result column="daily_consumption" property="dailyConsumption"/>
        <result column="turnover_days" property="turnoverDays"/>
        <result column="profit_rate" property="profitRate"/>
        <result column="expiry_date" property="expiryDate"/>
    </resultMap>

    <select id="selectDrugInfoPage" resultMap="DrugInfoVOMap">
        SELECT 
            b.*,
            e.*,
            p.*,
            i.*
        FROM his_drug_basic_info b
        LEFT JOIN his_drug_extended_info e ON b.company_id = e.company_id 
            AND b.warehouse_id = e.warehouse_id AND b.oid = e.oid
        LEFT JOIN his_drug_pricing_info p ON b.company_id = p.company_id 
            AND b.warehouse_id = p.warehouse_id AND b.oid = p.oid
        LEFT JOIN his_drug_inventory_info i ON b.company_id = i.company_id 
            AND b.warehouse_id = i.warehouse_id AND b.oid = i.oid
        WHERE b.company_id = #{companyId} AND b.warehouse_id = #{warehouseId}
        <if test="keyword != null and keyword != ''">
            AND (
                b.name LIKE CONCAT('%', #{keyword}, '%')
                OR b.code LIKE CONCAT('%', #{keyword}, '%')
                OR b.common_name LIKE CONCAT('%', #{keyword}, '%')
                OR b.trade_name LIKE CONCAT('%', #{keyword}, '%')
                OR b.zjm LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="param.category != null and param.category != ''">
            AND b.category = #{param.category}
        </if>
        <if test="param.hideZeroStock != null and param.hideZeroStock == true">
            AND i.current_stock != 0
        </if>
        <if test="param.hideDisabled != null and param.hideDisabled == true">
            AND b.enable = 1
        </if>
    </select>

    <select id="selectDrugInfoById" resultMap="DrugInfoVOMap">
        SELECT 
            b.*,
            e.*,
            p.*,
            i.*
        FROM his_drug_basic_info b
        LEFT JOIN his_drug_extended_info e ON b.company_id = e.company_id 
            AND b.warehouse_id = e.warehouse_id AND b.oid = e.oid
        LEFT JOIN his_drug_pricing_info p ON b.company_id = p.company_id 
            AND b.warehouse_id = p.warehouse_id AND b.oid = p.oid
        LEFT JOIN his_drug_inventory_info i ON b.company_id = i.company_id 
            AND b.warehouse_id = i.warehouse_id AND b.oid = i.oid
        WHERE b.company_id = #{companyId} 
            AND b.warehouse_id = #{warehouseId}
            AND b.oid = #{oid}
    </select>
</mapper> 