package com.main.service.his;

import com.baomidou.mybatisplus.extension.service.IService;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.his.PatientTagRel;

import java.util.List;

/**
 * 患者标签服务接口
 * <AUTHOR>
 */
public interface PatientTagService extends IService<PatientTagRel> {
    
    /**
     * 为患者添加标签
     * @param patientId 患者ID
     * @param tagIds 标签ID列表
     * @return 添加结果
     */
    ResponseVO<String> addTagToPatient(String patientId, List<String> tagIds);
    
    /**
     * 移除患者标签
     * @param patientId 患者ID
     * @param tagId 要移除的标签ID列表
     * @return 移除结果
     */
    ResponseVO<String> removeTagFromPatient(String patientId, String tagId);
    
    /**
     * 获取患者的所有标签
     * @param patientId 患者ID
     * @return 标签列表
     */
    ResponseVO<List<PatientTagRel>> getPatientTags(String patientId);
} 