package com.main.service.admin;

import com.alibaba.fastjson.JSONObject;
import com.main.bean.vo.ResponseVO;
import com.main.pojo.admin.Dic;
import com.baomidou.mybatisplus.extension.service.IService;
import com.main.pojo.admin.User;

import java.util.Map;

/**
 * <p>
 * 通用字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface DicService extends IService<Dic> {

    ResponseVO<String> save(JSONObject obj);

    ResponseVO<Map<String, Object>> getTreeData(String parentId, User user);


    ResponseVO<String> delete(String oid, String parentId);

    boolean updateLeaf(String oid, int isLeaf);

    String getOid(String parentId);
}
