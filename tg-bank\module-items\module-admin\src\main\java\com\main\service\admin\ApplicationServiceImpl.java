package com.main.service.admin;

import com.main.pojo.admin.ApplicationInfo;
import com.main.service.inf.ApplicationService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ApplicationServiceImpl implements ApplicationService {
	@Override
	public ApplicationInfo getApplication(String appid) {
		//TODO CACHED BY REDIS
		ApplicationInfo application = new ApplicationInfo();
		application.setAppId("1A3VL0KVK0000B020A0A0000CC3F48AD");
		application.setAppSecret("1A3VL0KVE0010B010A0A0000277BDC91");
		application.setAppName("模拟应用返回数据");
		return application;
	}
}
