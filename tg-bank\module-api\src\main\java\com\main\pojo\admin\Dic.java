package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通用字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_dic")
public class Dic implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId
    private String oid;

    private String name;

    private String icon;

    private String val;

    private String extra;

    private String parentId;

    private Integer isLeaf;

    private Integer isStop;

}
