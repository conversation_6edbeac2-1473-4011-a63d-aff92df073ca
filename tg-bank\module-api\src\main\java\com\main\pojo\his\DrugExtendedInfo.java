package com.main.pojo.his;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 药品扩展信息实体类
 * <AUTHOR>
 */
@Data
@TableName("his_drug_extended_info")
public class DrugExtendedInfo {
    /**
     * 诊所ID，联合主键
     */
    private String companyId;
    
    /**
     * 库房ID，联合主键
     */
    private String warehouseId;
    
    /**
     * 药品ID，联合主键
     */
    @TableId
    private String oid;
    
    /**
     * 处方类型
     */
    private String prescriptionType;
    
    /**
     * 特殊药品标识，多个标识用逗号分隔
     */
    private String specialDrug;
    
    /**
     * 追溯码
     */
    private String traceCode;
    
    /**
     * 上市许可持有人
     */
    private String licenseHolder;
    
    /**
     * 是否基药，Y/N
     */
    private String isBasicDrug;
    
    /**
     * 药品标签，多个标签用逗号分隔
     */
    private String drugTags;
    
    /**
     * 储存条件，多个条件用逗号分隔
     */
    private String storageCondition;
    
    /**
     * 养护类型
     */
    private String maintenanceType;
    
    /**
     * 保质期（月）
     */
    private Integer shelfLife;
    
    /**
     * 有效期（月）
     */
    private Integer validityPeriod;
    
    /**
     * 有效期单位
     */
    private String validityUnit;
    
    /**
     * 批文有效期，格式：yyyy-MM-dd
     */
    private String approvalValidDate;
    
    /**
     * 货位号
     */
    private String locationNo;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 抗菌药物类型
     */
    private String antibacterialType;
    
    /**
     * 抗菌药物等级，1-3级
     */
    private Integer antibacterialLevel;
    
    /**
     * 抗菌药物类别，多个类别用逗号分隔
     */
    private String antibacterialCategories;
    
    /**
     * 抗生素用量
     */
    private Double antibioticDosage;
    
    /**
     * 抗生素单位
     */
    private String antibioticUnit;

    /**
     * 医疗器械分类
     */
    private String equipmentClass;
} 