package com.main.myenum;

import com.main.util.DateUtil;
import com.main.util.SMUtil.ByteUtil;
import com.main.util.SMUtil.SM4;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ActuatorSM4Utils {
    private static final String iv = "CFScD1fW6cXh9SbS";
    private static final String secret = "QQF9U9wHFQMfs2Y8";

    private static final int validTime = 10;

    public ActuatorSM4Utils() {
    }

    public static String encryptData_ECB(String plainText, String secretKey, boolean isHex) {
        try {
            byte[] keyBytes;
            if (isHex) {
                keyBytes = ByteUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            long[] sk = new long[32];
            sm4.sm4_setkey_enc(sk, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(true, SM4.SM4_ENCRYPT, sk, plainText.getBytes("GBK"));
            String cipherText = new BASE64Encoder().encode(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String decryptData_ECB(String cipherText, String secretKey, boolean isHex) {
        try {
            byte[] keyBytes;
            if (isHex) {
                keyBytes = ByteUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            long[] sk = new long[32];
            sm4.sm4_setkey_dec(sk, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(true, SM4.SM4_DECRYPT, sk, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String encryptData_CBC(String chipherText) {
        return ActuatorSM4Utils.encryptData_CBC(chipherText, secret, iv, false);
    }

    public static String encryptData_CBC(String plainText, String secretKey, String iv, boolean isHex) {
        try {
            byte[] keyBytes;
            byte[] ivBytes;
            if (isHex) {
                keyBytes = ByteUtil.hexStringToBytes(secretKey);
                ivBytes = ByteUtil.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4 sm4 = new SM4();
            long[] sk = new long[32];
            sm4.sm4_setkey_enc(sk, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_cbc(true, SM4.SM4_ENCRYPT, sk, ivBytes, plainText.getBytes("GBK"));
            String cipherText = new BASE64Encoder().encode(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getAuth() {
        //对上密码还要时间间隔15秒内，才可以正常访问。
        try {
            return URLEncoder.encode(encryptData_CBC("HOYO-MiX-WaterDragon&" + DateUtil.getMyTime()), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean validAuth(String auth, int invalidTimeSeconds) {
        try {
            auth = URLDecoder.decode(auth, "UTF-8");
            String plain = decryptData_CBC(auth);
            String[] strs = plain.split("&");
            if(invalidTimeSeconds == 0) {
                invalidTimeSeconds = 60;
            }
            if ("HOYO-MiX-WaterDragon".equals(strs[0]) && DateUtil.validMyTime(strs[1], DateUtil.getMyTime(), invalidTimeSeconds)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public static String decryptData_CBC(String chipherText) {
        return ActuatorSM4Utils.decryptData_CBC(chipherText, secret, iv, false);
    }

    public static String decryptData_CBC(String cipherText, String secretKey, String iv, boolean isHex) {
        try {
            byte[] keyBytes;
            byte[] ivBytes;
            if (isHex) {
                keyBytes = ByteUtil.hexStringToBytes(secretKey);
                ivBytes = ByteUtil.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4 sm4 = new SM4();
            long[] sk = new long[32];
            sm4.sm4_setkey_dec(sk, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(true, SM4.SM4_DECRYPT, sk, ivBytes, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) throws IOException {
        String plainText = "你是哪块小饼干你是哪块小饼干";
        String enc = ActuatorSM4Utils.encryptData_CBC(plainText);
        System.out.println(enc);
        System.out.println(ActuatorSM4Utils.decryptData_CBC(enc));
    }
}

