package com.main.service.inf;


import com.main.bean.vo.ResponseVO;

import java.net.MalformedURLException;
import java.util.Map;

/**
 * <AUTHOR>
 * 2021/10/11 15:29
 */
public interface SmsService {
	/**
	 * 发送单条短信
	 *
	 * @param phoneNo 手机号
	 * @param code    验证码
	 * @return
	 */
	ResponseVO<Map<String, Object>> sendOneMessage(long phoneNo, String code) throws MalformedURLException, Exception;

	/**
	 * 发送单条短信
	 *
	 * @param phoneNo 手机号
	 * @param code    验证码
	 * @param type    操作类型  0 登录  1 修改密码
	 * @return
	 */
	ResponseVO<Map<String, Object>> sendOneMessage(long phoneNo, String code, String type) throws MalformedURLException, Exception;

	/**
	 * 生成验证码
	 *
	 * @param length 验证码位数
	 * @return
	 */
	String createVcode(int length);

}
