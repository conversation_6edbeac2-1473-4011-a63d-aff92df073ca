# WebSocket 消息推送修复文档

## 问题描述

domino-framework 模块中的 WebSocket 消息推送功能存在问题：在第3步"后端查找用户B的WebSocket连接 → 推送消息给用户B"中，系统无法正确找到目标用户的 WebSocket 连接。

### 根本原因

1. **WebSocket连接管理使用token作为key**：
   ```java
   // QhWebSocketHandler.java
   this.id = loginUser.getToken();
   webSocketMap.put(this.id, this);  // key是token
   ```

2. **消息推送时直接使用用户ID**：
   ```java
   // AsyncMessageProcessor.java (修复前)
   String targetUserId = message.getReceiveTarget();  // 这是用户ID
   webSocketHandler.sendMessage(targetUserId, message);  // 但查找需要token
   ```

3. **缺少用户ID到token的映射机制**：
   - tg-bank 模块有 `RedisSrv.getTokenByOid(String oid)` 方法
   - domino-framework 模块缺少类似的反向查找功能

## 修复方案

### 1. 添加缓存常量

在 `CacheConstants.java` 中添加用户ID到token映射的常量：

```java
/**
 * 用户ID到token映射 redis key
 */
public static final String USER_TOKEN_KEY = "user_tokens:";
```

### 2. 扩展TokenService功能

在 `TokenService.java` 中添加用户ID到token的映射管理：

```java
/**
 * 根据用户ID获取token
 */
public String getTokenByUserId(String userId) {
    if (StringUtils.isNotEmpty(userId)) {
        String userTokenKey = getUserTokenKey(userId);
        return redisCache.getCacheObject(userTokenKey);
    }
    return null;
}

private String getUserTokenKey(String userId) {
    return CacheConstants.USER_TOKEN_KEY + userId;
}
```

### 3. 维护映射关系

在token创建、刷新、删除时维护用户ID到token的映射：

```java
// 创建token时处理重复登录
public String createToken(LoginUser loginUser) {
    // 处理用户重复登录：删除旧的token映射
    if (loginUser.getUserId() != null) {
        String oldToken = getTokenByUserId(loginUser.getUserId().toString());
        if (StringUtils.isNotEmpty(oldToken) && !oldToken.equals(token)) {
            // 删除旧的token缓存
            String oldUserKey = getTokenKey(oldToken);
            redisCache.deleteObject(oldUserKey);
        }
    }
    // ...
}

// 刷新token时维护映射
public void refreshToken(LoginUser loginUser) {
    // 维护用户ID到token的映射关系
    if (loginUser.getUserId() != null) {
        String userTokenKey = getUserTokenKey(loginUser.getUserId().toString());
        redisCache.setCacheObject(userTokenKey, loginUser.getToken(), expireTime, TimeUnit.MINUTES);
    }
    // ...
}
```

### 4. 修复消息推送逻辑

在 `AsyncMessageProcessor.java` 中修复消息推送逻辑：

```java
@Async
public void pushChatMessage(SysMessage message) {
    if (ChatConstants.RECEIVE_SCOPE_USER.equals(message.getReceiveScope())) {
        String targetUserId = message.getReceiveTarget();
        
        // 根据用户ID查找对应的token
        String token = tokenService.getTokenByUserId(targetUserId);
        if (StringUtils.isNotEmpty(token)) {
            webSocketHandler.sendMessage(token, message);
            log.info("聊天消息推送成功，目标用户: {}, token: {}", targetUserId, token);
        } else {
            log.warn("用户 {} 未在线或token不存在，无法推送聊天消息", targetUserId);
        }
    }
}
```

### 5. 完善机构消息推送

为机构消息推送添加正确的用户查找逻辑：

```java
case ChatConstants.RECEIVE_SCOPE_ORG:
case ChatConstants.RECEIVE_SCOPE_CURRORG:
    // 机构用户 - 根据机构ID获取用户列表进行推送
    String orgIds = message.getReceiveTarget();
    if (StringUtils.isNotEmpty(orgIds)) {
        List<SysUser> orgUsers = userService.getOrgOnlineUser(orgIds);
        
        for (SysUser user : orgUsers) {
            String userToken = tokenService.getTokenByUserId(user.getUserId().toString());
            if (StringUtils.isNotEmpty(userToken)) {
                webSocketHandler.sendMessage(userToken, message);
            }
        }
    }
    break;
```

## 修复效果

修复后的消息推送流程：

1. **用户A发送消息** → 调用API
2. **后端收到API请求** → 保存消息到数据库
3. **后端查找用户B的WebSocket连接** → 
   - 通过 `tokenService.getTokenByUserId(targetUserId)` 获取用户B的token
   - 使用token在 `webSocketMap` 中查找WebSocket连接
   - 推送消息给用户B
4. **用户B的WebSocket接收到消息** → 显示在界面

## 测试验证

创建了 `TokenServiceTest.java` 来验证：
- 用户ID到token映射功能
- 用户重复登录时的token更新
- token删除时的映射清理

## 兼容性

此修复完全向后兼容，不会影响现有功能，只是增强了消息推送的可靠性。
