package com.domino.common.qh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 试卷导出请求DTO
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
public class QhPaperExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试卷ID
     */
    @NotBlank(message = "试卷ID不能为空")
    private String paperId;

    /**
     * 导出文件类型：PDF、WORD
     */
    @NotBlank(message = "导出类型不能为空")
    private String exportType;

    /**
     * 内容选项
     */
    @NotNull(message = "内容选项不能为空")
    private ContentOptions contentOptions;

    /**
     * 格式选项
     */
    private FormatOptions formatOptions;

    /**
     * 试卷标识（ZJ-组卷，其他-原生试卷）
     */
    private String flag;

    /**
     * 内容选项
     */
    @Data
    public static class ContentOptions implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 是否包含试卷题目
         */
        private Boolean includeQuestions = true;

        /**
         * 是否包含答案
         */
        private Boolean includeAnswers = false;

        /**
         * 是否包含解析
         */
        private Boolean includeAnalysis = false;
    }

    /**
     * 格式选项
     */
    @Data
    public static class FormatOptions implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 页面大小：A4、A3、Letter
         */
        private String pageSize = "A4";

        /**
         * 字体大小
         */
        private Integer fontSize = 12;

        /**
         * 是否显示题目序号
         */
        private Boolean showQuestionNumbers = true;

        /**
         * 是否显示分数
         */
        private Boolean showScores = true;

        /**
         * 页边距（毫米）
         */
        private Integer margin = 20;

        /**
         * 行间距
         */
        private Double lineSpacing = 1.5;
    }
}
