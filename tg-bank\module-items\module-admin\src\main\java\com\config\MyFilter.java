package com.config;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import java.io.IOException;

@WebFilter(filterName="myFilter",urlPatterns="/*")
public class MyFilter implements Filter {


    @Override
    public void destroy() {
        System.out.println("过滤器销毁");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,FilterChain chain) throws IOException, ServletException {
        //TODO 拦截器，自行添加业务代码
        chain.  doFilter(request, response);
    }

    @Override
    public void init(FilterConfig config) throws ServletException {}

}
