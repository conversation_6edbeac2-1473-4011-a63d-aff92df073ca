<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="create_patient_table" author="system">
        <createTable tableName="his_patient" remarks="患者信息表">
            <column name="oid" type="varchar(50)" remarks="主键ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(50)" remarks="患者姓名">
                <constraints nullable="false"/>
            </column>
            <column name="gender" type="varchar(10)" remarks="性别">
                <constraints nullable="false"/>
            </column>
            <column name="age_years" type="int" remarks="年龄-年"/>
            <column name="age_months" type="int" remarks="年龄-月"/>
            <column name="age_days" type="int" remarks="年龄-天"/>
            <column name="mobile" type="varchar(20)" remarks="手机号码"/>
            <column name="birthday" type="date" remarks="出生日期"/>
            <column name="source" type="varchar(50)" remarks="患者来源"/>
            <column name="id_card" type="varchar(18)" remarks="身份证号"/>
            <column name="marital_status" type="varchar(20)" remarks="婚姻状况"/>
            <column name="weight" type="decimal(5,2)" remarks="体重(kg)"/>
            <column name="province" type="varchar(50)" remarks="省份"/>
            <column name="city" type="varchar(50)" remarks="城市"/>
            <column name="address" type="varchar(200)" remarks="详细地址"/>
            <column name="occupation" type="varchar(50)" remarks="职业"/>
            <column name="file_no" type="varchar(50)" remarks="档案号"/>
            <column name="medical_history" type="text" remarks="既往病史"/>
            <column name="allergy_history" type="text" remarks="过敏史"/>
            <column name="ethnicity" type="varchar(50)" remarks="民族"/>
            <column name="remarks" type="text" remarks="备注"/>
            <column name="visit_reason" type="text" remarks="就诊原因"/>
            <column name="create_time" type="datetime" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_time" type="datetime" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
            <column name="is_deleted" type="tinyint" defaultValue="0" remarks="是否删除：0-未删除，1-已删除"/>
            <column name="org_id" type="varchar(50)" remarks="所属机构ID">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="system" id="his_patient_upt_241206">
        <addColumn tableName="his_patient">
            <column name="is_vip" type="int" defaultValue="0" remarks="是否VIP" />
        </addColumn>
    </changeSet>
</databaseChangeLog> 