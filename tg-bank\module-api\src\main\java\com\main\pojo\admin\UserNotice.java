package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_user_notice")
public class UserNotice implements Serializable {

    private static final long serialVersionUID=1L;

    private String userOid;

    private String noticeOid;

    /**
     * 0 未读  1 已读
     */
    private Integer isRead;


}
