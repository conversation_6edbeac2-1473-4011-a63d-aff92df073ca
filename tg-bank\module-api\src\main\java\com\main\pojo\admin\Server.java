package com.main.pojo.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 云资源的服务器信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_server")
public class Server implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId
    private String oid;

    /**
     * 归属云
     */
    private String cloudOid;

    /**
     * 服务器别称
     */
    private String name;

    /**
     * 内网ip
     */
    private String innerIp;

    /**
     * ip
     */
    private String sshIp;

    /**
     * ssh 端口
     */
    private String sshPort;

    /**
     * ssh 账号
     */
    private String sshUser;

    /**
     * ssh 密码
     */
    private String sshPass;

    /**
     * 创建人id
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    @TableField(exist = false)
    private String createUserName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    @TableField(exist = false)
    private String updateUserName;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 是否停用
     */
    private Integer isStop;

}
