package com.main.service.admin;

import com.common.Global;
import com.main.service.inf.InitInfoYamlSrv;
import com.main.util.CUtil;
import com.util.YamlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 2021/11/15 14:32
 */

@Service
@Slf4j
public class InitInfoYamlSrvImpl implements InitInfoYamlSrv {
	private final String yamlPath = YamlUtil.getClassResources() + "/config/systemInfoParam.yml";
	private final File yamlFile = new File(yamlPath);

	@Override
	public boolean init() {
		try {
			boolean first = false;
			if (!yamlFile.exists()) {
				first = yamlFile.createNewFile();
			}
			//判断是否可以初始化
			if (first || this.canInit()) {
				this.setCanInit(true);
			}
		} catch (IOException e) {
			e.printStackTrace();
			log.error("判断系统初始化失败：{}", e.getMessage());
			return false;
		}
		return true;
	}

	@Override
	public boolean canInit() {
		Yaml yaml = new Yaml();
		Map<String, String> map = null;
		try {
			map = yaml.loadAs(new FileInputStream(yamlFile), Map.class);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			log.error("获取初始化配置信息失败，{}", e.getMessage());
		}
		if (map == null) {
			map = new HashMap<>();
		}
		String initFlag = CUtil.nullToZero(map.get("initFlag"));
		return initFlag.equals("0");
	}

	@Override
	public boolean setCanInit(boolean bool) {

		log.info("查找配置文件信息，文件路径：{}", yamlPath);
		Yaml yaml = new Yaml();
		try {
			Map<String, String> map = (Map<String, String>) yaml.loadAs(new FileInputStream(yamlFile), Map.class);
			if (map == null) {
				map = new HashMap<>();
			}
			if (bool) {
				String password = null;
				if(CUtil.isEmpty(map.get("password"))) {
					password = Global.createUUID();
				}else {
					password = map.get("password");
				}
				map.put("initFlag", "0");
				map.put("password", password);
				log.info("系统尚未初始化数据，请访问 /init 路径初始化。");
				log.warn("-----------------------------------");
				log.warn("您的初始化密码为：[ {} ]", password);
				log.warn("-----------------------------------");
			} else {
				map.put("initFlag", "1");
				log.info("系统已经成功关闭初始化，如需要初始化请修改配置文件：systemInfoParam.yml");
			}

			yaml.dump(map, new FileWriter(yamlFile));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("设置初始化配置信息异常：{}", e.getMessage());
			return false;
		}
		return true;
	}

	@Override
	public boolean validInitPwd(String pwd) {
		Yaml yaml = new Yaml();
		Map<String, String> map = null;
		try {
			map = yaml.loadAs(new FileInputStream(yamlFile), Map.class);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			log.error("获取初始化配置信息失败，{}", e.getMessage());
		}
		if (map == null) {
			map = new HashMap<>();
		}
		String password = CUtil.nullToZero(map.get("password"));
		return password.equals(pwd);
	}

	@Override
	public boolean setParam(String key, String value) {
		log.info("查找配置文件信息，文件路径：{}", yamlPath);
		Yaml yaml = new Yaml();
		try {
			Map<String, String> map = (Map<String, String>) yaml.loadAs(new FileInputStream(yamlFile), Map.class);
			if (map == null) {
				map = new HashMap<>();
			}
			map.put(key,value);
			yaml.dump(map, new FileWriter(yamlFile));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("设置初始化配置信息异常：{}", e.getMessage());
			return false;
		}
		return true;
	}

	@Override
	public String getParam(String key) {
		Yaml yaml = new Yaml();
		Map<String, String> map = null;
		try {
			map = yaml.loadAs(new FileInputStream(yamlFile), Map.class);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			log.error("获取初始化配置信息失败，{}", e.getMessage());
		}
		if (map == null) {
			map = new HashMap<>();
		}
		return map.get(key);
	}
}
