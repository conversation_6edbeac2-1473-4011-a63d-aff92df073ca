package com.main.handler.admin;

import com.alibaba.fastjson.JSONObject;
import com.common.Global;
import com.main.bean.vo.ResponseVO;
import com.main.myenum.HandlerType;
import com.main.service.inf.InitInfoYamlSrv;
import com.main.service.admin.SysInitService;
import com.main.util.CUtil;
import com.main.vo.InitReqVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@Slf4j
public class SysInitHandler {

    @Resource
    private InitInfoYamlSrv initInfoYamlSrv;

    @Resource
    private SysInitService sysInitService;

    /**
     * 显示初始化系统的配置页面
     */
    @RequestMapping("/init")
    @ResponseBody
    public ResponseVO<String> init() {
        ResponseVO<String> response = new ResponseVO();
        //判断能否初始化
        if (initInfoYamlSrv.canInit()) {
            String password = initInfoYamlSrv.getParam("password");
            if (CUtil.isEmpty(password)) {
                password = Global.createUUID();
                initInfoYamlSrv.setParam("password", password);
            }
            log.info("您的初始化密码为：[ {} ]", password);
            return response;
        } else {
            response.setRetInfo(HandlerType.SYSTEM_ERROR);
            response.setMsg("系统已经初始化，无法进行初始化操作！");
            return response;
        }
    }


    /**
     * 初始化系统数据
     */
    @RequestMapping("/init/do")
    @ResponseBody
    public ResponseVO<String> doInit(@RequestBody InitReqVO initReqVO) {
        log.info(JSONObject.toJSONString(initReqVO));
        Assert.assertTrue("系统已经初始化，无法进行初始化操作！", initInfoYamlSrv.canInit());
        System.out.println(initInfoYamlSrv.getParam("password") + "," + initReqVO.getInitPwd());
        Assert.assertEquals("输入的初始化密码不对，请您核实后重新输入！请根据表格下方提示获取初始密码！",
                initInfoYamlSrv.getParam("password"), initReqVO.getInitPwd());
        Assert.assertEquals("两次输入密码不一致请重新输入", initReqVO.getPword(), initReqVO.getRepword());
        Assert.assertTrue("系统已完成初始化设置，暂无法初始化，请联系管理员设置对应参数！", initInfoYamlSrv.canInit());
        return sysInitService.init(initReqVO);
    }

}
