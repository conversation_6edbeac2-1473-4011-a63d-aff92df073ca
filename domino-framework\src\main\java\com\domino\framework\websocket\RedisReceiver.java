package com.domino.framework.websocket;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.domino.common.constant.ChatConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 在集群中，用户的 WebSocket 连接是随机分布在不同的应用服务器（节点）上的，
 * 而任何一台服务器都无法直接知道其他服务器上维护了哪些连接
 * 消息监听对象，接收订阅消息
 */
@Component
@Slf4j
public class RedisReceiver implements MessageListener {

    @Resource
    private QhWebSocketHandler webSocketServer;
 
 
    /**
     * 处理接收到的订阅消息
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());// 订阅的频道名称
        String msg = "";
        log.debug("收到Redis消息 - 频道: {}, 期望频道: {}", channel, ChatConstants.REDIS_CHANNEL);

        try {
            msg = new String(message.getBody(), StandardCharsets.UTF_8);//注意与发布消息编码一致，否则会乱码
            log.debug("消息内容: {}", msg);

            if (!StrUtil.isEmpty(msg)){
                if (ChatConstants.REDIS_CHANNEL.equals(channel)){
                    JSONObject jsonObject = JSON.parseObject(msg);
                    String key = jsonObject.get(ChatConstants.REDIS_MESSAGE_KEY).toString();
                    String value = jsonObject.get(ChatConstants.REDIS_MESSAGE_VALUE).toString();

                    webSocketServer.sendMessageToClient(key, value);
                }else{
                    log.warn("频道不匹配，跳过处理。接收频道: {}, 期望频道: {}", channel, ChatConstants.REDIS_CHANNEL);
                }

            }else{
                log.info("消息内容为空，不处理。");
            }
        }
        catch (Exception e) {
            log.error("处理Redis消息异常，频道: {}, 消息: {}, 错误: {}", channel, msg, e.getMessage(), e);
        }
    }
}